import type { Config } from "tailwindcss";

const config: Config = {
	darkMode: "class",
	content: [
		"./src/components/**/*.{js,ts,jsx,tsx,mdx}",
		"./src/app/**/*.{js,ts,jsx,tsx,mdx}",
	],
	theme: {
		extend: {
			height: {
				"screen-minus-navbar": "calc(100vh - 4.5rem)",
			},
			colors: {
				"allo-destructive": "#d1001f",
				"allo-white": "#FFFFFF",
				"allo-dark-blue": "#2C294C",
				"allo-blue": "#3023AE",
				"allo-pink": "#C024B6",
				"allo-dark-grey": "#333333",
				"allo-md-grey": "#636363",
				"allo-light-grey": "#D9D9D9",
				"allo-bg-2": "#F7F8F9",
				"allo-line": "#F1F2F4",
			},
			fontFamily: {
				primary: [
					"Inter",
					...require("tailwindcss/defaultTheme").fontFamily.serif,
				],
			},
			spacing: {
				"54": "13.5rem",
			},
			backgroundImage: {
				"allo-grad": "linear-gradient(90deg, #3023AE, #C024B6)",
				"allo-grad-2": "linear-gradient(18015deg, #3023AE, #C024B6)",
			},
			fontSize: {
				"t-15": "0.937rem",
			},
			boxShadow: {
				custom:
					"0 0 1px 0 rgba(8, 11, 14, 0.06), 0 16px 16px -1px rgba(8, 11, 14, 0.1)",
			},
			borderRadius: {
				lg: "var(--radius)",
				md: "calc(var(--radius) - 2px)",
				sm: "calc(var(--radius) - 4px)",
			},
			keyframes: {
				"accordion-down": {
					from: {
						height: "0",
					},
					to: {
						height: "var(--radix-accordion-content-height)",
					},
				},
				"accordion-up": {
					from: {
						height: "var(--radix-accordion-content-height)",
					},
					to: {
						height: "0",
					},
				},
			},
			animation: {
				"accordion-down": "accordion-down 0.2s ease-out",
				"accordion-up": "accordion-up 0.2s ease-out",
			},
		},
	},
	plugins: [require("tailwindcss-animate")],
};
export default config;
