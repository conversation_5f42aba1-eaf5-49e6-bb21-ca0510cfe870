---
# Settings for Backend (on Cloud Run).
# See https://firebase.google.com/docs/app-hosting/configure#cloud-run
runConfig:
  minInstances: 0
  # maxInstances: 100
  # concurrency: 80
  # cpu: 1
  # memoryMiB: 512
env:
  - variable: NEXT_PUBLIC_STRIPE_SECRET_KEY
    secret: NEXT_PUBLIC_STRIPE_SECRET_KEY
  - variable: NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
    secret: NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
  - variable: NEXT_PUBLIC_STRIPE_TAX_RATE_ID
    secret: NEXT_PUBLIC_STRIPE_TAX_RATE_ID
  - variable: NEXT_PUBLIC_STRIPE_WEBHOOK_SECRET_LIVE
    secret: NEXT_PUBLIC_STRIPE_WEBHOOK_SECRET_LIVE
  - variable: MAILGUN_LIVE_API_KEY
    secret: MAILGUN_LIVE_API_KEY
  - variable: MAILGUN_LIVE_DOMAIN
    secret: MA<PERSON><PERSON><PERSON>_LIVE_DOMAIN
  - variable: GOO<PERSON>LE_CLIENT_ID
    secret: GOO<PERSON><PERSON>_CLIENT_ID
  - variable: GOOGLE_CLIENT_SECRET
    secret: GOOGLE_CLIENT_SECRET
  - variable: GOOGLE_REFRESH_TOKEN
    secret: GOOGLE_REFRESH_TOKEN
  - variable: GOOGLE_REDIRECT_URI
    secret: GOOGLE_REDIRECT_URI
# Environment variables and secrets.
# env:
# Configure environment variables.
# See https://firebase.google.com/docs/app-hosting/configure#user-defined-environment
# - variable: MESSAGE
#   value: Hello world!
#   availability:
#     - BUILD
#     - RUNTIME

# Grant access to secrets in Cloud Secret Manager.
# See https://firebase.google.com/docs/app-hosting/configure#secret-parameters
# - variable: MY_SECRET
#   secret: mySecretRef
