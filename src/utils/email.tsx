// utils/email.ts
import formData from "form-data";
import Mailgun from "mailgun.js";

const mailgun = new Mailgun(formData);
const mg = mailgun.client({
	username: "api",
	key: process.env.MAILGUN_LIVE_API_KEY as string,
	url: "https://api.mailgun.net",
});

interface EmailParams {
	to: string | string[];
	from: string;
	subject: string;
	message: string;
}

export async function sendEmail({ to, from, subject, message }: EmailParams) {
	try {
		const result = await mg.messages.create("mail.allocvc.fr", {
			from,
			to,
			subject,
			text: message,
		});

		console.log("✅ Email envoyé avec succès !");
		return result;
	} catch (error) {
		console.error("❌ Erreur d'envoi de l'email :", error);
		throw error;
	}
}
