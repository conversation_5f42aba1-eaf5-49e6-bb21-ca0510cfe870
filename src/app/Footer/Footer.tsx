import LogoImage from "/public/uploads/logos/AlloCVC_text_logo_white.webp";
import Image from "next/image";
import Link from "next/link";
import ClientSignInComponent from "./ClientSignInComponent";
import PolicyDialog from "../TermsAndConditions/PolitiqueDeConfidentialite";
import MentionsLegalesDialog from "../TermsAndConditions/MentionsLegales";

export default function Footer() {
	return (
		<section className="footerSection bg-allo-grad flex min-h-64 w-full flex-col items-center justify-center">
			<div className="m-5 flex h-5/6 w-11/12 max-w-screen-2xl flex-col items-center justify-between gap-10 lg:w-4/5 lg:flex-row lg:items-start lg:gap-10 lg:py-10">
				<div className="flex basis-1/2 flex-col items-center gap-2 lg:items-start lg:gap-6">
					<Link href="/">
						<Image
							src={LogoImage.src}
							alt="alloCVC logo white version"
							width={150}
							height={75}
						></Image>
					</Link>
					<div className="flex gap-4 sm:gap-8">
						<PolicyDialog>
							<p className="font-primary text-allo-white cursor-pointer text-center text-sm font-normal md:text-start">
								Politique de Confidentialité
							</p>
						</PolicyDialog>
						<MentionsLegalesDialog>
							<p className="font-primary text-allo-white cursor-pointer text-center text-sm font-normal md:text-start">
								Mentions légales
							</p>
						</MentionsLegalesDialog>
					</div>
				</div>
				<div className="flex basis-1/2 flex-col-reverse items-center justify-between gap-10 lg:flex-col lg:items-end">
					<div className="mt-2 flex lg:mt-5">
						<ClientSignInComponent />
					</div>
					<div className="flex gap-8">
						<Link
							className="font-primary text-allo-white cursor-pointer text-sm font-normal"
							href="/#heroSection"
						>
							Accueil
						</Link>
						<Link
							className="font-primary text-allo-white cursor-pointer text-sm font-normal"
							href="/#aboutUsSection"
						>
							L&apos;équipe
						</Link>
						<Link
							className="font-primary text-allo-white cursor-pointer text-sm font-normal"
							href="/#faqSection"
						>
							FAQ
						</Link>
						<Link
							className="font-primary text-allo-white cursor-pointer text-sm font-normal"
							href="/#contactFormSection"
						>
							Contact
						</Link>
					</div>
				</div>
			</div>
			<div className="m-5 flex h-1/6 w-11/12 max-w-screen-2xl items-center justify-center lg:m-5 lg:w-4/5 lg:items-start">
				<p className="font-primary text-allo-white text-xs font-thin">
					© 2025 Tous droits réservés
				</p>
			</div>
		</section>
	);
}
