import { Progress } from "@/components/ui/progress";

type TimeLineProps = {
	currentStage: number;
	goToStage: (stage: number) => void;
	stages: string[];
};
export default function Timeline({
	currentStage,
	goToStage,
	stages,
}: TimeLineProps) {
	const handleStageNavigation = (index: number) => {
		if (index + 1 <= currentStage) goToStage(index + 1);
	};

	return (
		<div className="timeLine hidden w-3/5 flex-col items-center min-[1280px]:flex">
			<Progress
				value={currentStage > 6 ? 100 : currentStage * 16.6}
				className="z-0 -mb-2"
			/>
			<div className="timeLineMilestones z-10 flex w-5/6 justify-between">
				{stages.map((stage, index) => (
					<div
						key={index}
						className="flex w-10 flex-col items-center justify-center gap-2"
					>
						<div
							className={`size-3 ${index + 1 <= currentStage ? "bg-allo-pink" : "bg-gray-300"} rounded`}
							onClick={() => handleStageNavigation(index)}
						/>
						<p
							className="text-allo-dark-grey cursor-pointer text-center text-xs font-medium hover:font-bold"
							onClick={() => handleStageNavigation(index)}
						>
							{stage}
						</p>
					</div>
				))}
			</div>
		</div>
	);
}
