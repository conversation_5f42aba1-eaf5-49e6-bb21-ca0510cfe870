import { ChevronLeft } from "lucide-react";

export default function BackButton({
	currentStage,
	goToStage,
	stages,
}: {
	currentStage: number;
	goToStage: (stage: number) => void;
	stages: string[];
}) {
	const handleStageNavigation = () => {
		goToStage(currentStage - 1);
	};

	const previousStage = currentStage > 1 ? stages[currentStage - 2] : "";

	return (
		<div
			className={`mb-2 w-full items-center justify-start ${currentStage > 1 ? "flex" : "hidden"}`}
		>
			<ChevronLeft
				className="text-allo-md-grey cursor-pointer"
				onClick={handleStageNavigation}
			/>
			<p
				className="text-allo-md-grey cursor-pointer text-xs"
				onClick={handleStageNavigation}
			>
				{previousStage}
			</p>
		</div>
	);
}
