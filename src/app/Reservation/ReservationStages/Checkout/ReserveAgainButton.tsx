import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";

export default function ReserveAgainButton({
	goToStage,
}: {
	goToStage: (stage: number) => void;
}) {
	const router = useRouter();

	const handleReserveAgain = () => {
		goToStage(1);
		router.push("/#reservationSection");
	};

	return (
		<Button
			onClick={handleReserveAgain}
			className="object-fit bg-allo-dark-grey mr-5 w-fit rounded-md px-5 py-2 text-sm font-medium text-slate-50 shadow-sm"
		>
			Réserver à nouveau
		</Button>
	);
}
