import NumInput from "@/components/ui/numInput";
import Image from "next/image";

interface AcCardProps {
	imageSrc: string;
	title: string;
	type: string;
	amount: number;
	onUpdate: (type: string, amount: number) => void;
}

export default function AcCard({
	imageSrc,
	title,
	type,
	amount,
	onUpdate,
}: AcCardProps) {
	let updatedTitle;

	if (title === "Split") updatedTitle = "Murale";
	else if (title === "Extérieur") updatedTitle = "Groupe extérieur";
	else updatedTitle = title;

	return (
		<div className="box-border flex w-40 min-w-40 flex-col items-center justify-between gap-1 rounded-lg bg-white p-5 shadow-md hover:shadow-md min-[375px]:px-0 min-[375px]:py-5 sm:w-54 lg:min-w-80 lg:px-12 lg:py-10 lg:shadow">
			<div className="flex h-24 w-full overflow-hidden lg:h-40">
				<Image
					width={720}
					height={480}
					alt={`Clim ${title}`}
					src={imageSrc}
					className="w-full object-cover"
				/>
			</div>
			<div className="flex flex-col items-center">
				<h4 className="lg:text-md font-primary text-allo-dark-grey mt-2 mb-1 text-sm font-bold">
					{updatedTitle}
				</h4>
				<p className="font-primary text-allo-dark-grey mb-4 text-xs lg:text-sm">
					Type:{" "}
					<span className="font-primary text-allo-dark-grey text-xs italic lg:text-sm">
						{type}
					</span>
				</p>
				<NumInput
					minAmount={type === "exterieur" ? 1 : 0}
					maxAmount={8}
					value={amount}
					onChange={(newAmount: number) =>
						onUpdate(title == "Extérieur" ? "exterieur" : title, newAmount)
					}
				/>
			</div>
		</div>
	);
}
