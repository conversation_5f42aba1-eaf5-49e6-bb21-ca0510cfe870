import { PasswordProps } from "@/components/ui/password";
import Password from "@/components/ui/password";
import ResetPasswordDialog from "./pwdReset";

export default function PwdInput({
	password,
	onChange,
	onValidityChange,
	showRequirements,
}: PasswordProps) {
	return (
		<>
			<Password
				{...{ password, onChange, onValidityChange, showRequirements }}
			/>
			{showRequirements && (
				<ResetPasswordDialog>
					<a className="mt-2 flex cursor-pointer justify-end text-right text-xs text-blue-500">
						Mot de passe oublié
					</a>
				</ResetPasswordDialog>
			)}
		</>
	);
}
