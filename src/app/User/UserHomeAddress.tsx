"use client";

import { z } from "zod";
import { useForm } from "react-hook-form";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { zodResolver } from "@hookform/resolvers/zod";
//import { useToast } from "@/components/ui/toast/use-toast";

export const addressFormSchema = z.object({
	address_1: z.string().optional(),
	address_2: z.string().optional(),
	zip_code: z.string().optional(),
	commune: z.string().optional(),
	department: z.literal("Alpes-Maritimes"),
});

type UserFormDetails = z.infer<typeof addressFormSchema>;

export default function UserHomeAddress({
	address_1,
	address_2,
	zip_code,
	commune,
	department = "Alpes-Maritimes",
}: {
	address_1?: string;
	address_2?: string;
	zip_code?: string;
	commune?: string;
	department?: "Alpes-Maritimes";
}) {
	//const { toast } = useToast();

	const form = useForm<UserFormDetails>({
		resolver: zodResolver(addressFormSchema),
		defaultValues: {
			address_1: address_1 || "",
			address_2: address_2 || "",
			zip_code: zip_code || "",
			commune: commune || "",
			department: department || "Alpes-Maritimes",
		},
	});

	// const onSubmit = async (values: UserFormDetails) => {
	// 	const result = await UpdateUserAddress(values);
	// 	if (result?.status === "success") {
	// 		toast({
	// 			title: result.title,
	// 			description: result.description,
	// 			variant: "default",
	// 		});
	// 	} else {
	// 		toast({
	// 			title: result.title,
	// 			description: result.description,
	// 			variant: "destructive",
	// 		});
	// 	}
	// };

	return (
		<div className="mx-0.5">
			<p className="text-allo-md-grey mt-3 mb-5 text-xs">
				Nos services sont disponibles uniquement pour les résidents des{" "}
				<span className="text-allo-dark-grey font-semibold">
					Alpes-Maritimes
				</span>{" "}
				ou de <span className="text-allo-dark-grey font-semibold">Monaco</span>.
			</p>
			<Form {...form}>
				<form
					//onSubmit={form.handleSubmit(onSubmit)}
					className="space-y-5 lg:space-y-8"
				>
					<FormField
						control={form.control}
						name="address_1"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Adresse 1 *</FormLabel>
								<FormControl>
									<Input {...field} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
					<FormField
						control={form.control}
						name="address_2"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Adresse 2</FormLabel>
								<FormControl>
									<Input {...field} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
					<FormField
						control={form.control}
						name="zip_code"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Code postal *</FormLabel>
								<FormControl>
									<Input {...field} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
					<FormField
						control={form.control}
						name="commune"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Commune *</FormLabel>
								<FormControl>
									<Input {...field} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<Button className="w-full text-xs md:text-base" type="submit">
						Mettre à jour
					</Button>
				</form>
			</Form>
		</div>
	);
}
