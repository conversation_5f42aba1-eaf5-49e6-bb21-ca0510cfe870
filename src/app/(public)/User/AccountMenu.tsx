import {
	Accordion,
	AccordionContent,
	AccordionItem,
	AccordionTrigger,
} from "@/components/ui/accordion";
import { useMemo, useEffect, useState } from "react";
import { onAuthStateChanged } from "firebase/auth";
import { auth, db } from "@/lib/firebaseConfig";
import {
	doc,
	getDoc,
	collection,
	query,
	where,
	getDocs,
} from "firebase/firestore";
import { useToast } from "@/components/ui/toast/use-toast";
import UserProfileSettings from "./UserProfile";
import { useRouter } from "next/navigation";
import { DateTime } from "luxon";

export type UserProfileDetails = {
	displayName: string;
	email: string;
	phone?: string;
};

export type Intervention = {
	id: string;
	interventionDate: string;
	interventionTime: string;
	commune: string;
	communeDept: string;
	chaudiere: number;
	clim: number;
	pompe: number;
	panne: number;
	maintenance: number;
	interventionTotalPrice: number;
	paymentStatus: string;
	createdAt: Date;
	interventionNetPrice?: number;
	invoiceUrl?: string; // 🆕 Lien vers la facture
};

export default function AccountMenu() {
	const [searchTerm, setSearchTerm] = useState("");
	const [userProfileDetails, setUserProfileDetails] =
		useState<UserProfileDetails>();
	const [userInterventions, setUserInterventions] = useState<Intervention[]>(
		[],
	);
	const { toast } = useToast();
	const router = useRouter();

	function formatPrice(value: number) {
		return value.toLocaleString("fr-FR", {
			style: "currency",
			currency: "EUR",
		});
	}

	const filteredInterventions = useMemo(() => {
		return userInterventions.filter((i) => {
			const fullText = [
				i.commune,
				i.interventionDate,
				i.interventionTime,
				i.paymentStatus,
				i.chaudiere ? "chaudiere" : "",
				i.clim ? "clim" : "",
				i.pompe ? "pompe" : "",
				i.panne ? "panne" : "",
				i.maintenance ? "maintenance" : "",
			]
				.join(" ")
				.toLowerCase();

			return fullText.includes(searchTerm.toLowerCase());
		});
	}, [searchTerm, userInterventions]);

	useEffect(() => {
		const unsubscribe = onAuthStateChanged(auth, async (user) => {
			if (user && user.uid) {
				try {
					const docRef = doc(db, "users", user.uid);
					const docSnap = await getDoc(docRef);

					if (docSnap.exists()) {
						const data = docSnap.data();

						setUserProfileDetails({
							displayName: data.displayName || "",
							email: data.email || "",
							phone: data.phone || "",
						});

						const interventionsQuery = query(
							collection(db, "interventions"),
							where("userId", "==", user.uid),
							where("paymentStatus", "in", ["complete", "test_complete"]),
						);
						const snapshot = await getDocs(interventionsQuery);

						const interventionsData = snapshot.docs.map((doc) => {
							const data = doc.data();
							return {
								id: doc.id,
								...data,
								createdAt:
									data.createdAt?.toDate() || DateTime.fromMillis(0).toJSDate(),
							};
						}) as Intervention[];

						const sortedInterventions = interventionsData.sort(
							(a, b) => b.createdAt.getTime() - a.createdAt.getTime(),
						);

						setUserInterventions(sortedInterventions);
					}
				} catch (error) {
					console.error("Erreur récupération utilisateur :", error);
					toast({
						title: "Erreur lors de la récupération de vos données",
						description: "Veuillez vous reconnecter ou contacter le support.",
						variant: "destructive",
					});
				}
			} else {
				router.push("/");
				toast({
					title: "Accès refusé",
					description: "Vous devez être connecté pour accéder à cette page.",
					variant: "destructive",
				});
			}
			return () => unsubscribe();
		});
	}, [router, toast]);

	return (
		<Accordion className="w-full" type="single" collapsible>
			<AccordionItem value="item-1">
				<AccordionTrigger>
					<span className="text-allo-md-grey uppercase">Profil</span>
				</AccordionTrigger>
				<AccordionContent>
					<UserProfileSettings
						userProfileDetails={userProfileDetails}
						setUserProfileDetails={setUserProfileDetails}
					/>
				</AccordionContent>
			</AccordionItem>

			<AccordionItem value="item-2">
				<AccordionTrigger>
					<span className="text-allo-md-grey uppercase">Interventions</span>
				</AccordionTrigger>
				<AccordionContent>
					{userInterventions.length === 0 ? (
						<p className="text-sm text-gray-500">
							Aucune intervention trouvée.
						</p>
					) : (
						<div className="space-y-4">
							<input
								type="text"
								placeholder="Rechercher une intervention..."
								value={searchTerm}
								onChange={(e) => setSearchTerm(e.target.value)}
								className="focus:ring-allo-blue w-full rounded-md border px-3 py-2 text-sm text-gray-700 shadow-sm focus:ring-2 focus:outline-none"
							/>

							{filteredInterventions.length === 0 ? (
								<p className="text-sm text-gray-500">
									Aucun résultat pour cette recherche.
								</p>
							) : (
								<ul className="space-y-4">
									{filteredInterventions.map((intervention) => {
										const services = [];
										if (intervention.chaudiere) services.push("Chaudière");
										if (intervention.clim) services.push("Clim");
										if (intervention.pompe) services.push("Pompe");
										if (intervention.maintenance) services.push("Maintenance");
										if (intervention.panne) services.push("Panne");

										return (
											<li
												key={intervention.id}
												className="rounded border border-gray-200 p-4 shadow-sm"
											>
												<p className="mb-1 text-xs text-gray-400">
													📅 Réservé le :{" "}
													{intervention.createdAt.toLocaleDateString("fr-FR")}
												</p>
												<p className="text-sm font-medium">
													🛠️ {intervention.interventionDate} à{" "}
													{intervention.interventionTime}
												</p>
												<p className="text-sm text-gray-600">
													📍 {intervention.commune} ({intervention.communeDept})
												</p>
												<p className="text-sm text-gray-600">
													🔧 Services : {services.join(", ")}
												</p>
												<p className="text-sm text-gray-600">
													💶 Total :{" "}
													{intervention.interventionNetPrice !== undefined
														? formatPrice(intervention.interventionNetPrice)
														: formatPrice(intervention.interventionTotalPrice)}
												</p>
												<p className="text-sm text-gray-600">
													✅ Paiement :{" "}
													{intervention.paymentStatus === "complete" ||
													intervention.paymentStatus === "test_complete"
														? "Payé"
														: "En attente"}
												</p>
												{intervention.invoiceUrl && (
													<p className="mt-1 text-sm text-blue-600 underline">
														📄{" "}
														<a
															href={intervention.invoiceUrl}
															target="_blank"
															rel="noopener noreferrer"
														>
															Télécharger la facture PDF
														</a>
													</p>
												)}
											</li>
										);
									})}
								</ul>
							)}
						</div>
					)}
				</AccordionContent>
			</AccordionItem>
		</Accordion>
	);
}
