import { auth, db } from "@/lib/firebaseConfig";
import { setDoc, doc, getDoc, serverTimestamp } from "firebase/firestore";
import { FirebaseError } from "firebase/app";

import {
	createUserWithEmailAndPassword,
	signInWithEmailAndPassword,
} from "firebase/auth";

export const ManualSignUp = async (
	email: string,
	password: string,
	displayName: string,
) => {
	try {
		const userCredential = await createUserWithEmailAndPassword(
			auth,
			email,
			password,
		);
		if (userCredential && userCredential.user.uid) {
			await setDoc(doc(db, "users", userCredential.user.uid), {
				displayName: displayName,
				email: email,
				userId: userCredential.user.uid,
				userType: "client",
				createdAt: serverTimestamp(),
			});
			return {
				title: displayName ? `"Bienvenue ${displayName}"` : "Bienvenue",
				description: "Programmer une intervention maintenant",
				variant: "default",
				status: "success",
				email: email,
				uid: userCredential.user.uid,
			};
		} else {
			return {
				title: "Erreur lors de l'inscription",
				description: "L'inscription a échoué. Veuillez réessayer",
				variant: "destructive",
				status: "failed",
				email: email,
				uid: undefined,
			};
		}
	} catch (error) {
		// Handle specific Firebase errors
		let description = "Une erreur inconnue s'est produite"; // Default error message

		if ((error as FirebaseError).code === "auth/email-already-in-use") {
			description = "Cet email est déjà utilisé. Essayez de vous connecter.";
		} else if ((error as FirebaseError).code === "auth/invalid-email") {
			description = "L'email fourni n'est pas valide. Vérifiez votre saisie.";
		} else if ((error as FirebaseError).code === "auth/weak-password") {
			description = "Le mot de passe est trop faible.";
		} else {
			// For all other errors, fallback to the error's message or the default
			description = (error as Error).message || description;
		}

		return {
			title: "Erreur lors de l'inscription",
			description: description,
			variant: "destructive",
			status: "failed",
			email: email,
			uid: undefined,
		};
	}
};

export const ManualLogIn = async (email: string, password: string) => {
	try {
		const userCredential = await signInWithEmailAndPassword(
			auth,
			email,
			password,
		);
		if (userCredential && userCredential.user.uid) {
			const docRef = doc(db, "users", userCredential.user.uid);
			const docSnap = await getDoc(docRef);

			return {
				title: docSnap?.data()?.displayName
					? `Ravi de vous revoir ${docSnap?.data()?.displayName}`
					: "Ravi de vous revoir",
				description: "Programmer une intervention maintenant",
				variant: "default",
				status: "success",
				email: email,
				uid: userCredential.user.uid,
			};
		} else {
			return {
				title: "Erreur lors de connexion",
				description: "Veuillez essayer à nouveau",
				variant: "destructive",
				status: "failed",
				email: email,
				uid: undefined,
			};
		}
	} catch (error) {
		let description = "Une erreur inconnue s'est produite"; // Default error message

		if (
			(error as FirebaseError).code === "auth/invalid-credential" ||
			(error as FirebaseError).code === "auth/wrong-password"
		) {
			description = "Email ou mot de passe invalide";
		} else if ((error as FirebaseError).code === "auth/user-not-found") {
			description = "Utilisateur non trouvé. Vérifiez votre email.";
		} else if ((error as FirebaseError).code === "auth/too-many-requests") {
			description = "Trop de tentatives infructueuses. Réessayez plus tard.";
		} else {
			// For all other errors, fallback to the error's message or default
			description = (error as Error).message || description;
		}

		return {
			title: "Erreur lors de connexion",
			description: description,
			variant: "destructive",
			status: "failed",
			email: email,
			uid: undefined,
		};
	}
};
