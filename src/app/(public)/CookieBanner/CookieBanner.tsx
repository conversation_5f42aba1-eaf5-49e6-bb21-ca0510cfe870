"use client";
import CookieConsent from "react-cookie-consent";
import CookiePreferencesModal from "./CookiePreferencesModal";
import { useState } from "react";
import <PERSON>ie from "js-cookie";
import { FaCheck, FaTimes, FaCog, FaLock } from "react-icons/fa";
import PolicyDialog from "../TermsAndConditions/PolitiqueDeConfidentialite";

export default function CookieBanner() {
	const [visible, setVisible] = useState(true);

	const [showModal, setShowModal] = useState(false);
	const handleAccept = () => {
		Cookie.set("cookieConsent", "all", { expires: 365 });
		Cookie.set("analytics", "true", { expires: 365 });
		Cookie.set("marketing", "true", { expires: 365 });
		setVisible(false); //  on la cache manuellement car j'ai fait des boutons personnalisés
	};

	const handleDecline = () => {
		Cookie.set("cookieConsent", "none", { expires: 365 });
		Cookie.set("analytics", "false", { expires: 365 });
		Cookie.set("marketing", "false", { expires: 365 });
		setVisible(false); //  on la cache manuellement (pareil qu'en haut)
	};

	return (
		<>
			{showModal && (
				<CookiePreferencesModal
					isOpen={showModal}
					onClose={() => setShowModal(false)}
				/>
			)}
			{visible && (
				<CookieConsent
					location="bottom"
					enableDeclineButton
					onAccept={handleAccept}
					onDecline={handleDecline}
					buttonText=""
					declineButtonText=""
					style={{
						background: "white",
						color: "#1f2937",
						boxShadow: "0 -2px 8px rgba(0,0,0,0.1)",
						padding: "1rem 2rem",
						fontSize: "0.875rem",
					}}
					buttonStyle={{ display: "none" }}
					declineButtonStyle={{ display: "none" }}
				>
					<div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
						<div>
							<p className="mb-1 font-semibold">Ce site utilise des cookies</p>
							<p className="text-sm">
								...et vous donne le contrôle sur ceux que vous souhaitez
								activer.
							</p>
						</div>

						<div className="flex flex-wrap items-center gap-3">
							{/*bouton Accept personnalisé */}
							<button
								onClick={handleAccept}
								className="z-[4] flex items-center gap-2 rounded bg-gray-700 px-4 py-2 text-sm text-white shadow hover:bg-gray-800"
							>
								<FaCheck /> Tout accepter
							</button>

							{/* Ton bouton Refuser personnalisé */}
							<button
								onClick={handleDecline}
								className="z-5 flex items-center gap-2 rounded border border-gray-300 bg-gray-100 px-4 py-2 text-sm text-gray-700 hover:bg-gray-200"
							>
								<FaTimes /> Tout refuser
							</button>

							{/* Personnaliser */}
							<button
								onClick={() => {
									setShowModal(true);
									setVisible(false);
								}}
								className="flex items-center gap-2 rounded border border-gray-300 px-4 py-2 text-sm hover:bg-gray-100"
							>
								<FaCog /> Personnaliser
							</button>
							<div className="mt-4 flex items-center gap-2 text-sm text-gray-500">
								<FaLock className="text-gray-400" />
								<PolicyDialog>
									<p className="z-20 cursor-pointer underline hover:text-gray-700">
										Politique de confidentialité
									</p>
								</PolicyDialog>
							</div>
						</div>
					</div>
				</CookieConsent>
			)}
		</>
	);
}
