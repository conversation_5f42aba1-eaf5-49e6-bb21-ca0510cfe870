import { AnimatedIcon } from "@/lib/animatedIconPlayer";
import SearchIcon from "/public/uploads/icons/wired-lineal-19-magnifier-zoom-search-hover-spin-stroke-bold.json";
import ClimIcon from "/public/uploads/icons/wired-lineal-821-airconditioner-hover-pinch-stroke-bold.json";
import HeatingIcon from "/public/uploads/icons/wired-lineal-1628-adjust-heating-hover-pinch-stroke-bold.json";

export default function ServicePricing() {
	return (
		<div className="my-6 flex flex-col items-center md:my-2">
			<h2 className="font-primary text-allo-md-grey text-sm font-bold uppercase sm:hidden">
				Des tarifs avantageux
			</h2>
			<div className="my-2 flex w-full flex-col items-center justify-center gap-2 sm:flex-row sm:flex-wrap sm:gap-4 lg:my-4 lg:items-start lg:justify-start">
				{/* ENTRETIEN MONOSPLIT */}
				<div className="border-allo-light-grey sm-border-y-0 flex items-stretch gap-3 rounded-xl border-0 px-5 py-3 sm:border">
					{/* Icon */}
					<div className="flex items-center justify-center md:hidden lg:flex">
						<AnimatedIcon icon={ClimIcon} shouldPlay={true} size={48} />
					</div>
					{/* Service */}
					<div className="flex items-center">
						<h4 className="font-primary text-allo-md-grey sm:text-allo-dark-grey font-medium sm:font-bold">
							Entretien
							<br />
							Monosplit
						</h4>
					</div>
					{/* Price */}
					<div className="flex flex-col justify-center">
						<p className="text-xs uppercase">À partir de</p>
						<p className="bg-allo-grad inline-block bg-clip-text text-2xl font-medium text-transparent">
							149 €
						</p>
					</div>
				</div>

				<div className="border-allo-light-grey block h-1 w-2/3 border-b sm:hidden"></div>

				{/* RECHERCHE DE PANNE */}
				<div className="border-allo-light-grey flex items-stretch gap-3 rounded-xl border-0 px-5 py-3 sm:border">
					{/* Icon */}
					<div className="flex items-center justify-center md:hidden lg:flex">
						<AnimatedIcon icon={SearchIcon} shouldPlay={true} size={40} />
					</div>
					{/* Service */}
					<div className="flex items-center">
						<h4 className="font-primary text-allo-md-grey sm:text-allo-dark-grey font-medium sm:font-bold">
							Recherche
							<br />
							de panne
						</h4>
					</div>

					{/* Price */}
					<div className="flex flex-col justify-center">
						<p className="text-xs uppercase">À partir de</p>
						<p className="bg-allo-grad inline-block bg-clip-text text-2xl font-medium text-transparent">
							119 €
						</p>
					</div>
				</div>

				<div className="border-allo-light-grey block h-1 w-2/3 border-b sm:hidden"></div>

				{/* ENTRETIEN CHAUDIERE */}
				<div className="border-allo-light-grey flex items-stretch gap-3 rounded-xl border-0 px-5 py-3 sm:border">
					{/* Icon */}
					<div className="flex items-center justify-center md:hidden lg:flex">
						<AnimatedIcon icon={HeatingIcon} shouldPlay={true} size={48} />
					</div>
					{/* Service */}
					<div className="flex items-center">
						<h4 className="font-primary text-allo-md-grey sm:text-allo-dark-grey font-medium sm:font-bold">
							Entretien
							<br />
							Chaudière
						</h4>
					</div>
					{/* Price */}
					<div className="flex flex-col justify-center">
						<p className="text-xs uppercase">À partir de</p>
						<p className="bg-allo-grad inline-block bg-clip-text text-2xl font-medium text-transparent">
							159 €
						</p>
					</div>
				</div>
			</div>
		</div>
	);
}
