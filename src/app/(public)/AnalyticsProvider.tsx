'use client';

import { useEffect } from 'react';
import <PERSON><PERSON> from 'js-cookie';
import { analytics } from '@/lib/firebaseConfig';
import { setAnalyticsCollectionEnabled } from 'firebase/analytics';

export default function AnalyticsProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    // Check if user has consented to analytics
    const analyticsEnabled = Cookie.get('analytics') === 'true';
    
    if (analytics) {
      // Enable or disable analytics based on user preference
      setAnalyticsCollectionEnabled(analytics, analyticsEnabled);
    }
  }, []);

  return <>{children}</>;
}