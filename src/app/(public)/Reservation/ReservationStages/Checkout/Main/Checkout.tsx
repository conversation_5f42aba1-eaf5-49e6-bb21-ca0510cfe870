import { useCallback, useEffect, useState } from "react";
import { loadStripe } from "@stripe/stripe-js";
import {
	EmbeddedCheckoutProvider,
	EmbeddedCheckout,
} from "@stripe/react-stripe-js";
import {
	interventionDataSchema,
	InterventionData,
} from "@/lib/stripeProductSchema";
import { db } from "@/lib/firebaseConfig";
import { doc, getDoc } from "firebase/firestore";
import FailureSection from "../Failure";
import SuccessSection from "../Success";
import { LoadingSpinner } from "@/components/ui/loadingSpinner";

const fetchInterventionData = async (docId: string) => {
	const docRef = doc(db, "interventions", docId);
	const docSnap = await getDoc(docRef);

	if (!docSnap.exists()) {
		return {
			error: `Checkout error: Document with ID ${docId} does not exist`,
			success: false,
		};
	}
	const data = { ...docSnap.data(), interventionId: docSnap.id }; // Pour récuperer l'ID de l"intervention (j'en ai besoin pour l'addresse et le webhook))
	// Doesn't work ?

	const result = interventionDataSchema.safeParse(data);

	if (!result.success) {
		return {
			error: `Checkout error: Document with ID ${docId} does not conform to InterventionData type: ${result.error}`,
			success: false,
		};
	}

	return { success: true, data: result.data };
};

type stageProps = {
	docId: string | undefined;
	goToStage: (stage: number) => void;
};

export const stripePromise = loadStripe(
	process.env.NODE_ENV === "production"
		? process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!
		: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_TEST_KEY!,
);

export default function Checkout({ docId, goToStage }: stageProps) {
	const [interventionData, setInterventionData] = useState<
		InterventionData | undefined
	>(undefined);
	const [error, setError] = useState<string | undefined>(undefined);
	const [loading, setLoading] = useState<boolean>(true);

	useEffect(() => {
		const fetchData = async () => {
			if (!docId) {
				setError("Checkout error: Intervention document ID is missing.");
				setLoading(false);
				return;
			}

			const response = await fetchInterventionData(docId);

			if (!response.success) setError(response.error ?? undefined);
			else setInterventionData(response.data);
			setLoading(false);
		};
		fetchData();
	}, [docId]);

	const fetchClientSecret = useCallback(async () => {
		if (!interventionData) return undefined;

		const paymentIntent = await fetch("/api/checkout_sessions", {
			method: "POST",
			headers: { "Content-Type": "application/json" },
			body: JSON.stringify({ data: interventionData }),
		});

		if (!paymentIntent.ok) {
			setError(
				`Fetch client secret: HTTP error status: ${paymentIntent.status}`,
			);
			return undefined;
		} else {
			const paymentIntentData = await paymentIntent.json();
			return paymentIntentData.clientSecret;
		}
	}, [interventionData]);

	if (loading) return <LoadingSpinner />;

	if (error)
		return <FailureSection technicalError={false} goToStage={goToStage} />;

	if (interventionData?.paymentStatus === "complete") {
		return (
			<SuccessSection
				customerEmail={interventionData?.userEmail}
				goToStage={goToStage}
			/>
		);
	}

	const options = { fetchClientSecret };

	return (
		<div className="w-full">
			<div id="checkout">
				{stripePromise ? (
					<div className="flex flex-col gap-2">
						<h3 className="font-primary text-allo-dark-grey mt-2 mb-10 text-center text-2xl font-bold lg:text-3xl">
							Commander
						</h3>
						<EmbeddedCheckoutProvider stripe={stripePromise} options={options}>
							<EmbeddedCheckout />
						</EmbeddedCheckoutProvider>
					</div>
				) : (
					<div className="flex justify-center">
						<FailureSection technicalError={true} goToStage={goToStage} />
					</div>
				)}
			</div>
		</div>
	);
}
