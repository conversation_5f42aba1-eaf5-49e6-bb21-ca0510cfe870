import SuccessIcon from "/public/uploads/icons/wired-lineal-457-shield-security-hover-pinch.json";
import { AnimatedIcon } from "@/lib/animatedIconPlayer";
import ReserveAgainButton from "./ReserveAgainButton";

export default function SuccessSection({
	customerEmail,
	goToStage,
}: {
	customerEmail: string | undefined;
	goToStage: (stage: number) => void;
}) {
	return (
		<section
			id="success"
			className="box-border flex w-full max-w-96 flex-col items-center justify-center p-5 lg:mb-10"
		>
			<AnimatedIcon icon={SuccessIcon} shouldPlay={true} size={100} />
			<h3 className="font-primary text-allo-dark-grey mt-2 mb-10 text-center text-2xl font-bold lg:text-3xl">
				Votre paiement est en cours de traitement !
			</h3>
			<p className="font-primary text-allo-dark-grey mb-3 text-center text-sm font-normal lg:text-base">
				Un email de confirmation avec les détails de l&apos;intervention et la
				facture sera envoyé à&nbsp;
				<span className="font-semibold">{customerEmail}</span> lors de la
				confirmation du paiement.
			</p>
			<p className="font-primary text-allo-dark-grey mb-5 text-center text-sm font-normal lg:text-base">
				Si vous avez des questions, veuillez nous contacter à{" "}
				<a className="text-allo-blue" href="mailto:<EMAIL>">
					<EMAIL>
				</a>
				.
			</p>
			<p className="font-primary text-allo-dark-grey mb-3 text-center text-sm font-normal lg:text-base">
				Nous vous remercions de votre confiance !
			</p>
			<ReserveAgainButton goToStage={goToStage} />
		</section>
	);
}
