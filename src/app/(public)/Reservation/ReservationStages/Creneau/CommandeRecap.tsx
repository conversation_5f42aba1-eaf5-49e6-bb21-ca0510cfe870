import { useState, useEffect } from "react";
import { DocData } from "./Creneau";
import type { Prices } from "./Creneau";

export default function CommandeRecap({
	docData,
	interventionTravelDuration,
	interventionDuration,
	zonePrice,
	pricesByDay,
	date,
}: {
	docData: DocData;
	interventionTravelDuration: number;
	interventionDuration: number;
	zonePrice: number;
	pricesByDay: Prices;
	date: Date | undefined;
}) {
	const [prices, setPrices] = useState<{
		totalPrice: number;
		urgencyPremium: number;
		holidayPremium: number;
		weekendPremium: number;
		totalPriceBeforePremium: number;
	}>({
		totalPrice: 0,
		urgencyPremium: 0,
		holidayPremium: 0,
		weekendPremium: 0,
		totalPriceBeforePremium: 0,
	});

	useEffect(() => {
		if (date && pricesByDay) {
			const fullDateKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}-${String(date.getDate()).padStart(2, "0")}`;
			if (pricesByDay[fullDateKey]) {
				setPrices({
					totalPrice: pricesByDay[fullDateKey].totalPrice || 0,
					urgencyPremium: pricesByDay[fullDateKey].urgencyPremium || 0,
					holidayPremium: pricesByDay[fullDateKey].holidayPremium || 0,
					weekendPremium: pricesByDay[fullDateKey].weekendPremium || 0,
					totalPriceBeforePremium:
						(pricesByDay[fullDateKey].totalPrice || 0) -
						(pricesByDay[fullDateKey].urgencyPremium || 0) -
						(pricesByDay[fullDateKey].holidayPremium || 0) -
						(pricesByDay[fullDateKey].weekendPremium || 0),
				});
			}
		}
	}, [date, pricesByDay]);

	return (
		<div className="RECAP box-border flex w-full flex-col p-2 lg:basis-2/5 lg:p-5">
			<div className="font-primary text-allo-dark-grey flex w-full flex-col gap-2 text-xs font-normal md:text-sm">
				<div className="flex w-full max-md:justify-center">
					<h2 className="bg-allo-grad font-primary mb-3 inline-block bg-clip-text text-sm text-transparent md:text-lg">
						Récapitulatif de ma commande
					</h2>
				</div>
				<div className="flex justify-between">
					<p className="text-allo-md-grey">Type d&apos;intervention :</p>
					<p>&nbsp;{docData.maintenance ? "Maintenance" : "Panne"}</p>
				</div>
				<div className="flex justify-between">
					<p className="text-allo-md-grey">Lieu d&apos;intervention :</p>
					<p>&nbsp;{docData.commune}</p>
				</div>
				<div className="mb-4">
					<div className="flex justify-between">
						<p className="text-allo-md-grey">Équipement(s) :</p>
						<p>
							&nbsp;
							{[
								docData.clim && "Climatisation",
								docData.chaudiere && "Chaudière",
								docData.pompe && "Pompe à chaleur",
							]
								.filter(Boolean)
								.join(", ")}
						</p>
					</div>
					{docData.clim != 0 && (
						<div className="text-allo-md-grey mt-2 ml-4">
							<ul className="list-disc">
								{Object.entries(docData.climSubOptions || {}).map(
									([subOption, count]) =>
										(count as number) > 0 && (
											<li key={subOption}>
												{subOption === "split"
													? "Murale"
													: subOption === "exterieur"
														? "Groupe extérieur"
														: subOption.charAt(0).toUpperCase() +
															subOption.slice(1)}
												{" x "}
												{count as number}
											</li>
										),
								)}
							</ul>
						</div>
					)}
				</div>
				<div className="flex justify-between">
					<p className="text-allo-md-grey">Temps de déplacement : </p>
					<p>
						&nbsp;{Math.floor(interventionTravelDuration / 60)}h
						{!(interventionTravelDuration % 60)
							? ""
							: interventionTravelDuration % 60}
					</p>
				</div>
				<div className="flex justify-between">
					<p className="text-allo-md-grey">Temps de l&apos;intervention : </p>
					<p>
						&nbsp;{Math.floor(interventionDuration / 60)}h
						{!(interventionDuration % 60) ? "" : interventionDuration % 60}
					</p>
				</div>
				<div className="mb-4">
					<div className="flex justify-between">
						<p className="text-allo-md-grey">Durée totale: </p>
						<strong>
							&nbsp;
							{Math.floor(
								(interventionTravelDuration + interventionDuration) / 60,
							)}
							h
							{!((interventionTravelDuration + interventionDuration) % 60)
								? ""
								: (interventionTravelDuration + interventionDuration) % 60}
						</strong>
					</div>
				</div>
				<div className="flex justify-between">
					<p className="text-allo-md-grey">Prix de déplacement : </p>
					<p>&nbsp;{zonePrice}€</p>
				</div>
				<div className="flex justify-between">
					<p className="text-allo-md-grey">Prix des équipments : </p>
					<p>&nbsp;{prices.totalPriceBeforePremium - zonePrice}€</p>
				</div>
				{prices.urgencyPremium > 0 && (
					<div className="flex justify-between">
						<p className="text-allo-md-grey">
							Prime d&apos;intervention express :{" "}
						</p>
						<p>&nbsp;{prices.urgencyPremium}€</p>
					</div>
				)}
				{prices.holidayPremium > 0 && (
					<div className="flex justify-between">
						<p className="text-allo-md-grey">Prime de jours fériés : </p>
						<p>&nbsp;{prices.holidayPremium}€</p>
					</div>
				)}
				{prices.weekendPremium > 0 && (
					<div className="flex justify-between">
						<p className="text-allo-md-grey">Prime de week-end : </p>
						<p>&nbsp;{prices.weekendPremium}€</p>
					</div>
				)}
				<div className="my-4 flex justify-between lg:mb-8">
					<p className="text-allo-md-grey">Prix total : </p>
					<strong>&nbsp;{prices.totalPrice} EUR</strong>
				</div>
				<div className="flex justify-between">
					<p className="text-allo-md-grey">Date : </p>
					<p>
						&nbsp;
						{date?.toLocaleDateString("fr-FR", {
							day: "2-digit",
							month: "long",
							year: "numeric",
						})}
					</p>
				</div>
			</div>
		</div>
	);
}
