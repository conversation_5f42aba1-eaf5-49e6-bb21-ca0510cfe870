import { useState, useEffect, useCallback } from "react";
import { db } from "@/lib/firebaseConfig";
import { getDoc, doc, updateDoc, Timestamp } from "firebase/firestore";
import { Button } from "@/components/ui/button";
import { CalendarDay, DayPicker } from "react-day-picker";
import { fr } from "react-day-picker/locale";
import {
	fetchZonePrice,
	fetchEquipmentPrices,
	fetchSelectedDateAndTime,
} from "./Functions";
import {
	Breadcrumb,
	BreadcrumbItem,
	BreadcrumbList,
	BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { LoadingSpinner } from "@/components/ui/loadingSpinner";
import Horaires from "./Horaires";
import CommandeRecap from "./CommandeRecap";
import { fetchFrenchHolidays } from "@/lib/Holiday";
import { DateTime } from "luxon";

type stageProps = {
	nextStage: (id?: string) => void;
	docId: string | undefined;
};

export type DocData = {
	createdAt?: Timestamp;
	communeZone?: string;
	communeDept?: string;
	maintenance?: number;
	panne?: number;
	climSubOptions?: Record<string, number>;
	clim?: number;
	chaudiere?: number;
	pompe?: number;
	commune?: string;
	interventionDate?: Timestamp;
	interventionTime?: string;
	interventionDuration?: number;
};

type Equipment = {
	[equipmentName: string]: {
		maint_tarif: number;
		panne_tarif: number;
		maint_temps: number;
		panne_temps: number;
		options?: {
			[subOptionName: string]: {
				maint_tarif: number;
				panne_tarif: number;
				maint_temps: number;
				panne_temps: number;
			};
		};
	};
};

export type Prices = {
	[key: string]: {
		totalPrice: number;
		urgencyPremium: number | null;
		holidayPremium: number | null;
		weekendPremium: number | null;
	};
};

export default function Creneau({ nextStage, docId }: stageProps) {
	const [date, setDate] = useState<Date | undefined>(
		DateTime.now().setZone("Europe/Paris").plus({ days: 7 }).toJSDate(),
	);
	const [docData, setDocData] = useState<DocData>();
	const [zonePrice, setZonePrice] = useState<number>(0);
	const [equipments, setEquipments] = useState<Equipment>({});
	const [equipmentPrice, setEquipmentPrice] = useState<number>(0);
	const [travelDuration, setTravelDuration] = useState<number>(0);
	const [interventionDuration, setInterventionDuration] = useState<number>(0);
	const [pricesByDay, setPricesByDay] = useState<Prices>({});
	const [loading, setLoading] = useState<boolean>(true);
	const [isVisible, setIsVisible] = useState<boolean>(true);

	const loadData = useCallback(async () => {
		try {
			const docRef = doc(db, "interventions", docId!);
			const docSnap = await getDoc(docRef);

			if (docSnap.exists()) {
				const data = docSnap.data();
				setDocData(data);
				await fetchZonePrice(
					data.communeZone,
					data.communeDept,
					setZonePrice,
					setTravelDuration,
				);
				await fetchEquipmentPrices(setEquipments);
				await fetchSelectedDateAndTime(setDate, docId);
			}
		} catch (error) {
			console.warn("Error loading data:", error);
		}
	}, [docId]);

	useEffect(() => {
		if (docId) loadData();
	}, [docId, loadData]);

	const calculateTotalPrice = useCallback(() => {
		let price = zonePrice;
		let time = 0;

		// Add main equipment price based on intervention type
		if (docData) {
			const interventionType = docData.maintenance ? "maintenance" : "panne";
			const interventionTarif = docData.maintenance
				? "maint_tarif"
				: "panne_tarif";
			const interventionTime = docData.maintenance
				? "maint_temps"
				: "panne_temps";
			for (const [equipment, selected] of Object.entries(docData)) {
				if (selected && equipments[equipment]) {
					if (
						equipment === "clim" &&
						interventionType == "maintenance" &&
						docData.climSubOptions
					) {
						for (const [subOption, count] of Object.entries(
							docData.climSubOptions,
						)) {
							if (
								count &&
								equipments.clim.options &&
								equipments.clim.options[subOption]
							) {
								price +=
									equipments.clim.options[subOption][interventionTarif] *
									(count as number);
								time +=
									equipments.clim.options[subOption][interventionTime] *
									(count as number);
							}
						}
					} else {
						price += equipments[equipment][interventionTarif];
						time += equipments[equipment][interventionTime];
					}
				}
			}
		} else {
			console.warn("Error: Creneau: calculateTotalPrice: docData is undefined");
		}
		return { price: price, time: time };
	}, [zonePrice, docData, equipments]);

	useEffect(() => {
		if (docData && Object.keys(equipments).length > 0) {
			const interventionDetails = calculateTotalPrice();
			setEquipmentPrice(Math.ceil(interventionDetails.price));
			setInterventionDuration(interventionDetails.time);
		}
	}, [docData, equipments, calculateTotalPrice]);

	const calculatePricesByDay = useCallback(async () => {
		const today = DateTime.now().setZone("Europe/Paris").startOf("day");
		const prices: Prices = {};

		for (let offset = 0; offset < 3; offset++) {
			const baseMonth = today.plus({ months: offset });
			const year = baseMonth.year;
			const month = baseMonth.month;

			// 🔥 Récupère les jours fériés pour l’année en cours
			const holidays = await fetchFrenchHolidays(baseMonth.year);

			const daysInMonth = baseMonth.daysInMonth ?? 31;

			for (let day = 1; day <= daysInMonth; day++) {
				let urgencyPremium = 0,
					holidayPremium = 0,
					weekendPremium = 0;
				const currentDate = DateTime.fromObject(
					{ year, month, day },
					{ zone: "Europe/Paris" },
				).startOf("day");
				const key = currentDate.toFormat("yyyy-MM-dd");

				// Base price
				let totalPerDay = Math.ceil(equipmentPrice);

				// Calculate days difference from today
				const daysDifference = currentDate.diff(today, "days").days;

				// Apply urgency premiums
				if (daysDifference === 2 || daysDifference === 3) {
					urgencyPremium = Math.ceil(equipmentPrice * 2.0); // 200% premium for J+2 and J+3
					totalPerDay += urgencyPremium;
				} else if (daysDifference >= 4 && daysDifference <= 6) {
					urgencyPremium = Math.ceil(equipmentPrice * 1.5); // 150% premium for J+4, J+5, J+6
					totalPerDay += urgencyPremium;
				}

				// Apply weekend and holiday premiums
				const dayOfWeek = currentDate.weekday; // 1 (Monday) to 7 (Sunday)
				if (dayOfWeek === 7 || holidays.has(key)) {
					if (holidays.has(key)) {
						holidayPremium = Math.ceil(equipmentPrice * 0.4); // Apply 40% premium on holidays
						totalPerDay += holidayPremium;
					} else {
						weekendPremium = Math.ceil(equipmentPrice * 0.4); // Apply 40% premium on Sundays
						totalPerDay += weekendPremium;
					}
				} else if (dayOfWeek === 6) {
					weekendPremium = Math.ceil(equipmentPrice * 0.2); // Apply 40% premium on Saturdays
					totalPerDay += weekendPremium;
				}

				prices[key] = {
					totalPrice: totalPerDay,
					urgencyPremium: urgencyPremium,
					holidayPremium: holidayPremium,
					weekendPremium: weekendPremium,
				};
			}
		}

		setPricesByDay(prices);
	}, [equipmentPrice]);

	useEffect(() => {
		if (equipmentPrice && date) {
			calculatePricesByDay().then(() => setLoading(false));
		}
	}, [equipmentPrice, date, calculatePricesByDay]);

	const CustomDayButton = ({
		day,
		modifiers,
		...buttonProps
	}: {
		day: CalendarDay;
		modifiers: Record<string, boolean>;
	}) => {
		let price = 0;
		const dt = DateTime.fromJSDate(day.date, { zone: "Europe/Paris" }).startOf(
			"day",
		);
		const fullDateKey = dt.toFormat("yyyy-MM-dd");

		if (pricesByDay && pricesByDay[fullDateKey])
			price = pricesByDay[fullDateKey].totalPrice || 0;

		let priceTextColorClass = "text-emerald-700"; // Default dark green colour

		if (price > equipmentPrice) {
			if (price > equipmentPrice * 1.5) priceTextColorClass = "text-rose-600";
			else priceTextColorClass = "text-amber-600";
		}

		return (
			<div className="flex flex-col justify-between gap-0.5">
				<button {...buttonProps}>{dt.day}</button>
				{!modifiers.disabled ? (
					<span className={`price-text text-xs ${priceTextColorClass}`}>
						{price}€
					</span>
				) : (
					<span className="text-allo-md-grey invisible text-xs">0€</span>
				)}
			</div>
		);
	};

	const handleCalendarSelection = async () => {
		if (!date) return;
		try {
			const docRef = doc(db, "interventions", docId!);
			const docSnap = await getDoc(docRef);

			if (docSnap.exists() && date) {
				const updateData: Partial<{ interventionDate: string }> = {
					interventionDate:
						DateTime.fromJSDate(date, { zone: "Europe/Paris" }).toISODate() ??
						undefined,
				};

				await updateDoc(docRef, updateData);
				setIsVisible(false);
			}
		} catch (error) {
			console.warn("Error updating intervention date", error);
		}
	};

	const handeTimeSelection = async (selectedTime: string) => {
		try {
			const docRef = doc(db, "interventions", docId!);
			const docSnap = await getDoc(docRef);

			if (docSnap.exists() && date) {
				let totalNetPrice = 0,
					totalPremiumPrice = 0;

				const selectedDateKey = DateTime.fromJSDate(date, {
					zone: "Europe/Paris",
				}).toFormat("yyyy-MM-dd");
				if (pricesByDay && pricesByDay[selectedDateKey]) {
					totalNetPrice = pricesByDay[selectedDateKey].totalPrice || 0;
					totalPremiumPrice =
						(pricesByDay[selectedDateKey].urgencyPremium || 0) +
						(pricesByDay[selectedDateKey].holidayPremium || 0) +
						(pricesByDay[selectedDateKey].weekendPremium || 0);
				}

				if (totalNetPrice) {
					const updateData: Partial<{
						interventionTime: string;
						interventionTotalPrice: number;
						interventionGrossPrice: number;
						interventionPremiumPrice: number;
						interventionTravelPrice: number;
						interventionTravelDuration: number;
						interventionDuration: number;
						interventionTotalDuration: number;
					}> = {
						interventionTime: selectedTime,
						interventionTotalPrice: totalNetPrice,
						interventionGrossPrice: totalNetPrice - zonePrice,
						interventionPremiumPrice: totalPremiumPrice,
						interventionTravelPrice: zonePrice,
						interventionTravelDuration: travelDuration,
						interventionDuration: interventionDuration,
						interventionTotalDuration: interventionDuration + travelDuration,
					};
					await updateDoc(docRef, updateData);
					nextStage(docId ?? undefined);
				} else {
					throw Error("Intervention total price not defined");
				}
			} else
				throw Error("Intervention document not found or date not selected");
		} catch (error) {
			console.warn("Error updating intervention time:", error);
		}
	};

	return (
		<>
			{loading ? (
				<LoadingSpinner />
			) : (
				docData && (
					<div className="CRENEAU box-border flex w-full flex-col items-center">
						<h3 className="font-primary text-allo-dark-grey mt-2 mb-5 text-center text-2xl font-bold lg:mb-10 lg:text-start lg:text-3xl">
							Programmez votre intervention
						</h3>
						<div className="flex w-full flex-col justify-between gap-5 max-lg:max-w-lg lg:flex-row lg:items-stretch lg:gap-14">
							<CommandeRecap
								docData={docData}
								interventionTravelDuration={travelDuration}
								interventionDuration={interventionDuration}
								zonePrice={zonePrice}
								pricesByDay={pricesByDay}
								date={date}
							/>
							<div className="CALENDRIER box-border flex w-full flex-col max-lg:items-center lg:basis-3/5 lg:p-5">
								<Breadcrumb>
									<BreadcrumbList className="text-allo-md-grey mx-2 mt-1 mb-3 justify-end">
										<BreadcrumbItem
											className={`cursor-pointer hover:font-semibold ${
												isVisible ? "font-semibold" : ""
											}`}
											onClick={() => setIsVisible(true)}
										>
											Calendrier
										</BreadcrumbItem>
										<BreadcrumbSeparator />
										<BreadcrumbItem
											className={`${!isVisible ? "font-semibold" : ""}`}
										>
											Horaires
										</BreadcrumbItem>
									</BreadcrumbList>
								</Breadcrumb>
								<div className="calendar-time-group relative box-border flex w-full justify-center rounded-lg bg-white p-5 shadow max-[320px]:px-2 lg:p-10">
									<DayPicker
										className={isVisible ? "block" : "hidden"}
										locale={fr}
										mode="single"
										selected={date}
										onSelect={setDate}
										showOutsideDays={true}
										disabled={{
											before: DateTime.now()
												.setZone("Europe/Paris")
												.plus({ days: 2 })
												.toJSDate(),
											after: DateTime.now()
												.setZone("Europe/Paris")
												.plus({ days: 38 })
												.toJSDate(),
										}}
										footer={
											<>
												<Button onClick={handleCalendarSelection}>
													Suivant &#8594;
												</Button>
												{!date && (
													<p className="font-primary text-allo-dark-grey mt-2 text-xs">
														Veuillez sélectionner une date
													</p>
												)}
											</>
										}
										components={{
											DayButton: CustomDayButton, // Custom renderer for day cells
										}}
									/>
									<div
										className={`box-border flex w-full p-5 ${!isVisible ? `block` : `hidden`}`}
									>
										<Horaires
											handeTimeSelection={handeTimeSelection}
											selectedDate={date}
											interventionTotalDuration={
												interventionDuration + travelDuration
											}
											setIsVisible={setIsVisible}
										/>
									</div>
								</div>
							</div>
						</div>
					</div>
				)
			)}
		</>
	);
}
