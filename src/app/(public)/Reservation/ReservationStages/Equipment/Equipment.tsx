import ClimSplit from "/public/uploads/images/equipments/clim-split.jpg";
import <PERSON>udiere from "/public/uploads/images/equipments/chaudiere.jpg";
import PompeChaleur from "/public/uploads/images/equipments/pompe-chaleur.webp";
import { useState } from "react";
import ACTypes from "./ACTypes";
import { db } from "@/lib/firebaseConfig";
import { getDoc, doc, updateDoc } from "firebase/firestore";
import Image from "next/image";
import { Button } from "@/components/ui/button";

type stageProps = {
	nextStage: (id?: string) => void;
	docId: string | undefined;
};

export default function Equipment({ nextStage, docId }: stageProps) {
	const [showClimOptions, setShowClimOptions] = useState(false);
	const [climSubOptions, setClimSubOptions] = useState<{
		[key: string]: number;
	}>({});

	const handleEquipmentSelection = async (selectedEquipment: string) => {
		if (docId) {
			const docRef = doc(db, "interventions", docId);
			const docSnap = await getDoc(docRef);

			if (docSnap.exists()) {
				const docData = docSnap.data();
				let updateData: Record<string, number> = {};

				if (selectedEquipment === "clim") {
					// Set clim to 1, chaudiere and pompe to 0, and ensure exterieur is at least 1
					updateData = {
						clim: 1,
						chaudiere: 0,
						pompe: 0,
						"climSubOptions.exterieur": Math.max(
							1,
							docData.climSubOptions?.exterieur || 0,
						),
						// Retain existing values in climSubOptions (no reset needed)
					};
					setClimSubOptions(docData.climSubOptions || {});
				} else if (
					selectedEquipment === "chaudiere" ||
					selectedEquipment === "pompe"
				) {
					// Set the selected equipment to 1, reset the others, and reset all sub-options to 0
					updateData = {
						chaudiere: selectedEquipment === "chaudiere" ? 1 : 0,
						clim: 0,
						pompe: selectedEquipment === "pompe" ? 1 : 0,
					};

					// Loop through climSubOptions keys and set each to 0
					const climSubOptionsReset = Object.keys(
						docData.climSubOptions || {},
					).reduce(
						(acc: { climSubOptions: Record<string, number> }, key) => {
							acc.climSubOptions[key] = 0;
							return acc;
						},
						{ climSubOptions: {} },
					);

					// Merge climSubOptionsReset into updateData
					Object.assign(updateData, climSubOptionsReset);
				}

				// Update the document in Firestore
				await updateDoc(docRef, updateData);
				if (selectedEquipment === "clim") setShowClimOptions(true);
				else nextStage(docId);
			} else {
				console.error("Error: Equipment: Intervention document does not exist");
			}
		}
	};

	return (
		<div className="EQUIPMENT box-border flex w-full flex-col items-center">
			<h3 className="font-primary text-allo-dark-grey mt-2 mb-10 text-center text-2xl font-bold lg:text-start lg:text-3xl">
				Choisissez votre équipement
			</h3>
			{!showClimOptions && (
				<div className="flex w-full gap-5 overflow-y-auto min-[375px]:flex-wrap min-[375px]:justify-center min-[375px]:gap-1.5 min-[425px]:gap-5">
					{/* Climatisation */}
					<div className="box-border flex w-40 min-w-40 flex-col items-center justify-between gap-6 rounded-lg bg-white p-5 shadow-md hover:shadow-md min-[375px]:px-0 min-[375px]:py-5 sm:w-54 lg:min-w-80 lg:px-12 lg:py-10 lg:shadow">
						<div className="flex w-full">
							<Image
								width={720}
								height={480}
								alt="Climatiseur split"
								src={ClimSplit.src}
								className="w-full object-cover"
							/>
						</div>
						<Button
							variant="default2"
							onClick={() => handleEquipmentSelection("clim")}
						>
							Climatisation
						</Button>
					</div>

					{/* Chaudiere */}
					<div className="box-border flex w-40 min-w-40 flex-col items-center justify-between gap-6 rounded-lg bg-white p-5 shadow-md hover:shadow-md min-[375px]:px-0 min-[375px]:py-5 sm:w-54 lg:min-w-80 lg:px-12 lg:py-10 lg:shadow">
						<div className="flex w-full">
							<Image
								width={720}
								height={480}
								alt="Chaudière"
								src={Chaudiere.src}
								className="w-full object-cover"
							/>
						</div>
						<Button
							variant="default2"
							onClick={() => handleEquipmentSelection("chaudiere")}
						>
							Chaudière
						</Button>
					</div>

					{/* Pompe à chaleur */}
					<div className="box-border flex w-40 min-w-40 flex-col items-center justify-between gap-6 rounded-lg bg-white p-5 shadow-md hover:shadow-md min-[375px]:px-0 min-[375px]:py-5 sm:w-54 lg:min-w-80 lg:px-12 lg:py-10 lg:shadow">
						<div className="flex w-full">
							<Image
								width={720}
								height={480}
								alt="Pompe à chaleur"
								src={PompeChaleur.src}
								className="w-full object-cover"
							/>
						</div>
						<Button
							variant="default2"
							onClick={() => handleEquipmentSelection("pompe")}
						>
							Pompe à chaleur
						</Button>
					</div>
				</div>
			)}
			{showClimOptions && (
				<ACTypes
					nextStage={nextStage}
					docId={docId}
					initialSubOptions={climSubOptions}
				/>
			)}
		</div>
	);
}
