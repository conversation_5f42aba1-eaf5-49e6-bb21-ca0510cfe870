import ClimSplit from "/public/uploads/images/equipments/clim-split.jpg";
import ClimCassette from "/public/uploads/images/equipments/clim-cassette.jpg";
import ClimConsole from "/public/uploads/images/equipments/clim-console.jpg";
import ClimGainable from "/public/uploads/images/equipments/clim-gainable.jpg";
import ClimExterieur from "/public/uploads/images/equipments/clim-groupe-exterieur.jpg";
import PompeCondensats from "/public/uploads/images/equipments/pompe-condensats.jpeg";
import AcCard from "@/app/(public)/Reservation/ReservationStages/Equipment/AcCard";
import { Button } from "@/components/ui/button";
import { db } from "@/lib/firebaseConfig";
import { doc, updateDoc } from "firebase/firestore";
import { useState } from "react";
import { useToast } from "@/components/ui/toast/use-toast";

interface stageProps {
	nextStage: (id?: string) => void;
	docId: string | undefined;
	initialSubOptions: { [key: string]: number };
}

export default function ACTypes({
	nextStage,
	docId,
	initialSubOptions,
}: stageProps) {
	const { toast } = useToast();
	const [interieurCounts, setInterieurCounts] = useState(() => {
		return {
			split: initialSubOptions.split || 0,
			cassette: initialSubOptions.cassette || 0,
			console: initialSubOptions.console || 0,
			gainable: initialSubOptions.gainable || 0,
		};
	});

	const handleSubOptionUpdate = async (title: string, amount: number) => {
		if (docId) {
			const docRef = doc(db, "interventions", docId);
			await updateDoc(docRef, {
				[`climSubOptions.${title.toLowerCase()}`]: amount,
			});
		}

		// Update local state for 'interieur' types
		if (
			["split", "cassette", "console", "gainable"].includes(title.toLowerCase())
		) {
			setInterieurCounts((prev) => ({
				...prev,
				[title.toLowerCase()]: amount,
			}));
		}
	};

	const handleNextStage = () => {
		// Check if at least one 'interieur' option is selected
		const totalInterieurSelected = Object.values(interieurCounts).reduce(
			(sum, count) => sum + count,
			0,
		);
		if (totalInterieurSelected === 0) {
			toast({
				title: "Aucun équipement intérieur sélectionné",
				description:
					"Sélectionnez le(s) type(s) d'équipement de climatisation à l'intérieur de votre foyer.",
			});
			return;
		}

		// Proceed to the next stage if validation passes
		nextStage(docId ?? undefined);
	};

	return (
		<div className="box-border flex w-full flex-col items-center gap-10">
			<div className="acTypes flex w-full gap-5 overflow-y-auto min-[375px]:flex-wrap min-[375px]:justify-center min-[375px]:gap-1.5 min-[425px]:gap-5">
				{/* Clim Split */}
				<AcCard
					imageSrc={ClimSplit.src}
					title="Split"
					type="interieur"
					amount={interieurCounts.split}
					onUpdate={handleSubOptionUpdate}
				/>

				{/* Clim Cassette */}
				<AcCard
					imageSrc={ClimCassette.src}
					title="Cassette"
					type="interieur"
					amount={interieurCounts.cassette}
					onUpdate={handleSubOptionUpdate}
				/>

				{/* Clim Console */}
				<AcCard
					imageSrc={ClimConsole.src}
					title="Console"
					type="interieur"
					amount={interieurCounts.console}
					onUpdate={handleSubOptionUpdate}
				/>

				{/* Clim Gainable */}
				<AcCard
					imageSrc={ClimGainable.src}
					title="Gainable"
					type="interieur"
					amount={interieurCounts.gainable}
					onUpdate={handleSubOptionUpdate}
				/>

				{/* Clim Exteriuer */}
				<AcCard
					imageSrc={ClimExterieur.src}
					title="Extérieur"
					type="exterieur"
					amount={initialSubOptions.exterieur || 1}
					onUpdate={handleSubOptionUpdate}
				/>

				{/* Pompe Condensats */}
				<AcCard
					imageSrc={PompeCondensats.src}
					title="Pompe de condensats"
					type="optionnel"
					amount={initialSubOptions.pompe || 0}
					onUpdate={handleSubOptionUpdate}
				/>
			</div>
			<Button
				className="bg-allo-grad font-primary w-fit px-12 text-sm font-medium text-white"
				onClick={handleNextStage}
			>
				Suivant
			</Button>
		</div>
	);
}
