"use client";

import { useEffect, useState, useCallback } from "react";
import { Check, ChevronsUpDown } from "lucide-react";
import { db } from "@/lib/firebaseConfig";
import {
	query,
	collection,
	serverTimestamp,
	onSnapshot,
	setDoc,
	doc,
} from "firebase/firestore";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { LoadingSpinner } from "@/components/ui/loadingSpinner";
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from "@/components/ui/command";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@/components/ui/popover";
import { DateTime } from "luxon";

type Commune = {
	codePostal: string[];
	department: string;
	name: string;
	zone: number;
};

type ComboboxCommuneProps = {
	nextStage: (id?: string) => void;
};

export default function ComboboxCommune({ nextStage }: ComboboxCommuneProps) {
	const [open, setOpen] = useState(false);
	const [value, setValue] = useState("");
	const [communes, setCommunes] = useState<Commune[]>([]);

	const fetchCommunes = useCallback(async () => {
		const q = query(collection(db, "communes"));
		const unsubscribe = onSnapshot(q, (querySnapshot) => {
			const communeList: Commune[] = querySnapshot.docs.map((doc) => ({
				...doc.data(),
			})) as Commune[];
			setCommunes(communeList);
		});

		// Cleanup listener on component unmount
		return () => unsubscribe();
	}, []);

	useEffect(() => {
		fetchCommunes();
	}, [fetchCommunes]);

	return (
		<Popover open={open} onOpenChange={setOpen}>
			<PopoverTrigger asChild>
				<Button
					variant="outline"
					role="combobox"
					aria-expanded={open}
					className="w-full max-w-72 justify-between"
				>
					{value
						? communes.find((commune) => commune.name === value)?.name
						: "Choissisez votre commune..."}
					<ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
				</Button>
			</PopoverTrigger>
			<PopoverContent className="w-[300px] p-0">
				<Command>
					<CommandInput placeholder="Cherchez votre commune..." />
					<CommandList>
						<CommandEmpty>
							<div className="flex flex-col items-center justify-center">
								<LoadingSpinner />
							</div>
						</CommandEmpty>
						<CommandGroup>
							{communes.map((commune) => (
								<CommandItem
									key={commune.name}
									value={commune.name}
									onSelect={async (currentValue: string) => {
										setValue(currentValue === value ? "" : currentValue);
										setOpen(false);
										if (currentValue !== value) {
											const selectedCommune = communes.find(
												(commune) => commune.name === currentValue,
											);
											if (selectedCommune) {
												// Create a new document in Firestore with initial fields
												const docRef = doc(
													db,
													"interventions",
													selectedCommune.name +
														"_" +
														DateTime.now()
															.setZone("Europe/Paris")
															.toFormat("yyyy-MM-dd_HH:mm:ss:SSS"),
												);
												await setDoc(
													docRef,
													{
														commune: selectedCommune.name,
														communeZone: selectedCommune.zone,
														communeDept: selectedCommune.department,
														communeCodePostal: selectedCommune.codePostal,
														createdAt: serverTimestamp(),
														clim: 0,
														chaudiere: 0,
														pompe: 0,
														interventionId: docRef.id,
														env: process.env.NODE_ENV,
													},
													{ merge: true },
												);

												nextStage(docRef.id);
											} else {
												console.error("Commune not found");
											}
										}
									}}
								>
									<Check
										className={cn(
											"mr-2 h-4 w-4",
											value === commune.name ? "opacity-100" : "opacity-0",
										)}
									/>
									{commune.name}
								</CommandItem>
							))}
						</CommandGroup>
					</CommandList>
				</Command>
			</PopoverContent>
		</Popover>
	);
}
