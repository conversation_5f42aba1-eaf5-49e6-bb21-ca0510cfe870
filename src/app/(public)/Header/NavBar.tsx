"use client";

import { useEffect, useState } from "react";
import NavBarDesktop from "./NavBarDesktopContent";
import NavbarMobile from "./NavBarMobileContent";
import { onAuthStateChanged } from "firebase/auth";
import { auth } from "@/lib/firebaseConfig";
import { signOut } from "firebase/auth";
import useInactivityTimer from "@/lib/useInactivityTimer"; // Inactivity
import { useToast } from "@/components/ui/toast/use-toast";
import { FaTag } from "react-icons/fa";
export default function NavBar() {
	const [isVisible, setIsVisible] = useState<boolean>(true);
	const [lastScrollY, setLastScrollY] = useState<number>(0);
	const [isLoggedIn, setIsLoggedIn] = useState<boolean>(false);
	const { toast } = useToast();

	// Gestion de l'authentification
	useEffect(() => {
		const unsubscribe = onAuthStateChanged(auth, (user) => {
			if (user) {
				setIsLoggedIn(true);
			} else {
				setIsLoggedIn(false);
			}
		});
		return () => unsubscribe();
	}, []);

	// Utilisation du hook d'inactivité (1 heure par défaut)

	useInactivityTimer(3600000, () => {
		if (isLoggedIn) {
			setIsLoggedIn(false);
			signOut(auth)
				.then(() => {
					toast({
						title: "Déconnexion automatique",
						description: "Vous avez été déconnecté en raison d'inactivité.",
						variant: "destructive",
					});
				})
				.catch((error) => {
					toast({
						title: "Erreur lors de déconnexion",
						description: (error as Error).message,
						variant: "destructive",
					});
				});
		}
	});

	// Gestion du défilement
	useEffect(() => {
		const handleScroll = () => {
			const currentScrollY = window.scrollY;

			if (currentScrollY > lastScrollY && currentScrollY > 100)
				setIsVisible(false);
			else setIsVisible(true);

			setLastScrollY(currentScrollY);
		};

		window.addEventListener("scroll", handleScroll);

		return () => {
			window.removeEventListener("scroll", handleScroll);
		};
	}, [lastScrollY]);

	return (
		<>
			<header
				className={`fixed top-0 left-0 z-30 flex h-20 w-full justify-center bg-white shadow-md transition-opacity duration-300 ${isVisible ? "opacity-100" : "pointer-events-none opacity-0"}`}
			>
				<NavBarDesktop isLoggedIn={isLoggedIn} />
				<NavbarMobile isLoggedIn={isLoggedIn} />
			</header>
			<div
				className={`bg-allo-grad fixed top-20 left-0 z-20 w-full py-2 text-center text-sm text-white transition-opacity duration-300 ${isVisible ? "opacity-100" : "pointer-events-none opacity-0"}`}
			>
				<div className="flex items-start justify-center gap-2 px-2">
					<FaTag className="text-yellow-300 max-md:hidden" />
					<p>
						<span className="font-bold">Nouveau client ?</span> Profitez de 10%
						de réduction ! Utilisez le code promo{" "}
						<span className="font-bold">#NOUVEAUCLIENT</span>{" "}
						<span className="max-md:hidden">
							lors de votre première commande pour bénéficier de cette offre
							exclusive.
						</span>
					</p>
				</div>
			</div>
		</>
	);
}
