import Link from "next/link";
import Image from "next/image";
import LogoImage from "/public/uploads/logos/AlloCVC_main_logo.webp";
import HamburgerMenuImage from "/public/uploads/icons/burger-menu.svg";
import MenuSheet from "./MenuSheet";

export default function NavbarMobile({ isLoggedIn }: { isLoggedIn: boolean }) {
	return (
		<>
			<div className="flex w-11/12 justify-between md:w-4/5 lg:hidden">
				<Link href="/" className="">
					<Image
						src={LogoImage.src}
						width={200}
						height={100}
						alt="allo cvc logo"
						className="m-2 max-w-32"
					/>
				</Link>
				<div className="flex flex-col items-center justify-center">
					<MenuSheet
						triggerElement={
							<Image
								src={HamburgerMenuImage.src}
								width={150}
								height={150}
								alt="allo cvc logo"
								className="m-2 max-w-8"
							/>
						}
						isLoggedIn={isLoggedIn}
					/>
				</div>
			</div>
		</>
	);
}
