import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { AnimatedIcon } from "@/lib/animatedIconPlayer";
import AvatarIcon from "/public/uploads/icons/wired-lineal-21-avatar-hover-jumping.json";
import { UserConnectDialog } from "../User/UserConnectDialog";
import UserAccountSheet from "../User/UserAccountSheet";

export default function MenuSheet({
	triggerElement,
	isLoggedIn,
}: {
	triggerElement: React.ReactNode;
	isLoggedIn: boolean;
}) {
	return (
		<Sheet>
			<SheetTrigger>{triggerElement}</SheetTrigger>
			<SheetContent
				side="left"
				className="flex h-3/5 min-h-[480px] flex-col bg-white"
			>
				<SheetHeader>
					<SheetTitle>
						<span className="bg-allo-grad font-primary text-t-15 inline-block bg-clip-text px-5 font-semibold text-transparent uppercase">
							Menu
						</span>
					</SheetTitle>
				</SheetHeader>

				<nav className="mt-10 flex basis-2/5 flex-col px-5">
					<SheetClose asChild>
						<Link
							className="text-md font-primary text-allo-md-grey hover:border-allo-blue h-full w-fit py-2.5 font-medium hover:border-b-2 hover:font-bold"
							href="#heroSection"
						>
							Vue d&apos;ensemble
						</Link>
					</SheetClose>
					<SheetClose asChild>
						<Link
							className="text-md font-primary text-allo-md-grey hover:border-allo-blue h-full w-fit py-2.5 font-medium hover:border-b-2 hover:font-bold"
							href="#aboutUsSection"
						>
							Notre équipe
						</Link>
					</SheetClose>
					<SheetClose asChild>
						<Link
							className="text-md font-primary text-allo-md-grey hover:border-allo-blue h-full w-fit py-2.5 font-medium hover:border-b-2 hover:font-bold"
							href="#uspSection"
						>
							Pourquoi entretenir
						</Link>
					</SheetClose>
					<SheetClose asChild>
						<Link
							className="text-md font-primary text-allo-md-grey hover:border-allo-blue h-full w-fit py-2.5 font-medium hover:border-b-2 hover:font-bold"
							href="#testimonialSection"
						>
							Vos avis et vos questions
						</Link>
					</SheetClose>
				</nav>
				<nav
					className={`flex basis-3/5 items-end justify-end ${isLoggedIn ? "space-x-7" : "space-x-2"}`}
				>
					{!isLoggedIn ? (
						<UserConnectDialog>
							<Button variant="outline">Se Connecter</Button>
						</UserConnectDialog>
					) : (
						<UserAccountSheet
							triggerElement={
								<div className="flex items-center justify-center">
									<AnimatedIcon icon={AvatarIcon} shouldPlay={true} size={34} />
								</div>
							}
						/>
					)}
					<SheetClose asChild>
						<Link
							href="#reservationSection"
							className="object-fit bg-allo-grad w-fit rounded-md px-5 py-2 text-sm font-medium text-slate-50 shadow-sm"
						>
							Réserver
						</Link>
					</SheetClose>
				</nav>
			</SheetContent>
		</Sheet>
	);
}
