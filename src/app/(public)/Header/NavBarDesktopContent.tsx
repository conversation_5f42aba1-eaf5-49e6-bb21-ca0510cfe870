import Image from "next/image";
import Link from "next/link";
import LogoImage from "/public/uploads/logos/AlloCVC_main_logo.webp";
import { UserConnectDialog } from "../User/UserConnectDialog";
import UserAccountSheet from "../User/UserAccountSheet";
import { Button } from "@/components/ui/button";
import { AnimatedIcon } from "@/lib/animatedIconPlayer";
import AvatarIcon from "/public/uploads/icons/wired-lineal-21-avatar-hover-jumping.json";

export default function NavBarDesktop({ isLoggedIn }: { isLoggedIn: boolean }) {
	return (
		<>
			<div className="hidden w-5/6 max-w-screen-2xl lg:flex lg:w-4/5">
				<Link
					href="/"
					className="relative flex basis-1/4 items-center justify-start"
				>
					<Image
						src={LogoImage.src}
						width={300}
						height={150}
						alt="allo cvc logo"
						className="my-1 mr-10 ml-0 max-w-40"
					/>
				</Link>
				<nav className="flex basis-1/2 items-center justify-evenly">
					<Link
						className="font-primary text-allo-md-grey hover:border-allo-blue h-full content-center px-3 py-2.5 text-center text-xs font-semibold uppercase hover:border-b-2 hover:font-bold"
						href="#heroSection"
					>
						Vue
						<br />
						d&apos;ensemble
					</Link>
					<Link
						className="font-primary text-allo-md-grey hover:border-allo-blue h-full content-center px-3 py-2.5 text-center text-xs font-semibold uppercase hover:border-b-2 hover:font-bold"
						href="#aboutUsSection"
					>
						Notre
						<br />
						équipe
					</Link>
					<Link
						className="font-primary text-allo-md-grey hover:border-allo-blue h-full content-center px-3 py-2.5 text-center text-xs font-semibold uppercase hover:border-b-2 hover:font-bold"
						href="#uspSection"
					>
						Pourquoi
						<br />
						entretenir
					</Link>
					<Link
						className="font-primary text-allo-md-grey hover:border-allo-blue h-full content-center px-3 py-2.5 text-center text-xs font-semibold uppercase hover:border-b-2 hover:font-bold"
						href="#testimonialSection"
					>
						Vos avis
						<br />
						et vos questions
					</Link>
				</nav>
				<nav
					className={`flex basis-1/4 items-center justify-end ${isLoggedIn ? "space-x-7" : "space-x-2"}`}
				>
					{!isLoggedIn ? (
						<UserConnectDialog>
							<Button variant="outline">Se connecter</Button>
						</UserConnectDialog>
					) : (
						<UserAccountSheet
							triggerElement={
								<div className="flex cursor-pointer items-center justify-center">
									<AnimatedIcon icon={AvatarIcon} shouldPlay={true} size={34} />
								</div>
							}
						/>
					)}
					<Link
						href="#reservationSection"
						className="object-fit bg-allo-grad w-fit rounded-md px-5 py-2 text-sm font-medium text-slate-50 shadow-sm"
					>
						Réserver
					</Link>
				</nav>
			</div>
		</>
	);
}
