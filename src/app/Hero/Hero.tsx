import Image from "next/image";
import ChaudiereImage from "/public/uploads/images/Hero_chaudiere.png";
import SplitImage from "/public/uploads/images/Hero_murale.png";
import PompeChaleurImage from "/public/uploads/images/Hero_pompe_chaleur.png";
import InteractiveLink from "@/components/ui/interactiveLink";
import SectionUSP from "./HeroUSP";
import ServicePricing from "./ServicePricing";

export default function Hero() {
	return (
		<section
			id="heroSection"
			className="border-allo-line flex h-full w-full flex-col border-b"
		>
			<div className="mt-40 mb-10 flex h-full w-full flex-col justify-between lg:mt-52 lg:flex-row lg:align-top">
				{/* Left container */}
				<div className="relative flex flex-col items-center justify-start gap-10 text-center lg:basis-1/2 lg:items-start lg:text-left">
					<div className="font-primary text-allo-md-grey flex flex-col gap-3 text-center text-4xl tracking-wide md:text-5xl lg:text-left lg:text-6xl">
						<h1 className="hidden">
							Votre partenaire pour l&apos;entretien de votre climatisation,
							chaudière et pompe à chaleur
						</h1>
						<span>Votre&nbsp;partenaire</span>
						<span>pour&nbsp;l&apos;entretien</span>
						<div className="flex flex-col 2xl:flex-row">
							<span>de&nbsp;votre&nbsp;</span>
							<div className="cube-spinner flex w-full justify-center lg:justify-start">
								<span className="spinner-face-1 bg-allo-grad inline-block bg-clip-text text-transparent">
									climatisation
								</span>
								<span className="spinner-face-2 bg-allo-grad inline-block bg-clip-text text-transparent">
									chaudière
								</span>
								<span className="spinner-face-3 bg-allo-grad inline-block bg-clip-text text-transparent">
									pompe&nbsp;à&nbsp;chaleur
								</span>
							</div>
						</div>
					</div>
					<SectionUSP />
					{/* A link tracked through google Analytics */}
					<InteractiveLink
						href="#reservationSection"
						className="object-fit bg-allo-grad w-fit rounded-md px-10 py-3 text-sm text-slate-50 uppercase"
						linkText="Réserver votre créneau"
						onClickEventName="hero_reservation_button_click"
						onClickLocation="hero_section"
					/>
				</div>

				{/* Right container */}
				<div className="relative hidden basis-1/2 justify-start lg:flex lg:flex-col">
					<Image
						src={ChaudiereImage}
						alt="chaudière image"
						width={600}
						height={800}
						className="bg-allo-bg-2 absolute top-44 right-0 h-48 w-36 rounded-2xl shadow-md 2xl:top-54 2xl:h-64 2xl:w-48"
					/>
					<Image
						src={PompeChaleurImage}
						alt="pompe à chaleur image"
						width={800}
						height={600}
						className="bg-allo-bg-2 absolute top-0 right-16 h-36 w-48 rounded-2xl shadow-md 2xl:right-20 2xl:h-48 2xl:w-64"
					/>
					<Image
						src={SplitImage}
						alt="split image"
						width={800}
						height={600}
						className="bg-allo-bg-2 absolute top-44 right-44 h-36 w-48 rounded-2xl shadow-md 2xl:top-54 2xl:right-54 2xl:h-48 2xl:w-64"
					/>
				</div>
			</div>
			<div>
				<ServicePricing />
			</div>
		</section>
	);
}
