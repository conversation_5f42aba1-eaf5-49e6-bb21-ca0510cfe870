import { NextPage } from "next";
import Link from "next/link";

const pageNotFound: NextPage = () => {
	return (
		<section
			id="404Error"
			className="box-border flex h-screen w-full flex-row items-center justify-center border-b border-allo-line"
		>
			<div className="box-border flex basis-1/3 flex-col items-center p-5">
				<h2 className="inline-block bg-allo-grad bg-clip-text font-primary text-t-15 font-semibold uppercase text-transparent">
					404
				</h2>
				<h3 className="mb-6 mt-2 font-primary text-3xl font-bold text-allo-dark-grey">
					Page non trouvée
				</h3>
				<p className="text-center font-primary text-base font-normal text-allo-dark-grey">
					Il est possible que l&apos;adresse que vous avez saisie soit
					incorrecte ou que la page ait été supprimée. Vous pouvez essayer de
					revenir à la page d&apos;accueil ou de rechercher ce que vous cherchez
					dans notre site.
				</p>
				<div className="mt-8 flex flex-wrap">
					<Link
						href="/"
						className="object-fit w-fit rounded-md bg-allo-grad px-10 py-3 text-sm text-slate-50"
					>
						Retour à l&apos;accueil
					</Link>
					<Link
						href="/#contactFormSection"
						className="object-fit w-fit px-10 py-3 text-sm text-allo-dark-grey"
					>
						Contactez-nous &#8594;
					</Link>
				</div>
			</div>
		</section>
	);
};

export default pageNotFound;
