import Hero from "./(public)/Hero/Hero";
import SectionReservation from "./(public)/Reservation/Reservation";
import SectionAboutUs from "./(public)/AboutUs/AboutUs";
import SectionTestimonials from "./(public)/Testimonial/Testimonials";
import SectionFAQ from "./(public)/FAQ/FAQ";
import SectionUSP from "./(public)/USP/USP";
import ContactForm from "./(public)/Contact/ContactForm";
import CookieBanner from "./(public)/CookieBanner/CookieBanner";
//import Cookies from "../pages/Cookies";
//import CookiePreferencesModal from "./CookieBanner/CookiePreferencesModal";

export default function Home() {
	return (
		<>
			<article className="mx-auto flex h-fit w-11/12 max-w-screen-2xl flex-col lg:w-4/5">
				<Hero />
				<SectionReservation />
				<SectionUSP />
				<SectionAboutUs />
				<SectionTestimonials />
				<SectionFAQ />
				<ContactForm />
				{/* <Cookies /> */}
			</article>

			<CookieBanner />
			{/* <PolitiqueModal /> */}

			{/* <CookiePreferencesModal /> */}
		</>
	);
}
