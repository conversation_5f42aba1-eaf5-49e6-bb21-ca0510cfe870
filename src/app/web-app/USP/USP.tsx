"use client";

import USPAccordion from "./USPAccordion";
import ClockIcon from "../../../public/uploads/icons/wired-lineal-45-clock-time-hover-pinch.json";
import PigIcon from "../../..//public/uploads/icons/wired-lineal-453-savings-pig-hover-pinch.json";
import SecurityIcon from "../../../public/uploads/icons/wired-lineal-946-equity-security-hover-locked.json";
import LawIcon from "../../../public/uploads/icons/wired-lineal-1468-morale-hover-pinch.json";
import { useState, useEffect, useMemo } from "react";
// import dynamic from "next/dynamic";
import { AnimatedIcon } from "@/lib/animatedIconPlayer";

// const AnimatedIcon = dynamic(() => import("@/components/ui/animatedIcon"), { ssr: false });

export default function SectionUSP() {
	const [activeItem, setActiveItem] = useState<string>("item-1");
	const [isLooping, setIsLooping] = useState<boolean>(true);
	const icons = [ClockIcon, SecurityIcon, PigIcon, LawIcon];
	const items = useMemo(() => ["item-1", "item-2", "item-3", "item-4"], []);
	const intervalTime = 4000;

	useEffect(() => {
		let interval: NodeJS.Timeout;

		if (isLooping) {
			interval = setInterval(() => {
				setActiveItem((prevItem) => {
					const currentIndex = items.indexOf(prevItem);
					const nextIndex = (currentIndex + 1) % items.length;
					return items[nextIndex];
				});
			}, intervalTime);
		}

		return () => clearInterval(interval);
	}, [isLooping, items]);

	return (
		<section
			id="uspSection"
			className="box-border flex h-auto w-full flex-col items-center border-b border-allo-line py-10 lg:flex-row lg:items-stretch lg:pb-20 lg:pt-10"
		>
			<div className="box-border flex max-w-[25rem] flex-1 flex-col items-center px-5 py-10 lg:max-w-full lg:justify-center lg:pl-20 2xl:py-20 2xl:pl-40 2xl:pr-20">
				<div className="flex-inline flex-col text-center lg:text-left">
					<h2 className="inline-block bg-allo-grad bg-clip-text font-primary text-t-15 font-semibold uppercase text-transparent">
						Pourquoi entretenir ?
					</h2>
					<h3 className="mb-6 mt-2 font-primary text-2xl font-bold text-allo-dark-grey lg:text-3xl">
						Les avantages d&apos;un entretien régulier
					</h3>
				</div>
				<USPAccordion
					activeItem={activeItem}
					setActiveItem={setActiveItem}
					setIsLooping={setIsLooping}
				/>
			</div>
			<div className="box-border hidden h-auto flex-1 grid-cols-2 grid-rows-2 items-end gap-4 py-20 pl-20 pr-40 lg:grid">
				{icons.map((icon, index) => (
					<div
						key={index}
						className={`flex aspect-square w-full max-w-40 items-center justify-center rounded bg-slate-50 ${index % 2 === 0 ? "justify-self-end" : "justify-self-start"} ${index >= 2 ? "self-start" : "self-end"} ${activeItem === items[index] ? "shadow-md" : "shadow-sm"} `}
					>
						<div>
							<AnimatedIcon
								icon={icon}
								shouldPlay={activeItem === items[index]}
								size={96}
							/>
						</div>
					</div>
				))}
			</div>
		</section>
	);
}
