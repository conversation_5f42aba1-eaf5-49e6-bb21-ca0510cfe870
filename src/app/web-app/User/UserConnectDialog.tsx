"use client";

import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>onte<PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>Trigger,
} from "@/components/ui/dialog";

import { useState, useActionState } from "react";
import { AuthForms } from "@/components/userAuthForms";

import { useToast } from "@/components/ui/toast/use-toast";
import { ManualSignUp, ManualLogIn } from "./ManualSignIn";
//import GoogleSignInComponent from "./GoogleSignIn"

export function UserConnectDialog({ children }: React.PropsWithChildren) {
	const [isDialogOpen, setIsDialogOpen] = useState(false);
	const [password, setPassword] = useState("");

	const [signUpData, signUpAction, signUpPending] = useActionState(
		handleSignUp,
		undefined,
	);
	const [LogInData, logInAction, logInPending] = useActionState(
		handleLogIn,
		undefined,
	);
	const { toast } = useToast();

	const openConnectDialog = () => {
		setIsDialogOpen(true);
	};

	async function handleSignUp(_previousState: unknown, formData: FormData) {
		const email = formData.get("email") as string;
		const displayName = formData.get("displayName") as string;

		setPassword("");

		const result = await ManualSignUp(email, password, displayName);

		if (result.status === "success") {
			toast({
				title: result?.title,
				description: result?.description,
				variant: (result?.variant as "default" | "destructive") || "default",
			});
			setPassword("");
			setIsDialogOpen(false);
		} else {
			toast({
				title: result?.title,
				description: result?.description,
				variant: (result?.variant as "default" | "destructive") || "default",
			});
			return { email: email, displayName: displayName };
		}
	}

	async function handleLogIn(_previousState: unknown, formData: FormData) {
		const email = formData.get("email") as string;

		const result = await ManualLogIn(email, password);

		if (result.status === "success") {
			toast({
				title: result.title,
				description: result.description,
				variant: (result?.variant as "default" | "destructive") || "default",
			});
			setPassword("");
			setIsDialogOpen(false);
		} else {
			toast({
				title: result.title,
				description: result.description,
				variant: (result?.variant as "default" | "destructive") || "default",
			});
			return { email: email };
		}
	}

	return (
		<>
			{/* LogIn Dialog */}
			<Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
				<DialogTrigger asChild>
					<div onClick={openConnectDialog}>{children}</div>
				</DialogTrigger>
				<DialogContent className="sm:max-w-[425px]">
					<DialogHeader>
						<DialogTitle>Connexion</DialogTitle>
					</DialogHeader>
					<AuthForms
						intitialExistingUser={true}
						password={password}
						setPassword={setPassword}
						signUpAction={signUpAction}
						logInAction={logInAction}
						signUpData={signUpData}
						logInData={LogInData}
						signUpPending={signUpPending}
						logInPending={logInPending}
					/>
				</DialogContent>
			</Dialog>
		</>
	);
}
