"use client";

import LogOutButton from "../components/ui/logOutButton";
import AccountMenu from "../User/AccountMenu";
import { Button } from "@/components/ui/button";
import DeleteUserAccountDialog from "./DeleteAccount";
import {
	Sheet,
	SheetContent,
	SheetDescription,
	SheetHeader,
	SheetTitle,
	SheetTrigger,
} from "@/components/ui/sheet";

export default function UserAccountSheet({
	triggerElement,
}: {
	triggerElement: React.ReactNode;
}) {
	return (
		<Sheet>
			<SheetTrigger>{triggerElement}</SheetTrigger>
			<SheetContent
				side="left"
				className="flex h-full flex-col overflow-y-auto bg-white max-sm:w-screen"
			>
				<SheetHeader>
					<SheetTitle>
						<span className="bg-allo-grad font-primary text-t-15 inline-block bg-clip-text font-semibold text-transparent uppercase">
							Mon compte
						</span>
					</SheetTitle>
					<SheetDescription>
						Consultez vos informations et vos interventions.
					</SheetDescription>
				</SheetHeader>

				<AccountMenu />

				<div className="mt-auto flex flex-col justify-between gap-2 lg:flex-row lg:gap-4">
					<LogOutButton />
					<DeleteUserAccountDialog>
						<Button variant="destructive">Supprimer mon compte</Button>
					</DeleteUserAccountDialog>
				</div>
			</SheetContent>
		</Sheet>
	);
}
