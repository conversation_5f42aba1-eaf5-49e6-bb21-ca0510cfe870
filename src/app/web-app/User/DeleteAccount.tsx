import { useState, useActionState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/toast/use-toast";
import { DeleteUserAccount } from "./ManageUserFunctions";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
	DialogTrigger,
	DialogFooter,
} from "@/components/ui/dialog";

export default function DeleteUserAccountDialog({
	children,
}: React.PropsWithChildren) {
	const [isDeleteAccountOpen, setIsDeleteAccountOpen] =
		useState<boolean>(false);
	const { toast } = useToast();

	const [deleteAccountData, deleteAccountAction, deleteAccountPending] =
		useActionState(handleDeleteAccount, undefined);

	async function handleDeleteAccount(
		_previousState: unknown,
		formData: FormData,
	) {
		const deleteConfirmation = formData.get("delete") as string;

		if (deleteConfirmation == "SUPPRIMER") {
			const result = await DeleteUserAccount();

			if (result?.status === "success") {
				toast({
					title: result?.title,
					description: result?.description,
					variant: (result?.variant as "default" | "destructive") || "default",
				});
				setIsDeleteAccountOpen(false);
			} else {
				toast({
					title: result?.title,
					description: result?.description,
					variant: (result?.variant as "default" | "destructive") || "default",
				});
				return { delete: deleteConfirmation };
			}
		} else {
			toast({
				title: "Confirmation non valide",
				description:
					"Veuillez saisir le mot SUPPRIMER en majuscules et sans espaces.",
				variant: "destructive",
			});
			return { delete: deleteConfirmation };
		}
	}

	const handleReturnButton = (e: React.MouseEvent<HTMLButtonElement>) => {
		e.preventDefault();

		setIsDeleteAccountOpen(false);
		return;
	};

	return (
		<>
			<Dialog open={isDeleteAccountOpen} onOpenChange={setIsDeleteAccountOpen}>
				<DialogTrigger asChild>
					<div onClick={() => setIsDeleteAccountOpen(true)}>{children}</div>
				</DialogTrigger>
				<DialogContent className="sm:max-w-[425px]">
					<DialogHeader>
						<DialogTitle>Suppression de votre compte</DialogTitle>
						<DialogDescription>
							En supprimant votre compte, vous supprimez toutes vos informations
							de notre base de données. Il n&apos;est pas possible de revenir en
							arrière.
						</DialogDescription>
					</DialogHeader>
					<form action={deleteAccountAction} className="grid gap-4 py-4">
						<div className="grid gap-4 py-4">
							<div className="flex flex-col items-start gap-4">
								<Label htmlFor="delete account" className="text-right">
									Pour le confirmer, saisissez « SUPPRIMER »
								</Label>
								<Input
									id="delete account"
									name="delete"
									className="col-span-3"
									placeholder="SUPPRIMER"
									defaultValue={deleteAccountData?.delete}
								/>
							</div>
						</div>
						<DialogFooter>
							<div className="flex gap-4">
								<Button
									variant="destructive"
									type="submit"
									disabled={deleteAccountPending}
								>
									Supprimer
								</Button>
								<Button variant="secondary" onClick={handleReturnButton}>
									Retourner
								</Button>
							</div>
						</DialogFooter>
					</form>
				</DialogContent>
			</Dialog>
		</>
	);
}
