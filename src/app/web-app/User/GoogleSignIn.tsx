import { useEffect, useState } from "react";
import {
	GoogleAuthProvider,
	signInWithRedirect,
	getRedirectResult,
} from "firebase/auth";
import { auth, db } from "../../lib/firebaseConfig";
import { addDoc, collection } from "firebase/firestore";
import { useToast } from "@/components/ui/toast/use-toast";
import Image from "next/image";
import GoogleIcon from "/public/uploads/icons/Google__G__logo.svg.png";

export default function GoogleSignInComponent() {
	const [googleSignInStarted, setGoogleSignInStarted] = useState(false);
	const { toast } = useToast();

	const handleGoogleSignIn = async () => {
		const provider = new GoogleAuthProvider();
		auth.useDeviceLanguage();

		// Initiates the redirect
		signInWithRedirect(auth, provider);

		try {
			setGoogleSignInStarted(true); // Set the flag before initiating redirect.
			await signInWithRedirect(auth, provider);
		} catch (error) {
			console.error("Error during Google sign-in: ", error);
			setGoogleSignInStarted(false);
			toast({
				title: "Erreur lors de la connexion Google",
				description: (error as Error).message,
				variant: "destructive",
			});
		}
	};

	useEffect(() => {
		const fetchRedirectResult = async () => {
			if (!googleSignInStarted) return;

			try {
				const result = await getRedirectResult(auth);

				if (result) {
					const user = result.user;

					// Save user details in Firestore if needed
					await addDoc(collection(db, "users"), {
						displayName: user.displayName,
						email: user.email,
						photoURL: user.photoURL,
						userId: user.uid,
					});

					toast({
						title: `Bienvenue ${user.displayName || "Utilisateur"}`,
						description: "Vous êtes maintenant connecté(e).",
					});
				} else {
					console.warn("No redirect result found.");
				}
			} catch (error) {
				console.error("Error fetching redirect result: ", error);
				toast({
					title: "Erreur lors de la connexion Google",
					description: (error as Error).message,
					variant: "destructive",
				});
			} finally {
				setGoogleSignInStarted(false);
			}
		};
		fetchRedirectResult();
	}, [googleSignInStarted, toast]);

	return (
		<div
			className="flex h-8 w-24 cursor-pointer items-center justify-center rounded-md shadow hover:shadow-md"
			onClick={handleGoogleSignIn}
		>
			<div className="relative size-4 object-contain">
				<Image
					src={GoogleIcon}
					alt="Google sign in icon"
					fill
					sizes="max-width: 100px"
				/>
			</div>
		</div>
	);
}
