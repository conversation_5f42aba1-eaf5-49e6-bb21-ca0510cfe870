"use client";

import { z } from "zod";
import { useForm } from "react-hook-form";

import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";

import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { zodResolver } from "@hookform/resolvers/zod";
import { UserProfileDetails } from "./AccountMenu";
import { useToast } from "@/components/ui/toast/use-toast";
import { UpdateUserProfile } from "./ManageUserFunctions";
import ResetPasswordDialog from "./pwdReset";

const profilFormSchema = z.object({
	displayName: z
		.string()
		.trim()
		.min(1, { message: "Veuillez saisir votre nom" })
		.max(100, { message: "Veuillez indiquer un nom plus court" }),
	email: z
		.string()
		.email({ message: "Veuillez saisir une adresse email valide" }),
	phone: z
		.union([
			z
				.string()
				.trim()
				.transform((val) => val.replace(/\s/g, ""))
				.refine(() => /^\+?\d{10,15}$/, {
					message: "Veuillez saisir un numéro de téléphone valide",
				}),

			z.string().length(0),
		])
		.optional(),
});

type UserFormDetails = z.infer<typeof profilFormSchema>;

export default function UserProfileSettings({
	userProfileDetails,
	setUserProfileDetails,
}: {
	userProfileDetails: UserProfileDetails | undefined;
	setUserProfileDetails: (userProfileDetails: UserProfileDetails) => void;
}) {
	const { toast } = useToast();

	const form = useForm<UserProfileDetails>({
		resolver: zodResolver(profilFormSchema),
		defaultValues: {
			displayName: userProfileDetails?.displayName || "",
			email: userProfileDetails?.email || "",
			phone: userProfileDetails?.phone || "",
		},
	});

	const onSubmit = async (values: UserFormDetails) => {
		const result = await UpdateUserProfile(values);

		if (result?.status === "success") {
			toast({
				title: result?.title,
				description: result?.description,
				variant: (result?.variant as "default" | "destructive") || "default",
			});
			setUserProfileDetails(values);
		} else {
			toast({
				title: result?.title,
				description: result?.description,
				variant: (result?.variant as "default" | "destructive") || "default",
			});
		}
		return () => form.reset(userProfileDetails || {});
	};

	return (
		<div className="mx-0.5">
			<Form {...form}>
				<form
					onSubmit={form.handleSubmit(onSubmit)}
					className="space-y-5 lg:space-y-8"
				>
					{/* Display Name */}
					<FormField
						control={form.control}
						name="displayName"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Nom complet</FormLabel>
								<FormControl>
									<Input {...field} autoComplete="given-name" />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* Email */}
					<FormField
						control={form.control}
						name="email"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Email</FormLabel>
								<FormControl>
									<Input {...field} autoComplete="email" />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					{/* Phone */}
					<FormField
						control={form.control}
						name="phone"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Numéro de téléphone</FormLabel>
								<FormControl>
									<Input {...field} autoComplete="tel" />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>

					<Button className="w-full text-xs md:text-sm" type="submit">
						Mettre à jour
					</Button>
				</form>
			</Form>
			<ResetPasswordDialog>
				<Button className="mt-4 w-full text-xs md:text-sm" variant="secondary">
					Réinitialiser le mot de passe
				</Button>
			</ResetPasswordDialog>
		</div>
	);
}
