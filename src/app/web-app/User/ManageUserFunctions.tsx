import { auth, db } from "../../lib/firebaseConfig";
import { FirebaseError } from "firebase/app";
import { User } from "firebase/auth";
import { deleteDoc, doc, updateDoc, getDoc } from "firebase/firestore";
import {
	sendPasswordResetEmail,
	deleteUser,
	onAuthStateChanged,
	updateEmail,
} from "firebase/auth";
import { UserProfileDetails } from "./AccountMenu";

function handleErrors(error: unknown) {
	let description = "Une erreur inconnue s'est produite"; // Default error message

	if ((error as FirebaseError).code === "auth/requires-recent-login") {
		description =
			"Votre dernière connexion n'a pas été effectuée récemment. Veuillez vous connecter à nouveau et réessayer l'action.";
	}
	if ((error as FirebaseError).code === "auth/invalid-email") {
		description = "L'email fourni n'est pas valide. Vérifiez votre saisie.";
	} else if ((error as FirebaseError).code === "auth/user-not-found") {
		description = "L'utilisateur n'existe pas dans notre système.";
	} else {
		// For all other errors, fallback to the error's message or the default
		description = (error as Error).message || description;
	}
	return description;
}

export const ResetPasswordEmail = async (email: string) => {
	try {
		await sendPasswordResetEmail(auth, email);

		return {
			title: "Un email de réinitialisation de mot de passe vous a été envoyé",
			description:
				"Veuillez vérifier votre boîte de réception et cliquez sur le lien pour confirmer.",
			variant: "default",
			status: "success",
		};
	} catch (error) {
		const description = handleErrors(error);
		return {
			title: "Erreur lors de la réinitialisation du mot de passe",
			description: description,
			variant: "destructive",
			status: "failed",
		};
	}
};

export async function DeleteUserAccount() {
	try {
		const user = await new Promise<User | undefined>((resolve, reject) => {
			const unsubscribe = onAuthStateChanged(auth, (user) => {
				if (user) resolve(user);
				else reject(new Error("Utilisateur non authentifié"));
				unsubscribe?.();
			});
		});

		if (user && user.uid) {
			await deleteDoc(doc(db, "users", user.uid));
			await deleteUser(user);

			return {
				title: "Votre compte a été supprimé avec succès",
				description:
					"Si ce n'était pas votre intention, n'hésitez pas à vous réinscrire.",
				variant: "default",
				status: "success",
			};
		} else {
			return {
				title: "Erreur lors de la suppression de votre compte",
				description: "Vous devez être connecté pour effectuer cette opération.",
				variant: "destructive",
				status: "failed",
			};
		}
	} catch (error) {
		const description = handleErrors(error);
		return {
			title: "Erreur lors de la suppression du compte utilisateur",
			description: description,
			variant: "destructive",
			status: "failed",
		};
	}
}

export const UpdateUserProfile = async (values: UserProfileDetails) => {
	try {
		const user = auth.currentUser;
		if (!user) {
			return {
				title: "Erreur lors de la mise à jour de votre profil",
				description: "Vous devez être connecté pour effectuer cette opération.",
				variant: "destructive",
				status: "failed",
			};
		} else {
			if (values.email !== user.email) await updateEmail(user, values.email);

			const userRef = doc(db, "users", user.uid);
			await updateDoc(userRef, values);

			return {
				title: "Votre compte a été mis à jour avec succès",
				description:
					"Assurez-vous que les nouvelles informations sont correctes.",
				variant: "default",
				status: "success",
			};
		}
	} catch (error) {
		const description = handleErrors(error);
		return {
			title: "Erreur lors de la mise à jour de votre profil",
			description: description,
			variant: "destructive",
			status: "failed",
		};
	}
};

type UserStreetAddressDetails = {
	address_1: string;
	address_2?: string;
	code_postal: string;
	company?: string;
	siren?: string;
};

export const updateUserStreetAddress = async (
	values: UserStreetAddressDetails,
	clientType: "particulier" | "entreprise",
	docId: string | undefined,
) => {
	try {
		const user = auth.currentUser;
		if (!user || !docId) {
			return {
				title: "Erreur lors de la mise à jour de votre profil",
				description: "Vous devez être connecté pour effectuer cette opération.",
				variant: "destructive",
				status: "failed",
			};
		} else {
			// Check if the information is the same before updating
			const docRef = doc(db, "interventions", docId);
			const docSnap = await getDoc(docRef);

			if (docSnap.exists()) {
				const currentData = docSnap.data();

				const isChanged =
					currentData.address_1 !== values.address_1 ||
					currentData.address_2 !== values.address_2 ||
					currentData.code_postal !== values.code_postal ||
					currentData.company !== values.company ||
					currentData.siren !== values.siren ||
					currentData.clientType !== clientType;

				if (isChanged) {
					await updateDoc(docRef, { ...values, clientType });

					return {
						title: "Votre adresse a été mise à jour avec succès",
						description:
							"Assurez-vous que les nouvelles informations sont correctes.",
						variant: "default",
						status: "success",
					};
				} else {
					return {
						status: "success",
					};
				}
			} else {
				return {
					title: "Erreur lors de la mise à jour de votre adresse de domicile",
					description: "L'intervention n'a pas été trouvée.",
					variant: "destructive",
					status: "failed",
				};
			}
		}
	} catch (error) {
		const description = handleErrors(error);
		return {
			title: "Erreur lors de la mise à jour de votre adresse de domicile",
			description: description,
			variant: "destructive",
			status: "failed",
		};
	}
};
