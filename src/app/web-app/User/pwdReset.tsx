import { useState, useActionState, useEffect } from "react";
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON>eader,
	<PERSON>alogTitle,
	DialogTrigger,
	DialogDescription,
	DialogFooter,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/ui/toast/use-toast";
import { ResetPasswordEmail } from "./ManageUserFunctions";
import { auth } from "@/lib/firebaseConfig";
import { onAuthStateChanged } from "firebase/auth";

export default function ResetPasswordDialog({
	children,
}: React.PropsWithChildren) {
	const [initialEmail, setInitialEmail] = useState<string>("");
	const [isOpen, setIsOpen] = useState<boolean>(false);
	const { toast } = useToast();

	const [resetPasswordData, resetPasswordAction, resetPasswordPending] =
		useActionState(handleResetPassword, undefined);

	useEffect(() => {
		const unsubscribe = onAuthStateChanged(auth, (user) => {
			if (user && user.email) {
				setInitialEmail(user.email);
			}
		});
		return () => unsubscribe();
	}, []);

	async function handleResetPassword(
		_previousState: unknown,
		formData: FormData,
	) {
		const email = formData.get("email") as string;

		const result = await ResetPasswordEmail(email);

		if (result.status == "success") {
			toast({
				title: result.title,
				description: result.description,
				variant: (result?.variant as "default" | "destructive") || "default",
			});
			setIsOpen(false);
		} else {
			toast({
				title: result.title,
				description: result.description,
				variant: (result?.variant as "default" | "destructive") || "default",
			});
			return { email: email };
		}
	}

	return (
		<>
			<Dialog open={isOpen} onOpenChange={setIsOpen}>
				<DialogTrigger asChild>
					<div onClick={() => setIsOpen}>{children}</div>
				</DialogTrigger>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Réinitialisation du mot de passe</DialogTitle>
						<DialogDescription>
							Recevoir un email pour réinitialiser le mot de passe de mon compte
						</DialogDescription>
					</DialogHeader>
					<form action={resetPasswordAction} className="grid gap-4 py-4">
						<div className="grid gap-4 py-4">
							<div className="grid grid-cols-4 items-center gap-4">
								<Label htmlFor="email" className="text-right">
									Email
								</Label>
								<Input
									id="email"
									name="email"
									type="email"
									autoComplete="email"
									className="col-span-3"
									defaultValue={resetPasswordData?.email ?? initialEmail}
									required
								/>
							</div>
						</div>
						<DialogFooter>
							<Button type="submit" disabled={resetPasswordPending}>
								Réinitialiser le mot de passe
							</Button>
						</DialogFooter>
					</form>
				</DialogContent>
			</Dialog>
		</>
	);
}
