import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import {
	getCookie,
	setCookie,
	deleteCookie,
	hasCookie,
	getCookies,
} from "cookies-next/server";

export async function middleware(req: NextRequest) {
	const res = NextResponse.next();
	await setCookie("test", "value", { res, req });
	await hasCookie("test", { req, res });
	await deleteCookie("test", { res, req });
	await getCookie("test", { res, req });
	await getCookies({ res, req });

	return res;
}
