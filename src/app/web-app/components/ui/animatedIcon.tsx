"use client";

import { useEffect, useRef } from "react";
import { Player } from "@lordicon/react";

export default function AnimatedIcon({
	icon,
	shouldPlay,
	size,
}: {
	icon: Record<string, unknown>;
	shouldPlay: boolean;
	size: number;
}) {
	const playerRef = useRef<Player>(null);

	useEffect(() => {
		if (shouldPlay) playerRef.current?.playFromBeginning();
	}, [shouldPlay]);

	return (
		<Player
			ref={playerRef}
			icon={icon}
			size={size}
			//onComplete={() => playerRef.current?.playFromBeginning()}
		/>
	);
}
