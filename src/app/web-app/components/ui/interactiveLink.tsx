"use client";

import Link from "next/link";
import { trackEvent } from "@/lib/analytics";

export default function InteractiveLink({
	href,
	className,
	linkText,
	onClickEventName,
	onClickLocation,
}: {
	href: string;
	className: string;
	linkText: string;
	onClickEventName: string;
	onClickLocation: string;
}) {
	return (
		<Link
			href={href}
			className={className}
			onClick={() =>
				trackEvent(onClickEventName, {
					location: { onClickLocation },
				})
			}
		>
			{linkText}
		</Link>
	);
}
