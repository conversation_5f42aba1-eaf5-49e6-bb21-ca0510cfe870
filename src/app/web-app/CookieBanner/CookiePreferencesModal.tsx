"use client";

import { useState } from "react";
import <PERSON><PERSON> from "js-cookie";
import { FaCheck, FaTimes, FaLock } from "react-icons/fa";
import PolicyDialog from "../TermsAndConditions/PolitiqueDeConfidentialite";
//import { trackEvent } from "@/lib/analytics";

export default function CookiePreferencesModal({
	isOpen,
	onClose,
}: {
	isOpen: boolean;
	onClose: () => void;
}) {
	const [analytics, setAnalytics] = useState(
		Cookie.get("analytics") === "true",
	);
	const [marketing, setMarketing] = useState(
		Cookie.get("marketing") === "true",
	);

	if (!isOpen) return null;

	const savePreferences = () => {
		if (analytics) {
			// Enable or disable analytics based on user preference
			Cookie.set("firebase-analytics-enabled", String(analytics), {
				expires: 365,
			});
		}
		Cookie.set("analytics", String(analytics), { expires: 365 });
		Cookie.set("marketing", String(marketing), { expires: 365 });
		Cookie.set("cookieConsent", "custom", { expires: 365 });
		onClose();
	};

	const acceptAll = () => {
		setAnalytics(true);
		setMarketing(true);
		Cookie.set("analytics", "true", { expires: 365 });
		Cookie.set("marketing", "true", { expires: 365 });
		Cookie.set("cookieConsent", "all", { expires: 365 });
		onClose();
	};

	const rejectAll = () => {
		setAnalytics(false);
		setMarketing(false);
		Cookie.set("analytics", "false", { expires: 365 });
		Cookie.set("marketing", "false", { expires: 365 });
		Cookie.set("cookieConsent", "none", { expires: 365 });
		onClose();
	};

	return (
		<div className="fixed inset-0 z-10 flex items-center justify-center bg-black/50 backdrop-blur-sm">
			<div className="relative w-[90%] max-w-3xl rounded-md bg-white p-6 shadow-lg">
				<button
					className="absolute top-4 right-4 text-sm font-semibold text-gray-500 hover:text-black"
					onClick={onClose}
				>
					Fermer
				</button>

				<h2 className="mb-2 text-xl font-bold">
					Panneau de gestion des cookies
				</h2>
				<p className="mb-4 text-sm text-gray-700">
					En autorisant ces services tiers, vous acceptez le dépôt et la lecture
					de cookies et l&apos;utilisation de technologies de suivi nécessaires
					à leur bon fonctionnement.
				</p>

				<PolicyDialog>
					<p className="z-20 mb-4 flex cursor-pointer items-center gap-2 text-xs text-gray-400">
						<FaLock /> Politique de confidentialité
					</p>
				</PolicyDialog>

				{/* Section d'action globale */}
				<div className="mb-6 flex gap-2">
					<button
						onClick={acceptAll}
						className="flex items-center gap-2 rounded bg-gray-700 px-4 py-2 text-sm text-white"
					>
						<FaCheck />
						Tout accepter
					</button>
					<button
						onClick={rejectAll}
						className="flex items-center gap-2 rounded border border-gray-400 px-4 py-2 text-sm text-gray-700"
					>
						<FaTimes />
						Tout refuser
					</button>
				</div>

				<hr className="my-4" />

				{/* Cookies obligatoires */}
				<div className="mb-6">
					<p className="mb-1 font-medium">Cookies obligatoires</p>
					<p className="text-sm text-gray-600">
						Ces cookies sont nécessaires au bon fonctionnement du site. Ils ne
						peuvent pas être désactivés.
					</p>
					<p className="mt-1 text-xs text-gray-400">
						Exemples : has_js, allocvc_cookie, cw_id
					</p>
				</div>

				{/* Analytics */}
				<div className="mb-6">
					<p className="mb-1 font-medium">Mesure d&apos;audience</p>
					<p className="text-sm text-gray-600">
						Google Analytics - Ce service peut déposer jusqu&apos;à 10 cookies.
					</p>
					<div className="mt-2 flex gap-2">
						<button
							onClick={() => setAnalytics(true)}
							className={`flex items-center gap-2 rounded px-4 py-2 text-sm ${
								analytics
									? "bg-gray-700 text-white"
									: "border border-gray-300 bg-white"
							}`}
						>
							<FaCheck />
							Autoriser
						</button>
						<button
							onClick={() => setAnalytics(false)}
							className={`flex items-center gap-2 rounded px-4 py-2 text-sm ${
								!analytics
									? "bg-gray-200 text-gray-700"
									: "border border-gray-300 bg-white"
							}`}
						>
							<FaTimes />
							Interdire
						</button>
					</div>
				</div>

				{/* Marketing */}
				<div className="mb-6">
					<p className="mb-1 font-medium">Cookies marketing</p>
					<p className="text-sm text-gray-600">
						Facebook Pixel, Google Ads… Ces services permettent de vous proposer
						des publicités adaptées.
					</p>
					<div className="mt-2 flex gap-2">
						<button
							onClick={() => setMarketing(true)}
							className={`flex items-center gap-2 rounded px-4 py-2 text-sm ${
								marketing
									? "bg-gray-700 text-white"
									: "border border-gray-300 bg-white"
							}`}
						>
							<FaCheck />
							Autoriser
						</button>
						<button
							onClick={() => setMarketing(false)}
							className={`flex items-center gap-2 rounded px-4 py-2 text-sm ${
								!marketing
									? "bg-gray-200 text-gray-700"
									: "border border-gray-300 bg-white"
							}`}
						>
							<FaTimes />
							Interdire
						</button>
					</div>
				</div>

				<div className="text-right">
					<button
						onClick={savePreferences}
						className="bg-allo-blue rounded px-6 py-2 text-sm text-white shadow"
					>
						Enregistrer mes choix
					</button>
				</div>
			</div>
		</div>
	);
}
