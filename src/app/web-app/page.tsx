import Hero from "./Hero/Hero";
import SectionReservation from "./Reservation/Reservation";
import SectionAboutUs from "./AboutUs/AboutUs";
import SectionTestimonials from "./Testimonial/Testimonials";
import SectionFAQ from "./FAQ/FAQ";
import SectionUSP from "./USP/USP";
import ContactForm from "./Contact/ContactForm";
import CookieBanner from "./CookieBanner/CookieBanner";
//import Cookies from "../pages/Cookies";
//import CookiePreferencesModal from "./CookieBanner/CookiePreferencesModal";

export default function Home() {
	return (
		<>
			<article className="mx-auto flex h-fit w-11/12 max-w-screen-2xl flex-col lg:w-4/5">
				<Hero />
				<SectionReservation />
				<SectionUSP />
				<SectionAboutUs />
				<SectionTestimonials />
				<SectionFAQ />
				<ContactForm />
				{/* <Cookies /> */}
			</article>

			<CookieBanner />
			{/* <PolitiqueModal /> */}

			{/* <CookiePreferencesModal /> */}
		</>
	);
}
