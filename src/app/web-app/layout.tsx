import type { Metadata } from "next";
import NavBar from "./Header/NavBar";
import "../globals.css";
import Footer from "./Footer/Footer";
import { Toaster } from "@/components/ui/toast/toaster";
import type { Viewport } from "next";
import AnalyticsProvider from "./AnalyticsProvider";

export const viewport: Viewport = {
	width: "device-width",
	initialScale: 1.0,
	maximumScale: 1.0,
	userScalable: false,
};

export const metadata: Metadata = {
	title: "alloCVC - Entretien Clim, Chaudière, Pompe à Chaleur",
	description:
		"Votre partenaire pour l'entretien de votre climatisation, votre chaudière et votre pompe à chaleur",
	icons: {
		// Favicon
		icon: [
			{ url: "/favicon.ico", sizes: "any" },
			{ url: "/icons/icon.svg", type: "image/svg+xml" },
			{ url: "/icons/icon.png", sizes: "192x192", type: "image/png" },
		],
		// Apple Touch Icon
		apple: "/icons/apple-touch-icon.png",
		// Shortcut icon (optional)
		shortcut: "/favicon.ico",
		// Other icons
		other: [
			{
				rel: "mask-icon",
				url: "/icons/icon-mask.png",
				color: "#C024B6",
			},
		],
	},
	// Web App Manifest (optional but recommended for PWA)
	manifest: "/manifest.json",
};

export default function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<html lang="en">
			<body className="flex max-w-full flex-col items-center">
				<AnalyticsProvider>
					<NavBar />
					{children}
					<Toaster />
					<Footer />
				</AnalyticsProvider>
			</body>
		</html>
	);
}
