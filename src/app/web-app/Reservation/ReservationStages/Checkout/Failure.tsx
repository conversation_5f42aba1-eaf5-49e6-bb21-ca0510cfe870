import FailureIcon from "../../../../../public/uploads/icons/wired-lineal-25-error-cross-hover-pinch.json";
import { AnimatedIcon } from "@/lib/animatedIconPlayer";
import ReserveAgainButton from "./ReserveAgainButton";

export default function Failure({
	technicalError,
	goToStage,
}: {
	technicalError: boolean;
	goToStage: (stage: number) => void;
}) {
	return (
		<section
			id="failure"
			className="box-border flex w-full max-w-96 flex-col items-center justify-center p-5 lg:mb-10"
		>
			<AnimatedIcon icon={FailureIcon} shouldPlay={true} size={100} />
			<h3 className="font-primary text-allo-dark-grey mt-2 mb-10 text-center text-2xl font-bold lg:text-3xl">
				{technicalError
					? "Une erreur technique s'est produite"
					: "Erreur lors du paiement"}
			</h3>
			<p className="font-primary text-allo-dark-grey mb-3 text-center text-sm font-normal lg:text-base">
				{technicalError
					? "Désolé, une erreur inattendue a été rencontrée. Notre équipe travaille à la résolution du problème. Veuillez réessayer plus tard."
					: "Désolé, une erreur est survenue lors du processus de paiement de votre intervention. Veuillez réessayer ou contacter notre équipe si le problème persiste."}
			</p>
			<ReserveAgainButton goToStage={goToStage} />
		</section>
	);
}
