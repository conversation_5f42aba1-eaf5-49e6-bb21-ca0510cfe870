"use client";

import { useEffect, useState, useCallback } from "react";
import { doc, getDoc, updateDoc } from "firebase/firestore";
import { db } from "@/lib/firebaseConfig";
import { LoadingSpinner } from "@/components/ui/loadingSpinner";
import { FirebaseError } from "firebase/app";
import { useToast } from "@/components/ui/toast/use-toast";
import Failure from "./Failure";
import Success from "./Success";

type stageProps = {
	sessionId: string | undefined;
	docId: string | undefined;
	goToStage: (stage: number) => void;
};

export default function Return({ sessionId, docId, goToStage }: stageProps) {
	const [status, setStatus] = useState<string | undefined>(undefined);
	const [customerEmail, setCustomerEmail] = useState<string | undefined>(
		undefined,
	);
	const { toast } = useToast();

	const updateIntervention = useCallback(
		async (paymentStatus: string) => {
			try {
				if (!docId) return;
				const docRef = doc(db, "interventions", docId);
				const docSnap = await getDoc(docRef);

				if (docSnap.exists()) {
					const updateData: Partial<{ paymentStatus: string }> = {
						paymentStatus:
							process.env.NODE_ENV !== "production" &&
							paymentStatus === "complete"
								? "test_complete"
								: paymentStatus,
					};

					await updateDoc(docRef, updateData);
				}
			} catch (error) {
				toast({
					title: "Erreur lors de l'enregistrement du paiement",
					description:
						(error as FirebaseError).code ||
						"Une erreur s'est produite lors de la mise à jour du paiement.",
					variant: "destructive",
				});
			}
		},
		[docId, toast],
	);

	useEffect(() => {
		const fetchSessionDetails = async () => {
			if (!sessionId) return;

			try {
				const result = await fetch(
					`/api/checkout_sessions?session_id=${sessionId}`,
				);
				const data = await result.json();

				if (data.error)
					throw Error(
						"Erreur provenant de l'interface de paiement Stripe: ",
						data.error,
					);

				if (data.status) {
					setStatus(data.status);
					setCustomerEmail(data.customer_email);

					await updateIntervention(data.status);
				}
			} catch (error) {
				toast({
					title: "Erreur lors du paiement",
					description:
						(error as Error).message ||
						"Erreur provenant de l'interface de paiement Stripe",
					variant: "destructive",
				});
			}
		};

		fetchSessionDetails();
	}, [sessionId, docId, updateIntervention, toast]);

	if (!status) return <LoadingSpinner />;

	if (status === "open") {
		return <LoadingSpinner />;
	} else if (status === "complete") {
		return <Success customerEmail={customerEmail} goToStage={goToStage} />;
	} else if (status === "expired") {
		return <Failure technicalError={false} goToStage={goToStage} />;
	}

	return undefined;
}
