import { useCallback, useEffect, useState, useActionState } from "react";
import { auth, db } from "@/lib/firebaseConfig";
import { doc, getDoc } from "firebase/firestore";
import { onAuthStateChanged } from "firebase/auth";
import { useToast } from "@/components/ui/toast/use-toast";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { updateUserStreetAddress } from "@/app/User/ManageUserFunctions";
import { z } from "zod";
import { cn } from "@/lib/utils";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

const streetAddressFormSchema = z.object({
	address_1: z
		.string()
		.min(1, "Veuillez saisir une adresse")
		.max(100, "Veuillez saisir une adresse moins longue"),
	address_2: z
		.string()
		.max(100, "Veuillez saisir une adresse moins longue")
		.optional(),
	code_postal: z
		.string()
		.min(1, "Veuillez saisir un code postal")
		.max(20, "Veuillez saisir un code postal valide"),
	company: z
		.string()
		.max(100, "Veuillez saisir un nom de société moins long")
		.optional(),
	siren: z.string().max(50, "Veuillez saisir un SIREN valide").optional(),
});

type UserAddressDetails = {
	address_1: string;
	address_2: string;
	code_postal: string[];
	commune: string;
	department: string;
	clientType: "particulier" | "entreprise";
	company?: string;
	siren?: string;
};

export default function Adresse({
	nextStage,
	docId,
}: {
	nextStage: () => void;
	docId: string | undefined;
}) {
	const [userAddressDetails, setUserAddressDetails] = useState<
		UserAddressDetails | undefined
	>(undefined);
	const [clientType, setClientType] = useState<"particulier" | "entreprise">(
		"particulier",
	);
	const [userAddressData, userAddressAction, userAddressPending] =
		useActionState(handleUserAddress, undefined);
	const [selectedCodePostal, setSelectedCodePostal] = useState<
		string | undefined
	>(undefined);
	const { toast } = useToast();

	useEffect(() => {
		if (userAddressDetails?.code_postal.length === 1) {
			setSelectedCodePostal(userAddressDetails.code_postal[0]);
		}
	}, [userAddressDetails]);

	const fetchUserAddressDetails = useCallback(async () => {
		const unsubscribe = onAuthStateChanged(auth, async (user) => {
			if (user && docId) {
				try {
					const docRef = doc(db, "interventions", docId);
					const docSnap = await getDoc(docRef);

					if (docSnap.exists()) {
						const data = docSnap.data();
						setUserAddressDetails({
							address_1: data.address_1 || "",
							address_2: data.address_2 || "",
							code_postal: (data.communeCodePostal || []).map(
								(code: number) => {
									const codeStr = String(code);
									if (
										codeStr.length === 4 &&
										codeStr.startsWith("6") &&
										data.communeDept === "06"
									) {
										return `0${code}`;
									}
									return code;
								},
							),
							commune: data.commune || "",
							department: data.communeDept || "06",
							clientType: data.clientType || "particulier",
							company: data.company || "",
							siren: data.siren || "",
						});

						setClientType(data.clientType || "particulier");
					}
				} catch (error) {
					const _error = error;
					toast({
						title:
							"Erreur lors de la récupération des détails de l'utilisateur",
						description:
							"Veuillez vous connecter à nouveau ou nous contacter si le problème persiste.",
						variant: "destructive",
					});
				}
			} else {
				toast({
					title: "Erreur d'accès",
					description: "Vous devez être connecté pour accéder à votre compte.",
					variant: "destructive",
				});
			}
			return () => unsubscribe();
		});
	}, [toast, docId]);

	useEffect(() => {
		fetchUserAddressDetails();
	}, [fetchUserAddressDetails]);

	async function handleUserAddress(
		_previousState: unknown,
		formData: FormData,
	) {
		const address_1 = formData.get("address_1") as string;
		const address_2 = formData.get("address_2") as string;
		const code_postal =
			(formData.get("code_postal") as string) || selectedCodePostal;
		const company =
			clientType === "entreprise" ? (formData.get("company") as string) : "";
		const siren =
			clientType === "entreprise" ? (formData.get("siren") as string) : "";

		const validatedInput = streetAddressFormSchema.safeParse({
			address_1,
			address_2,
			code_postal,
			company,
			siren,
		});

		if (!validatedInput || !validatedInput.data) {
			toast({
				title: "Erreur de validation",
				description: "Veuillez vérifier les informations saisies.",
				variant: "destructive",
			});
			return { address_1, address_2, company, siren };
		}

		const result = await updateUserStreetAddress(
			validatedInput.data,
			clientType,
			docId,
		);

		if (result?.status === "success") {
			if (result.title) {
				toast({
					title: result.title,
					description: result.description,
					variant: "default",
				});
			}
			nextStage();
		} else {
			toast({
				title: result.title,
				description: result.description,
				variant: "destructive",
			});
			return { address_1, address_2, company, siren };
		}
	}

	return (
		<div className="ADDRESS relative box-border rounded-lg bg-white p-10 shadow sm:w-10/12 md:w-10/12 lg:w-9/12 xl:w-6/12 2xl:w-5/12">
			<div className="flex flex-col space-y-1.5 text-center sm:text-center">
				<h4 className="text-lg leading-none font-semibold tracking-tight">
					L&apos;adresse de l&apos;intervention
				</h4>
				<div className="text-sm text-neutral-500 dark:text-neutral-400">
					Pour que l&apos;intervenant sache où se rendre
				</div>
			</div>
			<form action={userAddressAction} className="grid gap-4 py-4">
				<div className="grid gap-4 py-4">
					<div className="grid min-w-2/3 grid-cols-4 items-center gap-4">
						<Label htmlFor="address_1" className="hidden text-right md:block">
							Adresse 1 *
						</Label>
						<Input
							id="address_1"
							name="address_1"
							type="text"
							autoComplete="street-address"
							className="col-span-4 md:col-span-3 md:placeholder-transparent"
							placeholder="Adresse 1*"
							required
							defaultValue={
								userAddressData?.address_1 || userAddressDetails?.address_1
							}
						/>
						<Label htmlFor="address_2" className="hidden text-right md:block">
							Adresse 2&nbsp;&nbsp;
						</Label>
						<Input
							id="address_2"
							name="address_2"
							type="text"
							className="col-span-4 md:col-span-3 md:placeholder-transparent"
							placeholder="Adresse 2"
							defaultValue={
								userAddressData?.address_2 || userAddressDetails?.address_2
							}
						/>
						<Label htmlFor="commune" className="hidden text-right md:block">
							Commune&nbsp;&nbsp;
						</Label>
						<Input
							id="commune"
							name="commune"
							type="text"
							className="col-span-4 md:col-span-3 md:placeholder-transparent"
							placeholder="Commune"
							defaultValue={userAddressDetails?.commune}
							disabled
						/>
						<Label htmlFor="code_postal" className="hidden text-right md:block">
							Code Postal&nbsp;&nbsp;
						</Label>
						<select
							name="code_postal"
							value={selectedCodePostal}
							onChange={(e) => setSelectedCodePostal(e.target.value)}
							required
							className={cn(
								"border-input border-allo-light-grey bg-background ring-offset-background file:text-foreground placeholder:text-muted-foreground focus-visible:ring-ring col-span-4 flex h-10 w-full rounded-md border px-3 py-2 text-base file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 md:col-span-3 md:text-sm md:placeholder-transparent",
							)}
						>
							<option value="">Choisissez votre code postal</option>
							{userAddressDetails?.code_postal.map((code) => (
								<option key={code} value={code}>
									{code}
								</option>
							))}
						</select>
					</div>

					<RadioGroup
						defaultValue={userAddressDetails?.clientType}
						value={clientType}
						onValueChange={(value) =>
							setClientType(value as "particulier" | "entreprise")
						}
						className="mt-4 flex justify-end gap-4"
					>
						<div className="flex items-center space-x-2">
							<RadioGroupItem
								value="particulier"
								id="particulier"
								className="text-allo-blue"
							/>
							<Label htmlFor="particulier" className="text-xs font-normal">
								Particulier
							</Label>
						</div>
						<div className="flex items-center space-x-2">
							<RadioGroupItem
								value="entreprise"
								id="entreprise"
								className="text-allo-blue"
							/>
							<Label htmlFor="entreprise" className="text-xs font-normal">
								Entreprise
							</Label>
						</div>
					</RadioGroup>

					{clientType === "entreprise" && (
						<>
							<div className="grid grid-cols-4 items-center gap-4">
								<Label htmlFor="company" className="hidden text-right md:block">
									Société
								</Label>
								<Input
									id="company"
									name="company"
									type="text"
									className="col-span-4 text-sm md:col-span-3 md:placeholder-transparent"
									placeholder="Nom de la société"
									defaultValue={
										userAddressData?.company || userAddressDetails?.company
									}
								/>
							</div>
							<div className="grid grid-cols-4 items-center gap-4">
								<Label htmlFor="siren" className="hidden text-right md:block">
									SIREN
								</Label>
								<Input
									id="siren"
									name="siren"
									type="text"
									className="col-span-4 text-sm md:col-span-3 md:placeholder-transparent"
									placeholder="SIREN"
									defaultValue={
										userAddressData?.siren || userAddressDetails?.siren
									}
								/>
							</div>
							<div className="grid grid-cols-4">
								<div></div>
								<p className="col-span-4 text-xs italic md:col-span-3">
									Informations facultatives sur l&apos;entreprise à indiquer sur
									la facture
								</p>
							</div>
						</>
					)}
				</div>
				<div className="flex justify-center">
					<Button
						className="w-full"
						type="submit"
						disabled={userAddressPending}
					>
						Enregistrer
					</Button>
				</div>
			</form>
		</div>
	);
}
