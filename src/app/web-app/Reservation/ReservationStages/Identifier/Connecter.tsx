import { useState, useActionState, useEffect, useCallback } from "react";

import { useToast } from "@/components/ui/toast/use-toast";
import { doc, getDoc, updateDoc } from "firebase/firestore";
import { db, auth } from "../../../../lib/firebaseConfig";
import { ManualSignUp, ManualLogIn } from "../../../User/ManualSignIn";
import { onAuthStateChanged } from "firebase/auth";
import { LoadingSpinner } from "@/components/ui/loadingSpinner";
import Adresse from "./Adresse";
//import GoogleSignInComponent from "./GoogleSignIn"
import { AuthForms } from "@/components/userAuthForms";

type stageProps = {
	nextStage: (id?: string) => void;
	docId: string | undefined;
};
export default function Connecter({ nextStage, docId }: stageProps) {
	const [loading, setLoading] = useState<boolean>(true);
	const [signedInUser, setSignedInUser] = useState<boolean>(false);
	const [password, setPassword] = useState<string>("");

	const [signUpData, signUpAction, signUpPending] = useActionState(
		handleSignUp,
		undefined,
	);
	const [LogInData, logInAction, logInPending] = useActionState(
		handleLogIn,
		undefined,
	);
	const { toast } = useToast();

	const setInterventionUser = useCallback(async () => {
		setLoading(true);

		const unsubscribe = onAuthStateChanged(auth, async (user) => {
			setPassword("");

			if (user && docId) {
				try {
					const docRef = doc(db, "interventions", docId);
					const docSnap = await getDoc(docRef);

					if (docSnap.exists()) {
						await updateDoc(docRef, {
							userId: user.uid,
							userEmail: user.email,
						});
						setSignedInUser(true);
						setLoading(false);
					}
				} catch (error) {
					toast({
						title: "Erreur de mise à jour de l'intervention",
						description: (error as Error).message,
						variant: "destructive",
					});
				}
			} else {
				setSignedInUser(false);
				setLoading(false);
			}
		});
		return () => unsubscribe();
	}, [docId, toast]);

	useEffect(() => {
		setInterventionUser();
	}, [setInterventionUser]);

	async function handleSignUp(_previousState: unknown, formData: FormData) {
		const email = formData.get("email") as string;
		const displayName = formData.get("displayName") as string;

		setPassword("");

		const result = await ManualSignUp(email, password, displayName);

		if (result.status === "success") {
			toast({
				title: result.title,
				description: result.description,
				variant: (result?.variant as "default" | "destructive") || "default",
			});
			try {
				const docRef = doc(db, "interventions", docId!);
				const docSnap = await getDoc(docRef);

				if (docSnap.exists()) {
					await updateDoc(docRef, {
						userId: result.uid,
						userEmail: result.email,
					});
					setSignedInUser(true);
				}
			} catch (error) {
				toast({
					title: "Erreur lors de l'inscription",
					description: (error as Error).message,
					variant: "destructive",
				});
			}
		} else {
			toast({
				title: result.title,
				description: result.description,
				variant: (result?.variant as "default" | "destructive") || "default",
			});
			return {
				email: email,
				displayName: displayName,
			};
		}
	}

	async function handleLogIn(_previousState: unknown, formData: FormData) {
		const email = formData.get("email") as string;

		const result = await ManualLogIn(email, password);

		setPassword("");

		if (result.status === "success") {
			toast({
				title: result.title,
				description: result.description,
				variant: (result?.variant as "default" | "destructive") || "default",
			});
			try {
				const docRef = doc(db, "interventions", docId!);
				const docSnap = await getDoc(docRef);

				if (docSnap.exists()) {
					await updateDoc(docRef, {
						userId: result.uid,
						userEmail: result.email,
					});
					setSignedInUser(true);
				}
			} catch (error) {
				toast({
					title: "Erreur lors de connexion",
					description: (error as Error).message,
					variant: "destructive",
				});
			}
		} else {
			toast({
				title: result.title,
				description: result.description,
				variant: (result?.variant as "default" | "destructive") || "default",
			});
			return { email: email };
		}
	}

	if (loading) {
		return <LoadingSpinner />;
	}

	return (
		<div className="IDENTIFICATION flex w-full flex-col items-center px-2 sm:px-5">
			<h3 className="font-primary text-allo-dark-grey mt-2 mb-10 text-2xl font-bold lg:text-3xl">
				Faites-vous connaître
			</h3>
			{signedInUser ? (
				<Adresse nextStage={nextStage} docId={docId} />
			) : (
				<div className="relative box-border w-full rounded-lg bg-white p-10 shadow sm:w-10/12 md:w-6/12 lg:w-7/12 xl:w-6/12 2xl:w-5/12">
					<AuthForms
						intitialExistingUser={false}
						password={password}
						setPassword={setPassword}
						signUpAction={signUpAction}
						logInAction={logInAction}
						signUpData={signUpData}
						logInData={LogInData}
						signUpPending={signUpPending}
						logInPending={logInPending}
					/>
				</div>
			)}
		</div>
	);
}
