import Tick from "/public/uploads/icons/tickmark_blue_02.svg";
import XrayIcon from "../../../../public/uploads/icons/wired-lineal-1257-x-ray-hover-pinch.json";
import MaintenanceIcon from "../../../../public/uploads/icons/wired-lineal-40-cogs-hover-mechanic.json";
import { db } from "@/lib/firebaseConfig";
import { getDoc, doc, updateDoc } from "firebase/firestore";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { AnimatedIcon } from "@/lib/animatedIconPlayer";

type stageProps = {
	nextStage: (id?: string) => void;
	docId: string | undefined;
};

export default function Intervention({ nextStage, docId }: stageProps) {
	const handleInterventionSelection = async (
		selectedIntervention: "maintenance" | "panne",
	) => {
		if (!docId) {
			console.error(
				"Error: Interventions: Intervention document ID is missing",
			);
			return;
		}

		try {
			const docRef = doc(db, "interventions", docId);
			const docSnap = await getDoc(docRef);

			if (!docSnap.exists()) {
				console.error("Error: Interventions: Document does not exist");
				return;
			}

			// Update only the selected intervention field
			const updateData: Partial<{ maintenance: number; panne: number }> = {
				maintenance: selectedIntervention === "maintenance" ? 1 : 0,
				panne: selectedIntervention === "panne" ? 1 : 0,
			};

			await updateDoc(docRef, updateData);
			nextStage(docId);
		} catch (error) {
			console.error("Error updating intervention:", error);
		}
	};
	return (
		<div className="INTERVENTION flex w-full flex-col items-center px-2 sm:px-5">
			<h3 className="font-primary text-allo-dark-grey mt-2 mb-10 text-center text-2xl font-bold lg:text-start lg:text-3xl">
				Choisissez votre type d&apos;intervention
			</h3>

			<div className="flex h-auto w-full flex-col justify-center gap-8 max-[767px]:items-center md:flex-row md:gap-10 lg:gap-10 lg:px-10 xl:gap-32 xl:px-32">
				{/* Entretien */}
				<div className="flex w-full max-w-96 min-w-64 flex-1 flex-col items-center justify-center rounded-lg bg-white p-6 shadow-md hover:shadow-xl lg:p-10">
					<div className="flex h-full">
						<AnimatedIcon icon={MaintenanceIcon} shouldPlay={true} size={110} />
					</div>
					<div className="flex min-h-20 w-full items-start justify-center rounded-t-lg pt-3">
						<h4 className="font-primary text-allo-blue mt-2 mb-4 text-center leading-tight font-bold uppercase">
							Entretien
						</h4>
					</div>
					<ul className="flex max-w-72 flex-col gap-4 pb-6">
						<li className="font-primary text-allo-dark-grey flex items-start gap-3 text-xs lg:text-sm">
							<Image
								alt="blue tick mark"
								width={100}
								height={100}
								className="box-border size-5 pt-1"
								src={Tick.src}
							/>
							Décarrossage de l&apos;unité intérieure,
						</li>
						<li className="font-primary text-allo-dark-grey flex items-start gap-3 text-xs lg:text-sm">
							<Image
								alt="blue tick mark"
								width={100}
								height={100}
								className="box-border size-5 pt-1"
								src={Tick.src}
							/>
							Nettoyage des filtres,
						</li>
						<li className="font-primary text-allo-dark-grey flex items-start gap-3 text-xs lg:text-sm">
							<Image
								alt="blue tick mark"
								width={100}
								height={100}
								className="box-border size-5 pt-1"
								src={Tick.src}
							/>
							Nettoyage du bac à condensats et des échangeurs.
						</li>
					</ul>
					<div className="flex h-full flex-col justify-end">
						<Button
							className="w-full max-w-72 text-xs md:text-sm"
							onClick={() => handleInterventionSelection("maintenance")}
						>
							Programmer un entretien
						</Button>
					</div>
				</div>

				{/* Dépannage */}
				<div className="flex w-full max-w-96 min-w-64 flex-1 flex-col items-center justify-center rounded-lg bg-white p-6 shadow-md hover:shadow-xl lg:p-10">
					<div className="flex h-full">
						<AnimatedIcon icon={XrayIcon} shouldPlay={true} size={110} />
					</div>
					<div className="flex min-h-20 w-full items-start justify-center rounded-t-lg pt-3">
						<h4 className="font-primary text-allo-blue mt-2 mb-4 text-center leading-tight font-bold uppercase">
							Recherche de panne
							<br />
							et diagnostic
						</h4>
					</div>
					<ul className="flex max-w-72 flex-col gap-4 pb-6">
						<li className="font-primary text-allo-dark-grey flex items-start gap-3 text-sm">
							<Image
								alt="blue tick mark"
								width={100}
								height={100}
								className="box-border size-5 pt-1"
								src={Tick.src}
							/>
							Diagnostic complet de l’installation, recherche de code de défaut
						</li>
						<li className="font-primary text-allo-dark-grey flex items-start gap-3 text-sm">
							<Image
								alt="blue tick mark"
								width={100}
								height={100}
								className="box-border size-5 pt-1"
								src={Tick.src}
							/>
							<div className="flex flex-col">
								Réparation immédiate sur place
								<span className="mt-0.5 text-xs italic">
									Si le temps d’intervention n&apos;excède pas une heure et sans
									changement de pièces détachées
								</span>
							</div>
						</li>
						<li className="font-primary text-allo-dark-grey flex items-start gap-3 text-sm">
							<Image
								alt="blue tick mark"
								width={100}
								height={100}
								className="box-border size-5 pt-1"
								src={Tick.src}
							/>
							<div className="flex flex-col">
								Établissement d’un devis
								<span className="mt-0.5 text-xs italic">
									Si un remplacement ou un changement de pièce est nécessaire.
									Le coût du diagnostic sera déduit de l&apos;éventuel devis de
									réparation qui vous sera proposé.
								</span>
							</div>
						</li>
					</ul>
					<div className="flex h-full flex-col justify-end">
						<Button
							className="w-full max-w-72 text-xs md:text-sm"
							onClick={() => handleInterventionSelection("panne")}
						>
							Diagnostiquer mon équipement
						</Button>
					</div>
				</div>
			</div>
		</div>
	);
}
