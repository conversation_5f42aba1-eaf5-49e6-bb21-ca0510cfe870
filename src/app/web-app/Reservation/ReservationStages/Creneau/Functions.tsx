import { db } from "@/lib/firebaseConfig";
import { getDoc, doc, getDocs, collection } from "firebase/firestore";
import { DateTime } from "luxon";

export const fetchZonePrice = async (
	zone: number,
	communeDept: string,
	setZonePrice: (price: number) => void,
	setTravelDuration: (time: number) => void,
) => {
	try {
		// Reference the zones document in the tarifs collection
		const zoneDocRef = doc(db, "zones", communeDept);
		const zoneDocSnap = await getDoc(zoneDocRef);

		if (zoneDocSnap.exists()) {
			const zoneData = zoneDocSnap.data();
			// Retrieve the price for the specified zone
			const zonePrice = zoneData[`zone_${zone}`];
			if (zonePrice !== undefined) {
				setZonePrice(zonePrice);
				setTravelDuration(15 * zone);
			}
		}
	} catch (error) {
		console.warn("Error fetching zone price:", error);
	}
};

type EquipmentPrice = {
	maint_tarif: number;
	panne_tarif: number;
	maint_temps: number;
	panne_temps: number;
	options: {
		[key: string]: {
			maint_tarif: number;
			panne_tarif: number;
			maint_temps: number;
			panne_temps: number;
		};
	};
};

export const fetchEquipmentPrices = async (
	setEquipments: (equipments: Record<string, EquipmentPrice>) => void,
) => {
	try {
		const equipmentTypes = ["clim", "chaudiere", "pompe"];
		const prices: Record<string, EquipmentPrice> = {};

		for (const type of equipmentTypes) {
			const equipmentRef = doc(db, "equipments", type);
			const equipmentSnap = await getDoc(equipmentRef);

			if (equipmentSnap.exists()) {
				const equipmentData = equipmentSnap.data();
				prices[type] = {
					maint_tarif: parseFloat(equipmentData.maint_tarif),
					panne_tarif: parseFloat(equipmentData.panne_tarif),
					maint_temps: parseFloat(equipmentData.maint_temps),
					panne_temps: parseFloat(equipmentData.panne_temps),
					options: {},
				};
				if (type === "clim") {
					const climOptions = [
						"cassette",
						"console",
						"exterieur",
						"gainable",
						"pompe_condensat",
						"split",
					];
					for (const option of climOptions) {
						const optionRef = doc(
							db,
							"equipments",
							type,
							"climOptions",
							option,
						);
						const optionSnap = await getDoc(optionRef);
						if (optionSnap.exists()) {
							const optionData = optionSnap.data();
							prices[type].options[option] = {
								maint_tarif: parseFloat(optionData.maint_tarif),
								panne_tarif: parseFloat(optionData.panne_tarif),
								maint_temps: parseFloat(optionData.maint_temps),
								panne_temps: parseFloat(optionData.panne_temps),
							};
						}
					}
				}
			}
		}
		setEquipments(prices);
	} catch (error) {
		console.warn("Error fetching equipment prices: ", error);
	}
};

export const fetchSelectedDateAndTime = async (
	setDate: (date: Date | undefined) => void,
	docId: string | undefined,
) => {
	try {
		const dateTimeRef = doc(db, "interventions", docId!);
		const dateTimeSnap = await getDoc(dateTimeRef);

		if (dateTimeSnap.exists()) {
			const data = dateTimeSnap.data();
			if (data.interventionDate) {
				const luxonDate = DateTime.fromISO(data.interventionDate, {
					zone: "Europe/Paris",
				});

				if (luxonDate.isValid) {
					setDate(luxonDate.toJSDate());
				} else {
					console.warn(
						"Invalid date format in Firestore:",
						data.interventionDate,
					);
					setDate(undefined);
				}
			}
		}
	} catch (error) {
		console.warn("Error fetching selected date and time: ", error);
	}
};

export const getAffectedTimeSlots = (
	startTime: string,
	durationMinutes: number,
) => {
	const affected: string[] = [];
	const [hours, minutes] = startTime.split(":").map(Number);

	// Calculate how many 30-minute slots this intervention spans
	const slots = Math.ceil(durationMinutes / 30);

	for (let i = 0; i < slots; i++) {
		const slotHours = hours + Math.floor((minutes + i * 30) / 60);
		const slotMinutes = (minutes + i * 30) % 60;

		const timeString = `${String(slotHours).padStart(2, "0")}:${String(slotMinutes).padStart(2, "0")}`;
		affected.push(timeString);
	}

	return affected;
};

export const interventionsWouldOverlap = (
	startTime1: string,
	duration1: number,
	startTime2: string,
	duration2: number,
): boolean => {
	// Convert times to minutes since midnight for easier comparison
	const [hours1, minutes1] = startTime1.split(":").map(Number);
	const [hours2, minutes2] = startTime2.split(":").map(Number);

	const start1 = hours1 * 60 + minutes1;
	const end1 = start1 + duration1;

	const start2 = hours2 * 60 + minutes2;
	const end2 = start2 + duration2;

	// Check for overlap
	return start1 < end2 && start2 < end1;
};

export const fetchUnavailableDates = async (
	dateString: string,
	morningTimes: string[],
	eveningTimes: string[],
	unavailableTimeSlots: Set<string>,
) => {
	try {
		const unavailableDatesQuery = collection(
			db,
			"unavailable_intervention_dates",
		);
		const snapshot = await getDocs(unavailableDatesQuery);

		snapshot.docs.forEach((doc) => {
			const docId = doc.id;
			const data = doc.data();

			if (docId === dateString) {
				if (data.morning) {
					morningTimes.forEach((t) => unavailableTimeSlots.add(t));
				}
				if (data.evening) {
					eveningTimes.forEach((t) => unavailableTimeSlots.add(t));
				}
			}
		});
	} catch (error) {
		console.warn("Error fetching unavailable dates:", error);
	}
};
