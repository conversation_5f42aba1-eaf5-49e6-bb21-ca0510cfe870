import Image from "next/image";
import FrenchMap from "/public/uploads/images/France_map_Alpes-Maritimes-02.webp";
import ComboboxCommune from "@/app/Reservation/ReservationStages/Locality/ComboboxCommune";

interface stageProps {
	nextStage: (id?: string) => void;
}

export default function Locality({ nextStage }: stageProps) {
	return (
		<div className="LOCALITY flex w-full flex-row items-stretch p-5 max-sm:flex-col-reverse">
			<div className="box-border flex w-full flex-1 flex-col items-center justify-center pl-10 lg:items-start lg:pl-20 xl:py-10 xl:pl-40 xl:pr-20 max-sm:p-0">
				<h3 className="mb-6 mt-2 text-start font-primary text-2xl font-bold text-allo-dark-grey lg:text-3xl max-sm:text-center">
					Dans quelle commune habitez-vous ?
				</h3>
				<div className="flex flex-col-reverse items-start justify-center gap-5 lg:flex-col lg:gap-0 max-sm:items-center">
					<p className="mb-5 text-start font-primary text-sm font-normal text-allo-dark-grey lg:text-base max-sm:text-center">
						Pour l&apos;instant, nous n&apos;opérons que dans le département des
						Alpes-Maritimes, mais nous prévoyons de nous étendre à d&apos;autres
						départements prochainement.
					</p>
					<ComboboxCommune nextStage={nextStage} />
				</div>
			</div>
			<div className="box-border flex h-auto flex-1 flex-col items-center justify-center lg:items-end">
				<Image
					width={1000}
					height={750}
					alt="Carte des Alpes-Maritimes - département 06 en France"
					src={FrenchMap.src}
					className="w-full max-sm:max-w-96"
				/>
			</div>
		</div>
	);
}
