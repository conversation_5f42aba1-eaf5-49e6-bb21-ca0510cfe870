"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import Timeline from "@/app/Reservation/Utilities/Timeline";
import Locality from "./ReservationStages/Locality/Locality";
import Intervention from "./ReservationStages/Intervention";
import Equipment from "./ReservationStages/Equipment/Equipment";
import Creneau from "./ReservationStages/Creneau/Creneau";
import Connecter from "./ReservationStages/Identifier/Connecter";
import Checkout from "./ReservationStages/Checkout/Main/Checkout";
import Return from "./ReservationStages/Checkout/Return";
import BackButton from "./Utilities/BackButton";
import { onAuthStateChanged } from "firebase/auth";
import { auth } from "@/lib/firebaseConfig";

export const defaultStages = [
	"Localité",
	"Intervention",
	"Équipement",
	"S'identifier",
	"Créneau",
	"Payer",
];

export default function ReservationApp() {
	const [stages, setStages] = useState<string[]>(defaultStages);
	const [currentStage, setCurrentStage] = useState<number>(1);
	const [docId, setDocId] = useState<string | undefined>(undefined);
	const [sessionId, setSessionId] = useState<string | undefined>(undefined);
	const sectionRef = useRef<HTMLElement>(null);

	useEffect(() => {
		const unsubscribe = onAuthStateChanged(auth, (user) => {
			const loggedIn = !!user;

			if (loggedIn) {
				setStages([
					...defaultStages.slice(0, 3),
					"Adresse",
					...defaultStages.slice(4),
				]);
			} else {
				setStages(defaultStages);
			}
		});

		return () => unsubscribe();
	}, []);

	const scrollToSection = () => {
		if (sectionRef.current) {
			const sectionTop = sectionRef.current.getBoundingClientRect().top;
			const scrollOptions = { behavior: "smooth" as const };

			if (sectionTop < 0) sectionRef.current.scrollIntoView(scrollOptions);
		}
	};

	const nextStage = (id?: string) => {
		if (id) setDocId(id);
		if (currentStage <= 6) {
			setCurrentStage(currentStage + 1);
		}
	};

	const goToStage = (stage: number) => {
		if (stage < currentStage && stage > 0) setCurrentStage(stage);
	};

	const handleHashChange = useCallback(() => {
		try {
			const hash = window.location.hash.substring(1); // Remove '#'

			// Extract the session_id manually
			const match = hash.match(/session_id=([^&]*)/);
			const sessionId = match ? match[1] : undefined;

			if (sessionId) {
				setSessionId(sessionId);
				setCurrentStage(7);
			}
		} catch (error) {
			console.warn("Error parsing hash:", error);
		}
	}, []);

	useEffect(() => {
		handleHashChange(); // Check on initial render
		window.addEventListener("hashchange", handleHashChange);

		return () => {
			window.removeEventListener("hashchange", handleHashChange);
		};
	}, [handleHashChange]);

	useEffect(() => {
		const hash = window.location.hash;
		if (!hash || hash === "#reservationSection") {
			scrollToSection();
		}
	}, [currentStage]);

	return (
		<section
			ref={sectionRef}
			id="reservationSection"
			className="border-allo-line box-border flex h-auto w-full flex-col items-center justify-center gap-5 border-b py-10 lg:pb-20"
		>
			<h2 className="bg-allo-grad font-primary text-t-15 inline-block bg-clip-text pt-10 font-semibold text-transparent uppercase md:hidden">
				Application de réservation
			</h2>
			<div className="bg-allo-bg-2 flex w-full flex-col items-center gap-3 rounded-xl px-5 py-10 max-[425px]:px-2 lg:min-h-[500px] lg:gap-10 lg:px-10">
				<BackButton
					currentStage={currentStage}
					goToStage={goToStage}
					stages={stages}
				/>
				<h2 className="bg-allo-grad font-primary text-t-15 hidden bg-clip-text font-semibold text-transparent uppercase md:inline-block">
					Application de réservation
				</h2>

				<Timeline
					currentStage={currentStage}
					goToStage={goToStage}
					stages={stages}
				/>
				{currentStage === 1 && <Locality nextStage={nextStage} />}
				{currentStage === 2 && (
					<Intervention nextStage={nextStage} docId={docId} />
				)}
				{currentStage === 3 && (
					<Equipment nextStage={nextStage} docId={docId} />
				)}
				{currentStage === 4 && (
					<Connecter nextStage={nextStage} docId={docId} />
				)}
				{currentStage === 5 && <Creneau nextStage={nextStage} docId={docId} />}
				{currentStage === 6 && <Checkout docId={docId} goToStage={goToStage} />}
				{currentStage === 7 && (
					<Return sessionId={sessionId} docId={docId} goToStage={goToStage} />
				)}
			</div>
		</section>
	);
}
