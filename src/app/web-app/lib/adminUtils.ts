import { doc, updateDoc, getDoc } from "firebase/firestore";
import { db } from "@/lib/firebaseConfig";

/**
 * Utility function to promote a user to admin status
 * This should be used carefully and only by existing admins
 */
export async function promoteUserToAdmin(userId: string): Promise<{
	success: boolean;
	message: string;
}> {
	try {
		const userRef = doc(db, "users", userId);
		const userDoc = await getDoc(userRef);
		
		if (!userDoc.exists()) {
			return {
				success: false,
				message: "User not found"
			};
		}

		await updateDoc(userRef, {
			userType: "admin"
		});

		return {
			success: true,
			message: "User successfully promoted to admin"
		};
	} catch (error) {
		console.error("Error promoting user to admin:", error);
		return {
			success: false,
			message: "Failed to promote user to admin"
		};
	}
}

/**
 * Utility function to check if a user is an admin
 */
export async function isUserAdmin(userId: string): Promise<boolean> {
	try {
		const userRef = doc(db, "users", userId);
		const userDoc = await getDoc(userRef);
		
		if (!userDoc.exists()) {
			return false;
		}

		const userData = userDoc.data();
		return userData?.userType === "admin";
	} catch (error) {
		console.error("Error checking admin status:", error);
		return false;
	}
}

/**
 * Utility function to demote an admin back to regular user
 */
export async function demoteAdminToUser(userId: string): Promise<{
	success: boolean;
	message: string;
}> {
	try {
		const userRef = doc(db, "users", userId);
		const userDoc = await getDoc(userRef);
		
		if (!userDoc.exists()) {
			return {
				success: false,
				message: "User not found"
			};
		}

		await updateDoc(userRef, {
			userType: "client"
		});

		return {
			success: true,
			message: "Admin successfully demoted to regular user"
		};
	} catch (error) {
		console.error("Error demoting admin:", error);
		return {
			success: false,
			message: "Failed to demote admin"
		};
	}
}
