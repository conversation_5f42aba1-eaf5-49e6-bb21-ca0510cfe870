import { useState, useEffect } from "react";
// import { signOut } from "firebase/auth"; // si tu utilises Firebase
// import { auth } from "@/lib/firebaseConfig"; // ou importe la configuration Firebase
// import { Toaster } from "@/components/ui/toast/toaster";

const useInactivityTimer = (timeout = 3600000, handleLogOut: () => void) => {
	// timeout en millisecondes (1h)
	const [isInactive, setIsInactive] = useState(false);

	useEffect(() => {
		// Typage explicite de timeoutId
		let timeoutId: NodeJS.Timeout;

		const resetTimer = () => {
			if (timeoutId) clearTimeout(timeoutId);
			timeoutId = setTimeout(() => {
				setIsInactive(true); // Si l'utilisateur est inactif pendant le temps spécifié, le marque comme inactif
				handleLogOut(); // Déconnecter l'utilisateur
			}, timeout);
		};

		// Ajouter les événements pour réinitialiser le timer à chaque action
		window.addEventListener("mousemove", resetTimer);
		window.addEventListener("keydown", resetTimer);
		window.addEventListener("click", resetTimer);

		// Initialiser le timer dès le chargement
		resetTimer();

		// Nettoyage des événements lors du démontage du composant
		return () => {
			window.removeEventListener("mousemove", resetTimer);
			window.removeEventListener("keydown", resetTimer);
			window.removeEventListener("click", resetTimer);
			clearTimeout(timeoutId); // Assurez-vous de nettoyer le timeout
		};
	}, [timeout, handleLogOut]);

	return isInactive;
};

export default useInactivityTimer;
