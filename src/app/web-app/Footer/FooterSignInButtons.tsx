import { UserConnectDialog } from "@/app/User/UserConnectDialog";
import UserAccountSheet from "../User/UserAccountSheet";
import { useEffect, useState } from "react";
import { auth } from "@/lib/firebaseConfig";
import { onAuthStateChanged } from "firebase/auth";
import Link from "next/link";

export default function SignInButtons() {
	const [isLoggedIn, setIsLoggedIn] = useState<boolean>(false);

	useEffect(() => {
		const unsubscribe = onAuthStateChanged(auth, (user) => {
			setIsLoggedIn(user ? true : false);
			return () => unsubscribe();
		});
	}, []);

	return (
		<div className="flex">
			<Link
				href="/#reservationSection"
				className="object-fit bg-allo-dark-grey mr-5 w-fit rounded-md px-5 py-2 text-sm font-medium text-slate-50 shadow-sm"
			>
				Réserver
			</Link>
			{!isLoggedIn ? (
				<UserConnectDialog>
					<div className="object-fit w-fit rounded-md bg-neutral-100 px-5 py-2 text-sm font-medium text-neutral-900 shadow-sm hover:bg-neutral-100/80">
						Se connecter
					</div>
				</UserConnectDialog>
			) : (
				<UserAccountSheet
					triggerElement={
						<div className="object-fit w-fit rounded-md bg-neutral-100 px-5 py-2 text-sm font-medium text-neutral-900 shadow-sm hover:bg-neutral-100/80">
							Votre compte
						</div>
					}
				/>
			)}
		</div>
	);
}
