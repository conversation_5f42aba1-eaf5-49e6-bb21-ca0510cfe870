"use client";

import { useEffect, useState } from "react";
import { onAuthStateChanged } from "firebase/auth";
import { doc, getDoc } from "firebase/firestore";
import { auth, db } from "@/lib/firebaseConfig";
import { useRouter } from "next/navigation";
import { LoadingSpinner } from "@/components/ui/loadingSpinner";
import Link from "next/link";
import { signOut } from "firebase/auth";

export default function AdminLayout({
	children,
}: {
	children: React.ReactNode;
}) {
	const [isLoading, setIsLoading] = useState(true);
	const [isAdmin, setIsAdmin] = useState(false);
	const [userDisplayName, setUserDisplayName] = useState<string>("");
	const router = useRouter();

	useEffect(() => {
		const unsubscribe = onAuthStateChanged(auth, async (user) => {
			if (user) {
				try {
					// Check if user is admin
					const userDoc = await getDoc(doc(db, "users", user.uid));
					const userData = userDoc.data();
					
					if (userData?.userType === "admin") {
						setIsAdmin(true);
						setUserDisplayName(userData.displayName || "Admin");
					} else {
						// User is not admin, redirect to main site
						router.push("/");
						return;
					}
				} catch (error) {
					console.error("Error checking admin status:", error);
					router.push("/");
					return;
				}
			} else {
				// User not authenticated, redirect to main site
				router.push("/");
				return;
			}
			setIsLoading(false);
		});

		return () => unsubscribe();
	}, [router]);

	const handleSignOut = async () => {
		try {
			await signOut(auth);
			router.push("/");
		} catch (error) {
			console.error("Error signing out:", error);
		}
	};

	if (isLoading) {
		return (
			<div className="min-h-screen flex items-center justify-center">
				<LoadingSpinner />
			</div>
		);
	}

	if (!isAdmin) {
		return null; // Will redirect in useEffect
	}

	return (
		<div className="min-h-screen bg-gray-100">
			{/* Admin Navigation Header */}
			<header className="bg-white shadow-sm border-b">
				<div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
					<div className="flex justify-between items-center h-16">
						<div className="flex items-center">
							<Link href="/admin" className="text-xl font-bold text-gray-900">
								alloCVC Admin
							</Link>
						</div>
						
						<nav className="hidden md:flex space-x-8">
							<Link 
								href="/admin" 
								className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
							>
								Dashboard
							</Link>
							<Link 
								href="/admin/users" 
								className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
							>
								Users
							</Link>
							<Link 
								href="/admin/interventions" 
								className="text-gray-600 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium"
							>
								Interventions
							</Link>
						</nav>

						<div className="flex items-center space-x-4">
							<span className="text-sm text-gray-600">
								Welcome, {userDisplayName}
							</span>
							<button
								onClick={handleSignOut}
								className="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-md text-sm font-medium"
							>
								Sign Out
							</button>
						</div>
					</div>
				</div>
			</header>

			{/* Main Content */}
			<main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
				{children}
			</main>
		</div>
	);
}
