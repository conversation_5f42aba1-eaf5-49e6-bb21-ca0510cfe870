export default function AdminDashboard() {
	return (
		<div className="px-4 py-6 sm:px-0">
			<div className="border-b border-gray-200 pb-5">
				<h1 className="text-3xl leading-6 font-bold text-gray-900">
					Admin Dashboard
				</h1>
				<p className="mt-2 max-w-4xl text-sm text-gray-500">
					Welcome to the alloCVC admin panel. Manage your business operations
					from here.
				</p>
			</div>

			{/* Dashboard Content */}
			<div className="mt-6">
				<div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
					{/* Quick Stats Cards */}
					<div className="overflow-hidden rounded-lg bg-white shadow">
						<div className="p-5">
							<div className="flex items-center">
								<div className="flex-shrink-0">
									<div className="flex h-8 w-8 items-center justify-center rounded-md bg-blue-500">
										<span className="text-sm font-medium text-white">U</span>
									</div>
								</div>
								<div className="ml-5 w-0 flex-1">
									<dl>
										<dt className="truncate text-sm font-medium text-gray-500">
											Total Users
										</dt>
										<dd className="text-lg font-medium text-gray-900">-</dd>
									</dl>
								</div>
							</div>
						</div>
					</div>

					<div className="overflow-hidden rounded-lg bg-white shadow">
						<div className="p-5">
							<div className="flex items-center">
								<div className="flex-shrink-0">
									<div className="flex h-8 w-8 items-center justify-center rounded-md bg-green-500">
										<span className="text-sm font-medium text-white">I</span>
									</div>
								</div>
								<div className="ml-5 w-0 flex-1">
									<dl>
										<dt className="truncate text-sm font-medium text-gray-500">
											Interventions
										</dt>
										<dd className="text-lg font-medium text-gray-900">-</dd>
									</dl>
								</div>
							</div>
						</div>
					</div>

					<div className="overflow-hidden rounded-lg bg-white shadow">
						<div className="p-5">
							<div className="flex items-center">
								<div className="flex-shrink-0">
									<div className="flex h-8 w-8 items-center justify-center rounded-md bg-yellow-500">
										<span className="text-sm font-medium text-white">$</span>
									</div>
								</div>
								<div className="ml-5 w-0 flex-1">
									<dl>
										<dt className="truncate text-sm font-medium text-gray-500">
											Revenue
										</dt>
										<dd className="text-lg font-medium text-gray-900">-</dd>
									</dl>
								</div>
							</div>
						</div>
					</div>
				</div>

				{/* Recent Activity */}
				<div className="mt-8">
					<div className="rounded-lg bg-white shadow">
						<div className="px-4 py-5 sm:p-6">
							<h3 className="mb-4 text-lg leading-6 font-medium text-gray-900">
								Recent Activity
							</h3>
							<p className="text-sm text-gray-500">
								Recent activity will be displayed here once data is connected.
							</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
