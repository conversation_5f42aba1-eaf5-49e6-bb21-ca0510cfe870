@import url("https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&family=Lexend:wght@100..900&display=swap");

@import "tailwindcss";

@import "react-day-picker/style.css";
@config "../tailwind.config.ts";

@utility text-balance {
	text-wrap: balance;
}

.screen-minus-navbar {
	min-height: calc(100vh - 4.5rem);
}

html {
	scroll-behavior: smooth;
}

@layer base {
	:root {
		--radius: 0.5rem;
	}
}

.rdp-root {
	.rdp-day {
		padding-top: 1.2rem;
		width: 5.5rem;
		height: 3rem;
		justify-items: center;
	}

	.rdp-day_button {
		width: 2.5rem;
		height: 2.5rem;
		font-size: 1rem;
	}

	.rdp-selected .rdp-day_button {
		background-color: #c024b6;
		color: white;
		font-family: "Inter", sans-serif;
		font-weight: 400;
	}

	.rdp-selected .price-text {
		font-family: "Inter", sans-serif;
		font-size: 0.75rem;
		font-weight: 400;
		color: #c024b6;
	}

	.rdp-weekdays {
		border-bottom: 1px solid lightgrey;
	}
	.rdp-month_caption {
		margin-bottom: 1rem;
	}

	.rdp-footer {
		padding-top: 2rem;
	}
}

@media (max-width: 1023px) {
	.rdp-root {
		.rdp-day {
			padding-top: 0.2rem;
			width: 4rem;
			height: 2.5rem;
			justify-items: center;
		}
		.rdp-week {
			height: 2rem;
		}

		.rdp-day_button {
			width: 2rem;
			height: 2rem;
			font-size: 0.8rem;
		}

		.rdp-month_caption {
			margin-bottom: 0.5rem;
		}

		.rdp-footer {
			padding-top: 1rem;
		}
	}
}

.cube-spinner {
	margin: 0;
	margin-top: 15px;
	transform-origin: 0 20px 0;
	animation-name: hero_spincube;
	animation-timing-function: ease-in-out;
	animation-iteration-count: infinite;
	animation-duration: 6s;
	transform-style: preserve-3d;
}

@media (min-width: 1024px) {
	.cube-spinner {
		transform-origin: 0 30px 0;
		margin-top: 10px;
	}
}

@media (min-width: 1280px) {
	.cube-spinner {
		margin: 0;
	}
}

@keyframes hero_spincube {
	0%,
	33.3333% {
		transform: rotateX(0deg);
	}
	33.3333%,
	66.6667% {
		transform: rotateX(120deg);
	}
	66.6667%,
	100% {
		transform: rotateX(240deg);
	}
	100% {
		transform: rotateX(1turn);
	}
}

.spinner-face-1 {
	transform: rotateX(0deg) translateZ(45px);
	backface-visibility: hidden;
	position: absolute;
}

.spinner-face-2 {
	transform: rotateX(120deg) translateZ(45px);
	backface-visibility: hidden;
	position: absolute;
}

.spinner-face-3 {
	transform: rotateX(240deg) translateZ(45px);
	backface-visibility: hidden;
	position: absolute;
}
