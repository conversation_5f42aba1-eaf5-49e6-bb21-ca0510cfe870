import { NextApiRequest, NextApiResponse } from "next";
import <PERSON><PERSON> from "stripe";
import getRawBody from "raw-body";
import { db } from "@/lib/firebaseConfig";
import { doc, setDoc, getDoc, arrayUnion } from "firebase/firestore";
import fetch from "node-fetch";
import FormData from "form-data";
import { createCalendarEvent } from "@/lib/googleCalendar";
import { DateTime } from "luxon";

const getStripe = () => {
	return new Stripe(
		process.env.NODE_ENV === "production"
			? process.env.NEXT_PUBLIC_STRIPE_SECRET_KEY!
			: process.env.NEXT_PUBLIC_STRIPE_SECRET_TEST_KEY!,
		{
			apiVersion: "2025-02-24.acacia",
		},
	);
};

const getEndpointSecret = () => {
	return process.env.NODE_ENV === "production"
		? process.env.NEXT_PUBLIC_STRIPE_WEBHOOK_SECRET_LIVE!
		: process.env.NEXT_PUBLIC_STRIPE_WEBHOOK_SECRET_TEST!;
};

export const config = {
	api: {
		bodyParser: false,
	},
};

const getTaxRateId = () => {
	return process.env.NODE_ENV === "production"
		? (process.env.STRIPE_LIVE_TAX_RATE_ID as string)
		: (process.env.STRIPE_TEST_TAX_RATE_ID as string);
};

async function handleAddressUpdate(
	userId: string,
	customerId: string,
	userAddress: Stripe.Address,
) {
	const stripe = getStripe();

	const userRef = doc(db, "users", userId);
	const userSnap = await getDoc(userRef);
	const userData = userSnap.exists() ? userSnap.data() : null;
	const existingAddress = userData?.shippingAddress;

	const isAddressDifferent =
		!existingAddress ||
		existingAddress.line1 !== userAddress.line1 ||
		existingAddress.line2 !== userAddress.line2 ||
		existingAddress.city !== userAddress.city ||
		existingAddress.postal_code !== userAddress.postal_code ||
		existingAddress.country !== userAddress.country;

	if (isAddressDifferent) {
		await setDoc(
			userRef,
			{
				shippingAddress: userAddress,
				address_1: userAddress.line1,
				address_2: userAddress.line2,
				commune: userAddress.city,
				zip_code: userAddress.postal_code,
				country: userAddress.country,
				updatedAt: DateTime.now().setZone("Europe/Paris").toJSDate(),
			},
			{ merge: true },
		);

		console.log("✅ Adresse enregistrée/mise à jour dans Firestore.");
	} else {
		console.log("ℹ️ Adresse déjà présente, inchangée.");
	}

	await stripe.customers.update(customerId, {
		address: {
			city: userAddress.city ?? undefined,
			country: userAddress.country ?? undefined,
			line1: userAddress.line1 ?? undefined,
			line2: userAddress.line2 ?? undefined,
			postal_code: userAddress.postal_code ?? undefined,
			state: userAddress.state ?? undefined,
		},
	});

	console.log("✅ Adresse du client mise à jour dans Stripe pour la facture.");
}

async function handleInvoiceCreation(
	session: Stripe.Checkout.Session,
	customerId: string,
	userAddress: Stripe.Address,
	email: string,
	userId: string,
	phone: string | null,
) {
	const stripe = getStripe();
	const taxRateId = getTaxRateId();

	const invoice = await stripe.invoices.create({
		customer: customerId,
		auto_advance: false,
	});
	let firestoreAddress = null;

	// On récupère l'interventionId depuis la session Stripe (via metadata)
	const interventionId = session.metadata?.interventionId;

	console.log("interventionId : ", interventionId);

	if (!interventionId) {
		console.error("❌ Aucun interventionId trouvé dans la session metadata.");
	} else {
		try {
			const interventionRef = doc(db, "interventions", interventionId);
			const interventionSnap = await getDoc(interventionRef);

			if (!interventionSnap.exists()) {
				console.error(
					`⚠️ Intervention avec ID ${interventionId} non trouvée dans Firestore.`,
				);
			} else {
				const data = interventionSnap.data();

				firestoreAddress = {
					address_1: data.address_1 ?? "",
					address_2: data.address_2 ?? "",
					code_postal: data.communeCodePostal ?? "", // 🔥 correction ici: tu stockes `communeCodePostal` non ?
					commune: data.commune ?? "",
					department: data.communeDept ?? "",
				};
				console.log(
					"✅ Adresse récupérée depuis l'intervention Firestore :",
					firestoreAddress,
				);
			}
		} catch (error) {
			console.error(
				"❌ Erreur lors de la récupération de l'intervention Firestore :",
				error,
			);
		}
	}

	const lineItems = await stripe.checkout.sessions.listLineItems(session.id, {
		expand: ["data.price.product"],
	});

	let totalDiscountAmount = 0;
	const productDescriptions: string[] = []; // tableau  pour stocker les descriptions pour le mail

	for (const item of lineItems.data) {
		const product = item.price?.product as Stripe.Product;
		const quantity = item.quantity ?? 1;
		const unitAmount = (item.price?.unit_amount ?? 0) / 100;

		//Climatisation : on affiche les sous-types dans la description de la facture
		let climDetails = "";

		if (product.metadata?.climSubOptions) {
			try {
				const subOptions = JSON.parse(product.metadata.climSubOptions);
				const parts: string[] = [];

				for (const [label, quantity] of Object.entries(subOptions)) {
					if (Number(quantity) > 0) {
						parts.push(`${label} (${quantity})`);
					}
				}

				if (parts.length) {
					climDetails = `Sous-types : ${parts.join(", ")}`;
				}
			} catch (err) {
				console.error("❌ Erreur parsing climSubOptions :", err);
			}
		}

		totalDiscountAmount += item.amount_discount || 0;
		productDescriptions.push(
			`${product.name} (${quantity}x) - ${product.description ?? ""}${climDetails ? " – " + climDetails : ""}`,
		);

		await stripe.invoiceItems.create({
			customer: customerId,
			amount: Math.round(unitAmount * quantity * 100),
			currency: session.currency ?? "eur",
			//description: `${product.name} - ${product.description ?? ""} (TTC)`,
			description: `${product.name} - ${product.description ?? ""}${climDetails ? " – " + climDetails : ""} (TTC)`,

			invoice: invoice.id,
			tax_rates: [taxRateId],
		});
	}

	if (totalDiscountAmount > 0) {
		const amountSubtotal = session.amount_subtotal ?? 0;
		const discountPercentage = (totalDiscountAmount / amountSubtotal) * 100;
		const discountDescription = `Réduction appliquée avec le code promo de ${discountPercentage.toFixed(2)}%`;

		await stripe.invoiceItems.create({
			customer: customerId,
			amount: -Math.round(totalDiscountAmount),
			currency: session.currency ?? "eur",
			description: discountDescription,
			invoice: invoice.id,
		});
	}

	const finalizedInvoice = await stripe.invoices.finalizeInvoice(invoice.id);

	const pdfUrl = finalizedInvoice.invoice_pdf;
	if (!pdfUrl) throw new Error("URL de la facture PDF manquante");

	const pdfResponse = await fetch(pdfUrl);
	const pdfBuffer = Buffer.from(await pdfResponse.arrayBuffer());
	const promoCode = session.discounts?.[0]?.promotion_code ?? null;
	console.log("promoCodeID  : ", promoCode);

	if (session.metadata?.interventionId) {
		// Pour récuupérer le prix après discount, still need interventionId...
		const interventionRef = doc(
			db,
			"interventions",
			session.metadata.interventionId,
		);
		let promoCodeLiteral: string | null = null;
		console.log("session discounnt : ", session.discounts);
		if (session.discounts?.length) {
			const promoCodeRef = session.discounts?.[0]?.promotion_code;
			const promoCodeId =
				typeof promoCodeRef === "string" ? promoCodeRef : promoCodeRef?.id;
			if (promoCodeId) {
				const promo = await stripe.promotionCodes.retrieve(promoCodeId);
				console.log("promo : ", promo);
				promoCodeLiteral = promo.code; // ex: "TestPromo20"
				console.log("promoCodeLiteral : ", promoCodeLiteral);
			}
		}
		await setDoc(
			interventionRef,
			{
				interventionNetPrice: (session.amount_total ?? 0) / 100, //  on divise par 100 pour avoir des euros
				invoiceUrl: pdfUrl,
				promoCode: promoCodeLiteral,
				tel: phone,
			},
			{ merge: true },
		);
	}

	console.log("✅ Addresse : ", firestoreAddress);
	const fullAddress =
		firestoreAddress &&
		firestoreAddress.address_1 &&
		firestoreAddress.code_postal &&
		firestoreAddress.commune
			? `${firestoreAddress.address_1}${firestoreAddress.address_2 ? ", " + firestoreAddress.address_2 : ""}, ${firestoreAddress.code_postal} ${firestoreAddress.commune}`
			: "Non renseignée";

	/* Email pour le CLIENT */
	const clientFormData = new FormData();
	const productsListHtml = productDescriptions //C'est une liste de produits pour le mail (sinon ça affiche un objet)
		.map((p) => `<li>${p}</li>`)
		.join("");

	clientFormData.append("from", "alloCVC <<EMAIL>>");
	clientFormData.append("to", email);
	clientFormData.append("subject", "Votre facture alloCVC 🧾");
	clientFormData.append(
		"html",
		`<p>Bonjour,</p>
   <p>Merci pour votre paiement de ${(session.amount_total! / 100).toFixed(2)} ${session.currency?.toUpperCase()}.</p>
   <p>Vous trouverez ci-joint votre facture au format PDF.</p>
   <p>Si vous avez des questions, n'hésitez pas à nous contacter.</p>
   <p>Nous vous remercions de votre confiance et restons à votre disposition.</p>
   <p>Bien cordialement,<br>L'équipe alloCVC</p>`,
	);
	clientFormData.append("attachment", pdfBuffer, {
		filename: "facture-alloCVC.pdf",
		contentType: "application/pdf",
	});

	/**  2) Email pour  alloCVC */
	const ownerFormData = new FormData();
	ownerFormData.append("from", "alloCVC <<EMAIL>>");
	ownerFormData.append("to", "<EMAIL>");
	ownerFormData.append("subject", "Nouvelle commande alloCVC 🛠️");
	ownerFormData.append(
		"html",
		`<p>Nouvelle intervention reçue !</p>
   <p><strong>Email client :</strong> ${email}</p>
   <p><strong>Téléphone :</strong> ${phone}</p>
   <p><strong>Adresse :</strong> ${fullAddress}</p>
   <p><strong>Prix total :</strong> ${(session.amount_total! / 100).toFixed(2)} ${session.currency?.toUpperCase()}</p>
   <p> Détails de la commande :</p>
   <ul>${productsListHtml} </ul>  
   <p><strong>Quantité :</strong> ${lineItems.data[0]?.quantity}</p>

   <p>Facture en pièce jointe.</p>`,
	);
	ownerFormData.append("attachment", pdfBuffer, {
		filename: "facture-alloCVC.pdf",
		contentType: "application/pdf",
	});

	/** 📬 Envoi des deux mails */
	await fetch(
		`https://api.eu.mailgun.net/v3/${process.env.MAILGUN_LIVE_DOMAIN}/messages`,
		{
			method: "POST",
			headers: {
				Authorization: `Basic ${Buffer.from(`api:${process.env.MAILGUN_LIVE_API_KEY}`).toString("base64")}`,
				...clientFormData.getHeaders(),
			},
			body: clientFormData,
		},
	);

	await fetch(
		`https://api.eu.mailgun.net/v3/${process.env.MAILGUN_LIVE_DOMAIN}/messages`,
		{
			method: "POST",
			headers: {
				Authorization: `Basic ${Buffer.from(`api:${process.env.MAILGUN_LIVE_API_KEY}`).toString("base64")}`,
				...ownerFormData.getHeaders(),
			},
			body: ownerFormData,
		},
	);

	/** 🔥 Mettre la dernière facture dans la tableau de factures */
	//const invoiceUrl = finalizedInvoice.hosted_invoice_url;

	await setDoc(
		doc(db, "users", userId),
		{
			invoices: arrayUnion({
				invoiceId: finalizedInvoice.id,
				createdAt: DateTime.now().setZone("Europe/Paris").toJSDate(),
				invoiceUrl: pdfUrl,
			}),
		},
		{ merge: true },
	);

	//Durée de 1h par défaut (tu peux la personnaliser selon les produits)
	const interventionDate = session.metadata?.interventionDate; // ex: "2025-06-09"
	const interventionTime = session.metadata?.interventionTime; // ex: "18:00"

	if (!interventionDate || !interventionTime) {
		throw new Error(
			"❌ interventionDate ou interventionTime manquant dans la session.metadata.",
		);
	}

	const start = DateTime.fromISO(`${interventionDate}T${interventionTime}`, {
		zone: "Europe/Paris",
	});

	const end = start.plus({ hours: 2 }); // Add 2 hours

	await createCalendarEvent({
		summary: `Intervention alloCVC – ${email}`,
		description: `Adresse : ${fullAddress}\nTéléphone : ${phone ?? "non renseigné"}  \nDétails de la commande :
	   ${productsListHtml} `,
		start: start.toISO(),
		end: end.toISO(),
	});
	// 	+console.log("📩 Mails client + alloCVC envoyés avec succès !");
}

export default async function handler(
	req: NextApiRequest,
	res: NextApiResponse,
) {
	if (req.method !== "POST") {
		return res.status(405).json({ error: "Méthode non autorisée" });
	}

	const stripe = getStripe();
	const endpointSecret = getEndpointSecret();

	const sig = req.headers["stripe-signature"] as string;
	let event: Stripe.Event;

	try {
		const rawBody = await getRawBody(req);
		event = stripe.webhooks.constructEvent(rawBody, sig, endpointSecret);
	} catch (error) {
		console.error("⚠️ Webhook error:", (error as Error).message);
		return res.status(400).send(`Webhook Error: ${(error as Error).message}`);
	}

	if (event.type !== "checkout.session.completed") {
		if (process.env.NODE_ENV === "development") {
			console.log("ℹ️ Ignored event type:", event.type);
		}

		return res.status(200).json({ ignored: true });
	}

	const session = event.data.object as Stripe.Checkout.Session;

	const userAddress = session.customer_details?.address || null;
	const customerId = session.customer as string;
	const email = session.customer_details?.email || "<EMAIL>";
	const phone = session.customer_details?.phone || null;

	const lineItems = await stripe.checkout.sessions.listLineItems(session.id, {
		expand: ["data.price.product"],
	});
	const product = lineItems.data[0]?.price?.product as Stripe.Product;
	const metadata = (product as Stripe.Product).metadata;
	const userId = metadata.userId;

	if (!userId) {
		console.log("❌ Aucun userId trouvé dans le metadata du produit.");
		return res.status(400).end("Missing userId");
	}

	if (userAddress) {
		await handleAddressUpdate(userId, customerId, userAddress);
		await handleInvoiceCreation(
			session,
			customerId,
			userAddress,
			email,
			userId,
			phone,
		);
	} else {
		console.log("❌ Aucune adresse récupérée depuis Stripe.");
	}

	return res.status(200).json({ received: true });
}
