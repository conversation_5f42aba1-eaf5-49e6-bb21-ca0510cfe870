"use client";

import { useEffect, useState } from "react";
import { onAuthStateChanged } from "firebase/auth";
import { doc, getDoc } from "firebase/firestore";
import { auth, db } from "@/lib/firebaseConfig";
import { useRouter } from "next/navigation";
import { LoadingSpinner } from "@/components/ui/loadingSpinner";

interface AdminProtectionProps {
	children: React.ReactNode;
	fallbackPath?: string;
}

/**
 * Component to protect admin routes
 * Checks if user is authenticated and has admin privileges
 */
export default function AdminProtection({ 
	children, 
	fallbackPath = "/" 
}: AdminProtectionProps) {
	const [isLoading, setIsLoading] = useState(true);
	const [isAuthorized, setIsAuthorized] = useState(false);
	const router = useRouter();

	useEffect(() => {
		const unsubscribe = onAuthStateChanged(auth, async (user) => {
			if (user) {
				try {
					// Check if user has admin privileges
					const userDoc = await getDoc(doc(db, "users", user.uid));
					const userData = userDoc.data();
					
					if (userData?.userType === "admin") {
						setIsAuthorized(true);
					} else {
						// User is not admin, redirect
						router.push(fallbackPath);
						return;
					}
				} catch (error) {
					console.error("Error checking admin status:", error);
					router.push(fallbackPath);
					return;
				}
			} else {
				// User not authenticated, redirect
				router.push(fallbackPath);
				return;
			}
			setIsLoading(false);
		});

		return () => unsubscribe();
	}, [router, fallbackPath]);

	if (isLoading) {
		return (
			<div className="min-h-screen flex items-center justify-center">
				<div className="text-center">
					<LoadingSpinner size={32} />
					<p className="mt-4 text-gray-600">Checking permissions...</p>
				</div>
			</div>
		);
	}

	if (!isAuthorized) {
		return null; // Will redirect in useEffect
	}

	return <>{children}</>;
}
