import { But<PERSON> } from "./button";
import { useToast } from "./toast/use-toast";
import { auth } from "@/lib/firebaseConfig";
import { signOut } from "firebase/auth";
import { cn } from "@/lib/utils";

export default function LogOutButton({ className }: { className?: string }) {
	const { toast } = useToast();

	const handleLogOut = () => {
		signOut(auth)
			.then(() => {
				toast({
					title: "Vous vous êtes déconnecté avec succès",
					description:
						"Veuillez vous reconnecter pour consulter vos interventions.",
				});
			})
			.catch((error) => {
				toast({
					title: "Erreur lors de déconnexion",
					description: (error as <PERSON>rror).message,
					variant: "destructive",
				});
			});
	};

	return (
		<div className={cn(className)}>
			<Button onClick={handleLogOut} variant="primary">
				Se déconnecter
			</Button>
		</div>
	);
}
