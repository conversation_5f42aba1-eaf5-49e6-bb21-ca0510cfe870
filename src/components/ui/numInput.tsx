import { useState } from "react";

interface NumInputProps {
	minAmount: number;
	maxAmount: number;
	value: number;
	onChange: (value: number) => void;
}

export default function NumInput({
	minAmount,
	maxAmount,
	value,
	onChange,
}: NumInputProps) {
	// State to keep track of the current input value
	const [currentValue, setCurrentValue] = useState(value);

	// Function to handle increment
	const increment = () => {
		setCurrentValue((prev) => {
			const newValue = prev + 1 > maxAmount ? maxAmount : prev + 1;
			onChange(newValue);
			return newValue;
		});
	};

	// Function to handle decrement
	const decrement = () => {
		setCurrentValue((prev) => {
			const newValue = prev - 1 < minAmount ? minAmount : prev - 1;
			onChange(newValue);
			return newValue;
		});
	};

	const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		let newValue = parseInt(e.target.value, 10);

		// If the input is not a number or out of range, set value within bounds
		if (isNaN(newValue)) newValue = minAmount;
		else if (newValue < minAmount) newValue = minAmount;
		else if (newValue > maxAmount) newValue = maxAmount;
		onChange(newValue);
		setCurrentValue(newValue);
	};

	return (
		<>
			<form className="mx-auto max-w-xs">
				<div className="relative flex max-w-24 items-center lg:max-w-32">
					<button
						type="button"
						id="decrement-button"
						onClick={decrement}
						className="h-6 rounded-s-lg border border-gray-300 bg-gray-100 p-2 hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-100 lg:h-8 lg:p-3"
					>
						<svg
							className="size-2 text-allo-dark-grey dark:text-white"
							aria-hidden="true"
							xmlns="http://www.w3.org/2000/svg"
							fill="none"
							viewBox="0 0 18 2"
						>
							<path
								stroke="currentColor"
								strokeLinecap="round"
								strokeLinejoin="round"
								strokeWidth="2"
								d="M1 1h16"
							/>
						</svg>
					</button>
					<input
						type="text"
						id="quantity-input"
						value={currentValue}
						onChange={handleChange}
						aria-describedby="quantity input"
						className="h-6 w-full border-y border-gray-300 bg-gray-50 py-2 text-center text-xs text-allo-dark-grey outline-none focus:bg-gray-100 lg:h-8 lg:py-2.5 lg:text-sm"
						placeholder="999"
						required
					/>
					<button
						type="button"
						id="increment-button"
						onClick={increment}
						className="h-6 rounded-e-lg border border-gray-300 bg-gray-100 p-2 text-xs hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-100 lg:h-8 lg:p-3"
					>
						<svg
							className="size-2 text-allo-dark-grey dark:text-white"
							aria-hidden="true"
							xmlns="http://www.w3.org/2000/svg"
							fill="none"
							viewBox="0 0 18 18"
						>
							<path
								stroke="currentColor"
								strokeLinecap="round"
								strokeLinejoin="round"
								strokeWidth="2"
								d="M9 1v16M1 9h16"
							/>
						</svg>
					</button>
				</div>
			</form>
		</>
	);
}
