import { useState, useEffect } from "react";

import { Label } from "@/components/ui/label";
import { Input } from "./input";

export type PasswordProps = {
	password: string;
	onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
	onValidityChange: (isValid: boolean) => void;
	showRequirements: boolean;
};

export default function Password({
	password,
	onChange,
	onValidityChange,
	showRequirements,
}: PasswordProps) {
	const [passwordVisible, setPasswordVisible] = useState(false);

	const togglePasswordVisibility = () => setPasswordVisible(!passwordVisible);

	const requirements = [
		{
			label: "Au moins 8 caractères",
			isMet: password.length >= 8,
		},
		{
			label: "Au moins un caractère majuscule",
			isMet: /[A-Z]/.test(password),
		},
		{
			label: "Au moins un numéro",
			isMet: /[0-9]/.test(password),
		},
		{
			label: "Au moins un caractère spécial, ex. ! @ # ? _",
			isMet: /[\^\$\*\.\[\]\{\}\(\)\?\"\!\@\#\%\&\/\\\,\>\<\'\:\;\|\_\~]/.test(
				password,
			),
		},
	];

	const isValid = requirements.every((req) => req.isMet);
	useEffect(() => {
		onValidityChange(isValid);
	}, [isValid, onValidityChange]);

	return (
		<div className="grid grid-cols-4 items-start gap-4">
			<Label htmlFor="password" className="mt-3 hidden text-right md:block">
				Password
			</Label>

			<div className="relative col-span-4 md:col-span-3">
				{/* Password Input Field */}
				<Input
					id="password"
					type={passwordVisible ? "text" : "password"}
					value={password}
					onChange={onChange}
					required
					placeholder="Mot de passe"
					className="text-sm md:placeholder-transparent"
				/>

				{/* Toggle Visibility Button */}
				<button
					type="button"
					onClick={togglePasswordVisibility}
					className="absolute top-2 right-2 text-neutral-500"
				>
					{passwordVisible ? "🙈" : "👁️"}
				</button>

				{/* Password Requirements */}
				{!showRequirements && password && (
					<div className="mt-2 text-xs text-gray-500">
						<ul className="space-y-1">
							{requirements.map((req, idx) => (
								<li key={idx} className="flex items-center">
									<svg
										className={`mr-2 h-4 w-4 ${
											req.isMet ? "text-green-500" : "text-gray-500"
										}`}
										fill="currentColor"
										viewBox="0 0 20 20"
										xmlns="http://www.w3.org/2000/svg"
										aria-hidden="true"
									>
										<path d="M10 .5a9.5 9.5 0 1 0 9.5 9.5A9.51 9.51 0 0 0 10 .5Zm3.707 8.207-4 4a1 1 0 0 1-1.414 0l-2-2a1 1 0 0 1 1.414-1.414L9 10.586l3.293-3.293a1 1 0 0 1 1.414 1.414Z" />
									</svg>
									{req.label}
								</li>
							))}
						</ul>
					</div>
				)}
			</div>
		</div>
	);
}
