"use client";

import Image from "next/image";
import { imageLoader } from "@/lib/imageLoader";
import Autoplay from "embla-carousel-autoplay";
import { useState, useEffect } from "react";

import {
	Carousel,
	CarouselContent,
	CarouselItem,
	CarouselNext,
	CarouselPrevious,
} from "@/components/ui/carousel";
import { testimonial } from "@/lib/testimonial";

interface TestimonialSliderProps {
	testimonials: testimonial[];
}

export default function TestimonialSlider({
	testimonials,
}: TestimonialSliderProps) {
	const [delay, setDelay] = useState<number>(2000);

	useEffect(() => {
		const updateDelay = () => {
			if (window.innerWidth < 640) {
				setDelay(4000); // Mobile (sm)
			} else if (window.innerWidth < 1024) {
				setDelay(3000); // Tablet (md)
			} else {
				setDelay(2000); // Desktop (lg)
			}
		};

		updateDelay();
		window.addEventListener("resize", updateDelay);
		return () => window.removeEventListener("resize", updateDelay);
	}, []);

	return (
		<div className="w-full overflow-hidden px-5 lg:max-w-6xl lg:overflow-visible">
			<Carousel
				opts={{
					loop: true,
					align: "start",
				}}
				plugins={[
					Autoplay({
						delay: delay,
					}),
				]}
			>
				<CarouselContent>
					{testimonials.map((testimonial, index) => (
						<CarouselItem key={index} className="md:basis-1/2 lg:basis-1/3">
							<div className="flex flex-col px-4 py-5 sm:p-6">
								<q className="flex-1 text-sm text-allo-dark-grey lg:text-base">
									{testimonial.quote}
								</q>
								<div className="mt-6 flex items-center gap-3">
									<Image
										loader={imageLoader}
										className="size-11 rounded-full lg:size-14"
										height={40}
										width={40}
										alt={testimonial.name}
										src={testimonial.imgSrc}
										loading="lazy"
									/>
									<div className="flex flex-col gap-0.5">
										<p className="text-xs font-semibold text-allo-dark-grey lg:text-sm">
											{testimonial.name}
										</p>
										<p className="text-xs text-gray-500 dark:text-gray-400 lg:text-sm">
											{testimonial.role}
										</p>
									</div>
								</div>
							</div>
						</CarouselItem>
					))}
				</CarouselContent>
				<CarouselPrevious className="absolute left-[-50px] top-1/2 -translate-y-1/2 fill-black" />
				<CarouselNext className="absolute right-[-50px] top-1/2 -translate-y-1/2 fill-black" />
			</Carousel>
		</div>
	);
}
