import { z } from "zod";

export const interventionDataSchema = z.object({
	chaudiere: z.number(),
	clim: z.number(),
	climSubOptions: z.record(z.string(), z.number()).optional(),
	commune: z.string(),
	communeDept: z.string(),
	communeZone: z.number(),
	interventionDate: z.string(),
	interventionTime: z.string(),
	interventionTotalPrice: z.number(),
	interventionGrossPrice: z.number(),
	interventionTravelPrice: z.number(),
	maintenance: z.number(),
	panne: z.number(),
	pompe: z.number(),
	userId: z.string(),
	userEmail: z.string().email(),
	paymentStatus: z.string().optional(),
	interventionId: z.string(), // Pour récupérer l'ID de l'intervention  ??
});

export type InterventionData = z.infer<typeof interventionDataSchema>;
