import { google } from "googleapis";

export async function createCalendarEvent({
	summary,
	description,
	start,
	end,
}: {
	summary: string;
	description: string;
	start: string | null;
	end: string | null;
}) {
	if (!start || !end) {
		throw new Error("Start and end timings are required");
	}

	const oauth2Client = new google.auth.OAuth2(
		process.env.GOOGLE_CLIENT_ID,
		process.env.GOOGLE_CLIENT_SECRET,
		process.env.GOOGLE_REDIRECT_URI,
	);

	oauth2Client.setCredentials({
		refresh_token: process.env.GOOGLE_REFRESH_TOKEN,
	});

	const calendar = google.calendar({ version: "v3", auth: oauth2Client });

	const event = {
		summary,
		description,
		start: { dateTime: start, timeZone: "Europe/Paris" },
		end: { dateTime: end, timeZone: "Europe/Paris" },
	};

	const response = await calendar.events.insert({
		calendarId: "primary",
		requestBody: event,
	});

	console.log("📅 Événement ajouté :", response.data.htmlLink);
}
