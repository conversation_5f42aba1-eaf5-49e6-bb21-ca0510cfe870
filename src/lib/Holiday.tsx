// 🔧 Fonction pour récupérer les jours fériés français via l’API officielle

// Cache to store already fetched holiday data
const holidayCache: Record<number, Set<string>> = {};

export const fetchFrenchHolidays = async (
	year: number,
): Promise<Set<string>> => {
	if (holidayCache[year]) {
		return holidayCache[year];
	}
	try {
		const response = await fetch(
			`https://calendrier.api.gouv.fr/jours-feries/metropole/${year}.json`,
		);
		const data = await response.json();

		// Store in cache for future use
		holidayCache[year] = new Set(Object.keys(data));

		// On retourne les dates sous forme d'un Set pour accès rapide
		return holidayCache[year]; // Format: "YYYY-MM-DD"
	} catch (error) {
		console.error("Erreur lors du fetch des jours fériés :", error);
		return new Set();
	}
};
