import { initializeApp } from "firebase/app";
import { getFirestore } from "firebase/firestore";
import { getAuth } from "firebase/auth";
import { Analytics, getAnalytics } from "firebase/analytics";

const firebaseConfig = {
	apiKey: "AIzaSyArklzdbl5xPSURoev_f6Df0pxrEXQiLCU",
	authDomain: "allocvc.firebaseapp.com",
	projectId: "allocvc",
	storageBucket: "allocvc.appspot.com",
	messagingSenderId: "184579621260",
	appId: "1:184579621260:web:f9862cd2728a9295a256a9",
	measurementId: "G-EEBWTYZP9F",
};

export const app = initializeApp(firebaseConfig);
export const auth = getAuth(app);
export const db = getFirestore(app);
export let analytics: Analytics | undefined = undefined;

if (typeof window !== "undefined") {
	analytics = getAnalytics(app);
}
