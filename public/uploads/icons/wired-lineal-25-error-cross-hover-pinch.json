{"v": "5.10.2", "fr": 60, "ip": 0, "op": 60, "w": 430, "h": 430, "nm": "wired-lineal-25-error-cross", "ddd": 0, "assets": [{"id": "comp_1", "nm": "mask-2", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "mask", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [214.5, 215.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.513, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 21, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[-84.777, -122.121], [-84.777, -119.469], [-119.69, -84.556], [-122.342, -84.556], [-125.734, -87.948], [-125.734, -90.6], [-90.82, -125.513], [-88.169, -125.513]], "c": true}]}, {"t": 46, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[87.946, 50.602], [87.946, 53.254], [53.033, 88.167], [50.381, 88.167], [-88.388, -50.602], [-88.388, -53.254], [-53.475, -88.167], [-50.823, -88.167]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "body", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 21, "op": 115, "st": 21, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "mask", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [214.5, 215.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.513, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 21, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[125.734, -90.6], [125.734, -87.948], [122.342, -84.556], [119.69, -84.556], [84.777, -119.469], [84.777, -122.121], [88.169, -125.513], [90.82, -125.513]], "c": true}]}, {"t": 46, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[88.388, -53.254], [88.388, -50.602], [-50.381, 88.167], [-53.033, 88.167], [-87.946, 53.254], [-87.946, 50.602], [50.823, -88.167], [53.475, -88.167]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "body", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 21, "op": 115, "st": 21, "ct": 1, "bm": 0}]}, {"id": "comp_2", "nm": "hover-pinch", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Cross-outline 2", "parent": 14, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0], [0, 0], [0.732, -0.732], [0, 0]], "v": [[88.388, -53.254], [88.388, -50.602], [37.565, 0.221], [87.946, 50.602], [87.946, 53.254], [53.033, 88.167], [50.381, 88.167], [0, 37.786], [-50.381, 88.167], [-53.033, 88.167], [-87.946, 53.254], [-87.946, 50.602], [-37.565, 0.221], [-88.388, -50.602], [-88.388, -53.254], [-53.475, -88.167], [-50.823, -88.167], [0, -37.344], [50.823, -88.167], [53.475, -88.167]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 29, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [0, 0], [0.732, 0.732], [0, 0], [0.614, 0.658], [0, 0], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [0, 0], [-0.661, 0.797], [0, 0], [-0.732, 0.732], [0, 0], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0], [0, 0], [0.732, -0.732], [0, 0]], "v": [[105.774, -70.64], [105.774, -67.988], [41.096, -2.998], [41.065, -2.906], [39.77, -1.395], [33.502, 4.916], [30.85, 4.916], [0.004, -26.733], [-30.562, 4.234], [-33.214, 4.234], [-39.615, -2.113], [-40.971, -3.686], [-41.003, -3.716], [-105.774, -67.988], [-105.774, -70.64], [-70.861, -105.553], [-68.209, -105.553], [0, -37.344], [68.209, -105.553], [70.861, -105.553]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [0, 0], [0.732, 0.732], [0, 0], [0.614, 0.658], [0, 0], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [0, 0], [-0.661, 0.797], [0, 0], [-0.732, 0.732], [0, 0], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0], [0, 0], [0.732, -0.732], [0, 0]], "v": [[105.054, -69.919], [105.054, -67.268], [37.565, 0.221], [37.534, 0.313], [36.239, 1.824], [24.077, 14.072], [21.425, 14.072], [0.002, -7.524], [-21.125, 14.192], [-23.776, 14.192], [-36.177, 1.824], [-37.534, 0.252], [-37.565, 0.221], [-105.054, -67.268], [-105.054, -69.919], [-70.14, -104.833], [-67.489, -104.833], [0, -37.344], [67.489, -104.833], [70.14, -104.833]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 31, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [0, 0], [0.732, 0.732], [0, 0], [0.614, 0.658], [0, 0], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [0, 0], [-0.661, 0.797], [0, 0], [-0.732, 0.732], [0, 0], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0], [0, 0], [0.732, -0.732], [0, 0]], "v": [[102.711, -67.577], [102.711, -64.925], [37.565, 0.221], [37.534, 0.313], [36.239, 1.824], [15.298, 22.905], [12.647, 22.905], [0.06, 10.709], [-11.808, 23.355], [-14.46, 23.355], [-36.177, 1.824], [-37.534, 0.252], [-37.565, 0.221], [-102.711, -64.925], [-102.711, -67.577], [-67.798, -102.49], [-65.146, -102.49], [0, -37.344], [65.146, -102.49], [67.798, -102.49]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 32, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [0, 0], [0.732, 0.732], [0, 0], [0.614, 0.658], [0, 0], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [0, 0], [-0.661, 0.797], [0, 0], [-0.732, 0.732], [0, 0], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0], [0, 0], [0.732, -0.732], [0, 0]], "v": [[100.241, -65.106], [100.241, -62.455], [37.565, 0.221], [37.534, 0.313], [36.239, 1.824], [5.838, 32.046], [-0.032, 27.14], [-0.063, 27.109], [-0.093, 27.201], [-5.401, 32.326], [-36.177, 1.824], [-37.534, 0.252], [-37.565, 0.221], [-100.241, -62.455], [-100.241, -65.106], [-65.327, -100.02], [-62.676, -100.02], [0, -37.344], [62.676, -100.02], [65.327, -100.02]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0], [0, 0], [0.732, -0.732], [0, 0]], "v": [[98.363, -63.229], [98.363, -60.577], [37.565, 0.221], [38.84, 1.496], [38.84, 4.148], [3.927, 39.061], [1.275, 39.061], [0, 37.786], [-1.275, 39.061], [-3.927, 39.061], [-38.84, 4.148], [-38.84, 1.496], [-37.565, 0.221], [-98.363, -60.577], [-98.363, -63.229], [-63.45, -98.142], [-60.798, -98.142], [0, -37.344], [60.798, -98.142], [63.45, -98.142]], "c": true}]}, {"i": {"x": 0.475, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 37, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0], [0, 0], [0.732, -0.732], [0, 0]], "v": [[93.197, -58.062], [93.197, -55.411], [37.565, 0.221], [63.529, 26.185], [63.529, 28.837], [28.616, 63.75], [25.964, 63.75], [0, 37.786], [-25.964, 63.75], [-28.616, 63.75], [-63.529, 28.837], [-63.529, 26.185], [-37.565, 0.221], [-93.197, -55.411], [-93.197, -58.062], [-58.283, -92.976], [-55.632, -92.976], [0, -37.344], [55.632, -92.976], [58.283, -92.976]], "c": true}]}, {"t": 45, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0], [0, 0], [0.732, -0.732], [0, 0]], "v": [[88.388, -53.254], [88.388, -50.602], [37.565, 0.221], [87.946, 50.602], [87.946, 53.254], [53.033, 88.167], [50.381, 88.167], [0, 37.786], [-50.381, 88.167], [-53.033, 88.167], [-87.946, 53.254], [-87.946, 50.602], [-37.565, 0.221], [-88.388, -50.602], [-88.388, -53.254], [-53.475, -88.167], [-50.823, -88.167], [0, -37.344], [50.823, -88.167], [53.475, -88.167]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.2, 0.2, 0.2, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-25-error-cross').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-25-error-cross').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "body", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 29, "op": 299, "st": -1, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Cross-outline", "parent": 14, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0], [0, 0], [0.732, -0.732], [0, 0]], "v": [[88.388, -53.254], [88.388, -50.602], [37.565, 0.221], [87.946, 50.602], [87.946, 53.254], [53.033, 88.167], [50.381, 88.167], [0, 37.786], [-50.381, 88.167], [-53.033, 88.167], [-87.946, 53.254], [-87.946, 50.602], [-37.565, 0.221], [-88.388, -50.602], [-88.388, -53.254], [-53.475, -88.167], [-50.823, -88.167], [0, -37.344], [50.823, -88.167], [53.475, -88.167]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 4, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0], [0, 0], [0.732, -0.732], [0, 0]], "v": [[74.954, -39.82], [74.954, -37.168], [37.565, 0.221], [91.175, 53.831], [91.175, 56.482], [56.261, 91.396], [53.61, 91.396], [0, 37.786], [-53.61, 91.396], [-56.261, 91.396], [-91.175, 56.482], [-91.175, 53.831], [-37.565, 0.221], [-74.954, -37.168], [-74.954, -39.82], [-40.041, -74.733], [-37.389, -74.733], [0, -37.344], [37.389, -74.733], [40.041, -74.733]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 7, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0], [0, 0], [0.732, -0.732], [0, 0]], "v": [[52.713, -17.579], [52.713, -14.927], [37.565, 0.221], [96.575, 59.231], [96.575, 61.883], [61.662, 96.796], [59.01, 96.796], [0, 37.786], [-59.01, 96.796], [-61.662, 96.796], [-96.575, 61.883], [-96.575, 59.231], [-37.565, 0.221], [-52.713, -14.927], [-52.713, -17.579], [-17.8, -52.492], [-15.148, -52.492], [0, -37.344], [15.148, -52.492], [17.8, -52.492]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 8, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0], [0, 0], [0.732, -0.732], [0, 0]], "v": [[44.601, -9.467], [44.601, -6.815], [37.565, 0.221], [98.561, 61.217], [98.561, 63.869], [63.648, 98.782], [60.996, 98.782], [0, 37.786], [-60.996, 98.782], [-63.648, 98.782], [-98.561, 63.869], [-98.561, 61.217], [-37.565, 0.221], [-44.601, -6.815], [-44.601, -9.467], [-9.688, -44.38], [-7.036, -44.38], [0, -37.344], [7.036, -44.38], [9.688, -44.38]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 9, "s": [{"i": [[0, 0], [-0.846, -0.781], [0, 0], [0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0, 0], [0.696, -0.76], [0, 0], [0.732, -0.732], [0, 0], [0, 0], [0.732, -0.732], [0, 0]], "v": [[36.181, -1.08], [37.574, 0.194], [37.565, 0.221], [100.615, 63.271], [100.615, 65.923], [65.702, 100.836], [63.05, 100.836], [0, 37.786], [-62.79, 100.576], [-65.441, 100.576], [-100.355, 65.662], [-100.355, 63.01], [-37.565, 0.221], [-37.553, 0.174], [-36.25, -1.109], [-2.671, -35.504], [-0.019, -35.504], [0, -35.475], [0.009, -35.491], [2.661, -35.491]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 10, "s": [{"i": [[0, 0], [-0.846, -0.781], [0, 0], [0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0, 0], [0.696, -0.76], [0, 0], [0.732, -0.732], [0, 0], [0, 0], [0.732, -0.732], [0, 0]], "v": [[36.181, -1.08], [37.574, 0.194], [37.565, 0.221], [102.61, 65.266], [102.61, 67.918], [67.697, 102.831], [65.045, 102.831], [0, 37.786], [-64.784, 102.57], [-67.436, 102.57], [-102.349, 67.657], [-102.349, 65.005], [-37.565, 0.221], [-37.553, 0.174], [-36.25, -1.109], [-12.611, -25.049], [-9.959, -25.049], [0, -15.263], [9.65, -24.736], [12.302, -24.736]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 11, "s": [{"i": [[0, 0], [-0.846, -0.781], [0, 0], [0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0, 0], [0.696, -0.76], [0, 0], [0.732, -0.732], [0, 0], [0, 0], [0.732, -0.732], [0, 0]], "v": [[36.181, -1.08], [37.574, 0.194], [37.565, 0.221], [105.263, 67.919], [105.263, 70.57], [70.35, 105.484], [67.698, 105.484], [0, 37.786], [-67.437, 105.223], [-70.089, 105.223], [-105.002, 70.31], [-105.002, 67.658], [-37.565, 0.221], [-37.553, 0.174], [-36.25, -1.109], [-22.811, -14.821], [-20.159, -14.821], [0, 5.165], [20.172, -14.233], [22.823, -14.233]], "c": true}]}, {"t": 12, "s": [{"i": [[0, 0], [-0.846, -0.781], [0, 0], [0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0, 0], [0.696, -0.76], [0, 0], [0.732, -0.732], [0, 0], [0, 0], [0.732, -0.732], [0, 0]], "v": [[36.181, -1.08], [37.574, 0.194], [37.565, 0.221], [107.239, 69.895], [107.239, 72.546], [72.325, 107.46], [69.674, 107.46], [0, 37.786], [-69.413, 107.199], [-72.065, 107.199], [-106.978, 72.286], [-106.978, 69.634], [-37.565, 0.221], [-37.553, 0.174], [-36.25, -1.109], [-33.825, -3.837], [-31.173, -3.837], [0, 28.876], [31.428, -3.096], [34.08, -3.096]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.2, 0.2, 0.2, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-25-error-cross').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-25-error-cross').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "body", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 13, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "body 8", "parent": 14, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.513, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[-84.777, -122.121], [-84.777, -119.469], [-119.69, -84.556], [-122.342, -84.556], [-125.734, -87.948], [-125.734, -90.6], [-90.82, -125.513], [-88.169, -125.513]], "c": true}]}, {"t": 46, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[87.946, 50.602], [87.946, 53.254], [53.033, 88.167], [50.381, 88.167], [-88.388, -50.602], [-88.388, -53.254], [-53.475, -88.167], [-50.823, -88.167]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.2, 0.2, 0.2, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-25-error-cross').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-25-error-cross').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "body", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 20, "op": 29, "st": 20, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "body 7", "parent": 14, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[87.946, 50.602], [87.946, 53.254], [53.033, 88.167], [50.381, 88.167], [-88.388, -50.602], [-88.388, -53.254], [-53.475, -88.167], [-50.823, -88.167]], "c": true}]}, {"t": 20, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[127.946, 90.602], [127.946, 93.254], [93.033, 128.167], [90.381, 128.167], [86.612, 124.398], [86.612, 121.746], [121.525, 86.833], [124.177, 86.833]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.2, 0.2, 0.2, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-25-error-cross').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-25-error-cross').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "body", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 13, "op": 20, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "body 6", "parent": 14, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.513, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[125.734, -90.6], [125.734, -87.948], [122.342, -84.556], [119.69, -84.556], [84.777, -119.469], [84.777, -122.121], [88.169, -125.513], [90.82, -125.513]], "c": true}]}, {"t": 46, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[88.388, -53.254], [88.388, -50.602], [-50.381, 88.167], [-53.033, 88.167], [-87.946, 53.254], [-87.946, 50.602], [50.823, -88.167], [53.475, -88.167]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.2, 0.2, 0.2, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-25-error-cross').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-25-error-cross').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "body", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 20, "op": 29, "st": 20, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "body 5", "parent": 14, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[88.388, -53.254], [88.388, -50.602], [-50.381, 88.167], [-53.033, 88.167], [-87.946, 53.254], [-87.946, 50.602], [50.823, -88.167], [53.475, -88.167]], "c": true}]}, {"t": 20, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[-86.612, 121.746], [-86.612, 124.398], [-90.381, 128.167], [-93.033, 128.167], [-127.946, 93.254], [-127.946, 90.602], [-124.177, 86.833], [-121.525, 86.833]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.2, 0.2, 0.2, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-25-error-cross').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-25-error-cross').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "body", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 13, "op": 20, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Vector 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [214.5, 215.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 9, "s": [94, 94, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 22, "s": [108, 108, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 40, "s": [98, 98, 100]}, {"t": 60, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-92.508, 0], [0, 92.508], [92.508, 0], [0, -92.508]], "o": [[92.508, 0], [0, -92.508], [-92.508, 0], [0, 92.508]], "v": [[0, 167.5], [167.5, 0], [0, -167.5], [-167.5, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.2, 0.2, 0.2, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-25-error-cross').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-25-error-cross').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 0, "nm": "mask", "parent": 14, "td": 1, "refId": "comp_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0.5, -0.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "Shape Layer 1", "tt": 1, "tp": 8, "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [121.5, 214.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-93.5, -0.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[92.5, -169.5], [92.5, 169.5], [-79.5, 169.5], [-79.5, -169.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.973, 0.98, 0.988, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-25-error-cross').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}, {"ty": "tr", "p": {"a": 0, "k": [-93.5, -0.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 1}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "body 4", "parent": 14, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.513, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[-84.777, -122.121], [-84.777, -119.469], [-119.69, -84.556], [-122.342, -84.556], [-125.734, -87.948], [-125.734, -90.6], [-90.82, -125.513], [-88.169, -125.513]], "c": true}]}, {"t": 46, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[87.946, 50.602], [87.946, 53.254], [53.033, 88.167], [50.381, 88.167], [-88.388, -50.602], [-88.388, -53.254], [-53.475, -88.167], [-50.823, -88.167]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.973, 0.98, 0.988, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-25-error-cross').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "body", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 20, "op": 98, "st": 20, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "body 2", "parent": 14, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[87.946, 50.602], [87.946, 53.254], [53.033, 88.167], [50.381, 88.167], [-88.388, -50.602], [-88.388, -53.254], [-53.475, -88.167], [-50.823, -88.167]], "c": true}]}, {"t": 20, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[127.946, 90.602], [127.946, 93.254], [93.033, 128.167], [90.381, 128.167], [86.612, 124.398], [86.612, 121.746], [121.525, 86.833], [124.177, 86.833]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.973, 0.98, 0.988, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-25-error-cross').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "body", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 20, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "body 3", "parent": 14, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.513, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[125.734, -90.6], [125.734, -87.948], [122.342, -84.556], [119.69, -84.556], [84.777, -119.469], [84.777, -122.121], [88.169, -125.513], [90.82, -125.513]], "c": true}]}, {"t": 46, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[88.388, -53.254], [88.388, -50.602], [-50.381, 88.167], [-53.033, 88.167], [-87.946, 53.254], [-87.946, 50.602], [50.823, -88.167], [53.475, -88.167]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.973, 0.98, 0.988, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-25-error-cross').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "body", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 20, "op": 98, "st": 20, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "body", "parent": 14, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[88.388, -53.254], [88.388, -50.602], [-50.381, 88.167], [-53.033, 88.167], [-87.946, 53.254], [-87.946, 50.602], [50.823, -88.167], [53.475, -88.167]], "c": true}]}, {"t": 20, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[-86.612, 121.746], [-86.612, 124.398], [-90.381, 128.167], [-93.033, 128.167], [-127.946, 93.254], [-127.946, 90.602], [-124.177, 86.833], [-121.525, 86.833]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.973, 0.98, 0.988, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-25-error-cross').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "body", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 20, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [214.5, 215.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 9, "s": [94, 94, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 22, "s": [108, 108, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 40, "s": [98, 98, 100]}, {"t": 60, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-23.413, 3.619], [-19.879, 12.887], [38.127, 15.45], [18.22, 36.884], [-10.9, 39.668], [-34.508, 22.395], [17.102, -16.394], [8.891, -21.959], [-0.877, -23.674], [-10.492, -21.24], [-18.268, -15.084], [-22.842, -6.283]], "o": [[23.413, -3.619], [-38.756, 13.796], [-38.127, -15.45], [-18.22, -36.884], [10.9, -39.668], [-22.315, 7.956], [-17.102, 16.394], [-8.891, 21.959], [0.877, 23.674], [10.492, 21.24], [18.268, 15.084], [22.842, 6.283]], "v": [[63.926, 161.034], [129.599, 135.995], [10.244, 133.429], [-77.231, 52.185], [-88.594, -66.656], [-18.101, -163.005], [-77.895, -126.065], [-117.327, -67.884], [-129.484, 1.342], [-112.237, 69.477], [-68.607, 124.58], [-6.242, 156.993]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.753, 0.141, 0.714, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-25-error-cross').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [-38.188, 4.656], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 50, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 2", "np": 2, "cix": 2, "bm": 1, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-92.508, 0], [0, 92.508], [92.508, 0], [0, -92.508]], "o": [[92.508, 0], [0, -92.508], [-92.508, 0], [0, 92.508]], "v": [[0, 167.5], [167.5, 0], [0, -167.5], [-167.5, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.753, 0.141, 0.714, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-25-error-cross').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}]}, {"id": "comp_3", "nm": "mask", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "mask", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [214.5, 215.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.513, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[-84.777, -122.121], [-84.777, -119.469], [-119.69, -84.556], [-122.342, -84.556], [-125.734, -87.948], [-125.734, -90.6], [-90.82, -125.513], [-88.169, -125.513]], "c": true}]}, {"t": 46, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[87.946, 50.602], [87.946, 53.254], [53.033, 88.167], [50.381, 88.167], [-88.388, -50.602], [-88.388, -53.254], [-53.475, -88.167], [-50.823, -88.167]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "body", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 20, "op": 128, "st": 20, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "mask", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [214.5, 215.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[87.946, 50.602], [87.946, 53.254], [53.033, 88.167], [50.381, 88.167], [-88.388, -50.602], [-88.388, -53.254], [-53.475, -88.167], [-50.823, -88.167]], "c": true}]}, {"t": 20, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[127.946, 90.602], [127.946, 93.254], [93.033, 128.167], [90.381, 128.167], [86.612, 124.398], [86.612, 121.746], [121.525, 86.833], [124.177, 86.833]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "body", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 20, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "mask", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [214.5, 215.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.513, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 20, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[125.734, -90.6], [125.734, -87.948], [122.342, -84.556], [119.69, -84.556], [84.777, -119.469], [84.777, -122.121], [88.169, -125.513], [90.82, -125.513]], "c": true}]}, {"t": 46, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[88.388, -53.254], [88.388, -50.602], [-50.381, 88.167], [-53.033, 88.167], [-87.946, 53.254], [-87.946, 50.602], [50.823, -88.167], [53.475, -88.167]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "body", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 20, "op": 128, "st": 20, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "mask", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [214.5, 215.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[88.388, -53.254], [88.388, -50.602], [-50.381, 88.167], [-53.033, 88.167], [-87.946, 53.254], [-87.946, 50.602], [50.823, -88.167], [53.475, -88.167]], "c": true}]}, {"t": 20, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[-86.612, 121.746], [-86.612, 124.398], [-90.381, 128.167], [-93.033, 128.167], [-127.946, 93.254], [-127.946, 90.602], [-124.177, 86.833], [-121.525, 86.833]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "body", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 20, "st": 0, "ct": 1, "bm": 0}]}, {"id": "comp_5", "nm": "mask-4", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "mask", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [214.5, 215.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.513, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 29, "s": [{"i": [[0, 0], [0.495, -0.627], [0, 0], [0.123, 0.789], [0, 0], [-0.495, 0.627], [0, 0], [-0.123, -0.789]], "o": [[0.123, 0.789], [0, 0], [-0.495, 0.627], [0, 0], [-0.123, -0.789], [0, 0], [0.495, -0.627], [0, 0]], "v": [[-12.267, -119.286], [-12.942, -116.722], [-36.555, -86.831], [-37.674, -87.126], [-38.243, -90.783], [-37.569, -93.347], [-13.955, -123.237], [-12.836, -122.943]], "c": true}]}, {"t": 60, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[87.946, 50.602], [87.946, 53.254], [53.033, 88.167], [50.381, 88.167], [-88.388, -50.602], [-88.388, -53.254], [-53.475, -88.167], [-50.823, -88.167]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.949019610882, 0.298039227724, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "body", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 29, "op": 111, "st": 20, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "mask", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [214.5, 215.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[87.946, 50.602], [87.946, 53.254], [53.033, 88.167], [50.381, 88.167], [-88.388, -50.602], [-88.388, -53.254], [-53.475, -88.167], [-50.823, -88.167]], "c": true}]}, {"t": 14, "s": [{"i": [[0, 0], [0.541, -0.638], [0, 0], [0.251, 0.798], [0, 0], [-0.541, 0.638], [0, 0], [-0.251, -0.798]], "o": [[0.251, 0.798], [0, 0], [-0.541, 0.638], [0, 0], [-0.251, -0.798], [0, 0], [0.541, -0.638], [0, 0]], "v": [[47.219, 93.199], [46.694, 95.798], [20.886, 126.198], [19.452, 125.908], [18.16, 121.801], [18.686, 119.202], [44.493, 88.802], [45.928, 89.092]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.949019610882, 0.298039227724, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "body", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 15, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "mask", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [214.5, 215.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.513, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 29, "s": [{"i": [[0, 0], [0.153, -0.788], [0, 0], [0.486, 0.639], [0, 0], [-0.153, 0.788], [0, 0], [-0.486, -0.639]], "o": [[0.486, 0.639], [0, 0], [-0.153, 0.788], [0, 0], [-0.486, -0.639], [0, 0], [0.153, -0.788], [0, 0]], "v": [[37.47, -93.06], [38.072, -90.478], [37.362, -86.829], [36.205, -86.559], [13.04, -117.008], [12.438, -119.591], [13.148, -123.239], [14.306, -123.509]], "c": true}]}, {"t": 60, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[88.388, -53.254], [88.388, -50.602], [-50.381, 88.167], [-53.033, 88.167], [-87.946, 53.254], [-87.946, 50.602], [50.823, -88.167], [53.475, -88.167]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.949019610882, 0.298039227724, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "body", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 29, "op": 111, "st": 20, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "mask", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [214.5, 215.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[88.388, -53.254], [88.388, -50.602], [-50.381, 88.167], [-53.033, 88.167], [-87.946, 53.254], [-87.946, 50.602], [50.823, -88.167], [53.475, -88.167]], "c": true}]}, {"t": 14, "s": [{"i": [[0, 0], [0.343, -0.801], [0, 0], [0.589, 0.642], [0, 0], [-0.343, 0.801], [0, 0], [-0.589, -0.642]], "o": [[0.589, 0.642], [0, 0], [-0.343, 0.801], [0, 0], [-0.589, -0.642], [0, 0], [0.343, -0.801], [0, 0]], "v": [[-19.147, 119.301], [-18.701, 121.915], [-20.466, 126.04], [-22.153, 126.328], [-50.232, 95.699], [-50.678, 93.085], [-48.914, 88.96], [-47.227, 88.672]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.949019610882, 0.298039227724, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "body", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 15, "st": 0, "ct": 1, "bm": 0}]}, {"id": "comp_7", "nm": "mask-3", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "mask", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [214.5, 215.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.336, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[-84.777, -122.121], [-84.777, -119.469], [-119.69, -84.556], [-122.342, -84.556], [-125.734, -87.948], [-125.734, -90.6], [-90.82, -125.513], [-88.169, -125.513]], "c": true}]}, {"t": 30, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[87.946, 50.602], [87.946, 53.254], [53.033, 88.167], [50.381, 88.167], [-88.388, -50.602], [-88.388, -53.254], [-53.475, -88.167], [-50.823, -88.167]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "body", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 1, "op": 103, "st": 20, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "mask", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [214.5, 215.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.336, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[125.734, -90.6], [125.734, -87.948], [122.342, -84.556], [119.69, -84.556], [84.777, -119.469], [84.777, -122.121], [88.169, -125.513], [90.82, -125.513]], "c": true}]}, {"t": 30, "s": [{"i": [[0, 0], [0.732, -0.732], [0, 0], [0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732]], "o": [[0.732, 0.732], [0, 0], [-0.732, 0.732], [0, 0], [-0.732, -0.732], [0, 0], [0.732, -0.732], [0, 0]], "v": [[88.388, -53.254], [88.388, -50.602], [-50.381, 88.167], [-53.033, 88.167], [-87.946, 53.254], [-87.946, 50.602], [50.823, -88.167], [53.475, -88.167]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "body", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 1, "op": 103, "st": 20, "ct": 1, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "control", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "stroke", "np": 3, "mn": "Pseudo/@@vK3UAC15T+OZvONZJAQbuw", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "<PERSON><PERSON>", "mn": "Pseudo/@@vK3UAC15T+OZvONZJAQbuw-0001", "ix": 1, "v": {"a": 0, "k": 2, "ix": 1}}]}, {"ty": 5, "nm": "primary", "np": 3, "mn": "ADBE Color Control", "ix": 2, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.2, 0.2, 0.2], "ix": 1}}]}, {"ty": 5, "nm": "secondary", "np": 3, "mn": "ADBE Color Control", "ix": 3, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.753, 0.141, 0.714], "ix": 1}}]}, {"ty": 5, "nm": "tertiary", "np": 3, "mn": "ADBE Color Control", "ix": 4, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.973, 0.98, 0.988], "ix": 1}}]}], "ip": 0, "op": 271, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "hover-pinch", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 0, "op": 70, "st": 0, "bm": 0}], "markers": [{"tm": 0, "cm": "default:hover-pinch", "dr": 60}]}