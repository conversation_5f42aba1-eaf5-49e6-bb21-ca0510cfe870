{"v": "5.12.1", "fr": 60, "ip": 0, "op": 180, "w": 430, "h": 430, "nm": "wired-lineal-1257-x-ray", "ddd": 0, "assets": [{"id": "comp_1", "nm": "hover-pinch", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "outline", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[11.558, 0], [0, 0], [0, 11.558], [0, 0], [-11.558, 0], [0, 0], [0, -11.558], [0, 0]], "o": [[0, 0], [-11.558, 0], [0, 0], [0, -11.558], [0, 0], [11.558, 0], [0, 0], [0, 11.558]], "v": [[141.271, 133.433], [-141.271, 133.433], [-162.199, 112.504], [-162.199, -112.504], [-141.271, -133.433], [141.271, -133.433], [162.199, -112.504], [162.199, 112.504]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.188, 0.137, 0.682, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1257-x-ray').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-1257-x-ray').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 908, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "outline 7", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215.001, 214.531, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [250.001, 249.531, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.13, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[13.366, 13.298], [-13.32, 13.343], [-13.366, -13.343]], "c": false}]}, {"i": {"x": 0.13, "y": 1}, "o": {"x": 0.87, "y": 0}, "t": 38, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[21.991, 17.08], [-18.132, 17.127], [-18.179, -22.996]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.87, "y": 0}, "t": 149, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[21.991, 17.08], [-18.132, 17.127], [-18.179, -22.996]], "c": false}]}, {"t": 180, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[13.366, 13.298], [-13.32, 13.343], [-13.366, -13.343]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 1, "k": [{"i": {"x": 0.13, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[234.323, 13.343], [261.01, 13.343], [261.01, -13.343]], "c": false}]}, {"i": {"x": 0.13, "y": 1}, "o": {"x": 0.87, "y": 0}, "t": 38, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[225.7, 17.127], [265.823, 17.127], [265.823, -22.996]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.87, "y": 0}, "t": 149, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[225.7, 17.127], [265.823, 17.127], [265.823, -22.996]], "c": false}]}, {"t": 180, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[234.323, 13.343], [261.01, 13.343], [261.01, -13.343]], "c": false}]}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 1, "k": [{"i": {"x": 0.13, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[234.323, -202.344], [261.01, -202.344], [261.01, -175.658]], "c": false}]}, {"i": {"x": 0.13, "y": 1}, "o": {"x": 0.87, "y": 0}, "t": 38, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[225.7, -206.128], [265.823, -206.128], [265.823, -166.005]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.87, "y": 0}, "t": 149, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[225.7, -206.128], [265.823, -206.128], [265.823, -166.005]], "c": false}]}, {"t": 180, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[234.323, -202.344], [261.01, -202.344], [261.01, -175.658]], "c": false}]}], "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 1, "k": [{"i": {"x": 0.13, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[13.32, -202.357], [-13.366, -202.311], [-13.32, -175.624]], "c": false}]}, {"i": {"x": 0.13, "y": 1}, "o": {"x": 0.87, "y": 0}, "t": 38, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[21.943, -206.141], [-18.179, -206.093], [-18.132, -165.97]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.87, "y": 0}, "t": 149, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[21.943, -206.141], [-18.179, -206.093], [-18.132, -165.97]], "c": false}]}, {"t": 180, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[13.32, -202.357], [-13.366, -202.311], [-13.32, -175.624]], "c": false}]}], "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.753, 0.141, 0.714, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1257-x-ray').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-1257-x-ray').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [126.179, 344.038], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "outline 6", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.22], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 18, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.78], "y": [0]}, "t": 30, "s": [40]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 45, "s": [100]}, {"i": {"x": [0.22], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 78, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.78], "y": [0]}, "t": 90, "s": [40]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 105, "s": [100]}, {"i": {"x": [0.22], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 138, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.78], "y": [0]}, "t": 150, "s": [40]}, {"t": 165, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.22, "y": 1}, "o": {"x": 0.78, "y": 0}, "t": 0, "s": [214.811, 102.96, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.22, "y": 1}, "o": {"x": 0.78, "y": 0}, "t": 60, "s": [214.811, 325.96, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.22, "y": 1}, "o": {"x": 0.78, "y": 0}, "t": 120, "s": [214.811, 102.96, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 180, "s": [214.811, 322.96, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [249.811, 137.96, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 1, "s": [100, 23, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 18, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 30, "s": [100, 500, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 45, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 78, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 90, "s": [100, 500, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 105, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 138, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 150, "s": [100, 500, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -0.005, 0]}, "t": 165, "s": [100, 100, 100]}, {"t": 180, "s": [100, 23, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.13, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[259.943, -203.118], [-12.679, -202.038]], "c": false}]}, {"i": {"x": 0.13, "y": 1}, "o": {"x": 0.87, "y": 0}, "t": 38, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[265.443, -206.062], [-18.179, -206.093]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.87, "y": 0}, "t": 149, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[265.443, -206.062], [-18.179, -206.093]], "c": false}]}, {"t": 180, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[259.943, -203.118], [-12.679, -202.038]], "c": false}]}], "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.188, 0.137, 0.682, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1257-x-ray').layer('control').effect('quaternary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-1257-x-ray').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".quaternary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "quaternary"}, {"ty": "tr", "p": {"a": 0, "k": [126.179, 344.038], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 1, "op": 180, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "outline 5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [250, 250, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.3, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [-14.381, 1.845], [-18.968, -3.552], [-0.141, 4.784], [6.137, 23.496], [17.969, 5.747], [0, -9.303], [0, 0], [-0.061, -0.718]], "o": [[1.197, 14.177], [9.794, -1.257], [4.705, 0.881], [0.467, -15.863], [-7.449, -28.52], [-8.861, -2.834], [0, 0], [0, 0.735], [0, 0]], "v": [[18.803, 75.21], [47.136, 98.304], [84.524, 96.277], [93.743, 88.831], [88.092, 17.205], [36.634, -30.147], [18.712, -17.111], [18.712, 73.031], [18.803, 75.21]], "c": false}]}, {"i": {"x": 0.3, "y": 1}, "o": {"x": 0.7, "y": 0}, "t": 15, "s": [{"i": [[0, 0], [-13.368, 1.986], [-17.631, -3.823], [-0.131, 5.15], [5.705, 25.29], [16.703, 6.186], [0, -10.013], [0, 0], [-0.056, -0.773]], "o": [[1.112, 15.259], [9.104, -1.353], [4.373, 0.948], [0.434, -17.074], [-6.924, -30.697], [-8.236, -3.05], [0, 0], [0, 0.791], [0, 0]], "v": [[18.889, 80.868], [45.226, 105.725], [79.978, 103.542], [88.548, 95.529], [83.295, 18.435], [35.464, -32.531], [18.804, -18.5], [18.804, 78.522], [18.889, 80.868]], "c": false}]}, {"i": {"x": 0.3, "y": 1}, "o": {"x": 0.7, "y": 0}, "t": 37.879, "s": [{"i": [[0, 0], [-15.186, 1.753], [-20.028, -3.374], [-0.149, 4.544], [6.48, 22.317], [18.974, 5.459], [0, -8.836], [0, 0], [-0.064, -0.682]], "o": [[1.264, 13.466], [10.342, -1.194], [4.968, 0.837], [0.494, -15.067], [-7.866, -27.089], [-9.357, -2.692], [0, 0], [0, 0.698], [0, 0]], "v": [[18.73, 69.815], [48.648, 91.75], [88.126, 89.824], [97.862, 82.752], [91.895, 14.719], [37.558, -30.257], [18.633, -17.875], [18.633, 67.744], [18.73, 69.815]], "c": false}]}, {"i": {"x": 0.3, "y": 1}, "o": {"x": 0.7, "y": 0}, "t": 59.666, "s": [{"i": [[0, 0], [-13.368, 1.986], [-17.631, -3.823], [-0.131, 5.149], [5.705, 25.289], [16.703, 6.186], [0, -10.013], [0, 0], [-0.056, -0.773]], "o": [[1.112, 15.259], [9.104, -1.353], [4.373, 0.948], [0.434, -17.074], [-6.924, -30.697], [-8.236, -3.05], [0, 0], [0, 0.791], [0, 0]], "v": [[18.889, 80.868], [45.226, 105.725], [79.978, 103.542], [88.548, 95.529], [83.295, 18.435], [35.464, -32.531], [18.804, -18.5], [18.804, 78.522], [18.889, 80.868]], "c": false}]}, {"i": {"x": 0.3, "y": 1}, "o": {"x": 0.7, "y": 0}, "t": 82.545, "s": [{"i": [[0, 0], [-15.186, 1.753], [-20.028, -3.374], [-0.149, 4.544], [6.48, 22.317], [18.974, 5.459], [0, -8.836], [0, 0], [-0.064, -0.682]], "o": [[1.264, 13.466], [10.342, -1.194], [4.968, 0.837], [0.494, -15.067], [-7.866, -27.089], [-9.357, -2.692], [0, 0], [0, 0.698], [0, 0]], "v": [[18.73, 69.815], [48.648, 91.75], [88.126, 89.824], [97.862, 82.752], [91.895, 14.719], [37.558, -30.257], [18.633, -17.875], [18.633, 67.744], [18.73, 69.815]], "c": false}]}, {"i": {"x": 0.3, "y": 1}, "o": {"x": 0.7, "y": 0}, "t": 104.334, "s": [{"i": [[0, 0], [-13.368, 1.986], [-17.631, -3.823], [-0.131, 5.149], [5.705, 25.289], [16.703, 6.186], [0, -10.013], [0, 0], [-0.056, -0.773]], "o": [[1.112, 15.259], [9.104, -1.353], [4.373, 0.948], [0.434, -17.074], [-6.924, -30.697], [-8.236, -3.05], [0, 0], [0, 0.791], [0, 0]], "v": [[18.889, 80.868], [45.226, 105.725], [79.978, 103.542], [88.548, 95.529], [83.295, 18.435], [35.464, -32.531], [18.804, -18.5], [18.804, 78.522], [18.889, 80.868]], "c": false}]}, {"i": {"x": 0.3, "y": 1}, "o": {"x": 0.7, "y": 0}, "t": 127.211, "s": [{"i": [[0, 0], [-15.186, 1.753], [-20.028, -3.374], [-0.149, 4.544], [6.48, 22.317], [18.974, 5.459], [0, -8.836], [0, 0], [-0.064, -0.682]], "o": [[1.264, 13.466], [10.342, -1.194], [4.968, 0.837], [0.494, -15.067], [-7.866, -27.089], [-9.357, -2.692], [0, 0], [0, 0.698], [0, 0]], "v": [[18.73, 69.815], [48.648, 91.75], [88.126, 89.824], [97.862, 82.752], [91.895, 14.719], [37.558, -30.257], [18.633, -17.875], [18.633, 67.744], [18.73, 69.815]], "c": false}]}, {"i": {"x": 0.13, "y": 1}, "o": {"x": 0.7, "y": 0}, "t": 149, "s": [{"i": [[0, 0], [-13.368, 1.986], [-17.631, -3.823], [-0.131, 5.149], [5.705, 25.289], [16.703, 6.186], [0, -10.013], [0, 0], [-0.056, -0.773]], "o": [[1.112, 15.259], [9.104, -1.353], [4.373, 0.948], [0.434, -17.074], [-6.924, -30.697], [-8.236, -3.05], [0, 0], [0, 0.791], [0, 0]], "v": [[18.889, 80.868], [45.226, 105.725], [79.978, 103.542], [88.548, 95.529], [83.295, 18.435], [35.464, -32.531], [18.804, -18.5], [18.804, 78.522], [18.889, 80.868]], "c": false}]}, {"t": 180, "s": [{"i": [[0, 0], [-14.381, 1.845], [-18.968, -3.552], [-0.141, 4.784], [6.137, 23.496], [17.969, 5.747], [0, -9.303], [0, 0], [-0.061, -0.718]], "o": [[1.197, 14.177], [9.794, -1.257], [4.705, 0.881], [0.467, -15.863], [-7.449, -28.52], [-8.861, -2.834], [0, 0], [0, 0.735], [0, 0]], "v": [[18.803, 75.21], [47.136, 98.304], [84.524, 96.277], [93.743, 88.831], [88.092, 17.205], [36.634, -30.147], [18.712, -17.111], [18.712, 73.031], [18.803, 75.21]], "c": false}]}], "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 1, "k": [{"i": {"x": 0.3, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [14.381, 1.845], [18.968, -3.552], [0.141, 4.784], [-6.137, 23.496], [-17.969, 5.747], [0, -9.303], [0, 0], [0.061, -0.718]], "o": [[-1.197, 14.177], [-9.794, -1.257], [-4.705, 0.881], [-0.467, -15.863], [7.449, -28.52], [8.861, -2.834], [0, 0], [0, 0.735], [0, 0]], "v": [[-18.803, 77.262], [-47.136, 100.355], [-84.524, 98.328], [-93.743, 90.882], [-88.092, 19.256], [-36.634, -28.096], [-18.712, -15.06], [-18.712, 75.082], [-18.803, 77.262]], "c": false}]}, {"i": {"x": 0.3, "y": 1}, "o": {"x": 0.7, "y": 0}, "t": 15, "s": [{"i": [[0, 0], [13.403, 1.986], [17.677, -3.823], [0.131, 5.15], [-5.72, 25.29], [-16.746, 6.186], [0, -10.013], [0, 0], [0.056, -0.773]], "o": [[-1.115, 15.259], [-9.127, -1.353], [-4.385, 0.948], [-0.436, -17.074], [6.943, -30.697], [8.258, -3.05], [0, 0], [0, 0.791], [0, 0]], "v": [[-18.705, 83.076], [-45.11, 107.932], [-79.954, 105.75], [-88.547, 97.737], [-83.28, 20.643], [-35.323, -30.323], [-18.619, -16.292], [-18.619, 80.73], [-18.705, 83.076]], "c": false}]}, {"i": {"x": 0.3, "y": 1}, "o": {"x": 0.7, "y": 0}, "t": 37.879, "s": [{"i": [[0, 0], [15.186, 1.753], [20.028, -3.374], [0.149, 4.544], [-6.48, 22.317], [-18.974, 5.459], [0, -8.836], [0, 0], [0.064, -0.682]], "o": [[-1.264, 13.466], [-10.342, -1.194], [-4.968, 0.837], [-0.494, -15.067], [7.866, -27.089], [9.357, -2.692], [0, 0], [0, 0.698], [0, 0]], "v": [[-18.98, 71.711], [-48.898, 93.647], [-88.376, 91.721], [-98.112, 84.649], [-92.145, 16.616], [-37.808, -28.36], [-18.883, -15.978], [-18.883, 69.641], [-18.98, 71.711]], "c": false}]}, {"i": {"x": 0.3, "y": 1}, "o": {"x": 0.7, "y": 0}, "t": 59.666, "s": [{"i": [[0, 0], [13.403, 1.986], [17.677, -3.823], [0.131, 5.15], [-5.72, 25.29], [-16.746, 6.186], [0, -10.013], [0, 0], [0.056, -0.773]], "o": [[-1.115, 15.259], [-9.127, -1.353], [-4.385, 0.948], [-0.436, -17.074], [6.943, -30.697], [8.258, -3.05], [0, 0], [0, 0.791], [0, 0]], "v": [[-18.705, 83.076], [-45.11, 107.932], [-79.954, 105.75], [-88.547, 97.737], [-83.28, 20.643], [-35.323, -30.323], [-18.619, -16.292], [-18.619, 80.73], [-18.705, 83.076]], "c": false}]}, {"i": {"x": 0.3, "y": 1}, "o": {"x": 0.7, "y": 0}, "t": 82.545, "s": [{"i": [[0, 0], [15.186, 1.753], [20.028, -3.374], [0.149, 4.544], [-6.48, 22.317], [-18.974, 5.459], [0, -8.836], [0, 0], [0.064, -0.682]], "o": [[-1.264, 13.466], [-10.342, -1.194], [-4.968, 0.837], [-0.494, -15.067], [7.866, -27.089], [9.357, -2.692], [0, 0], [0, 0.698], [0, 0]], "v": [[-18.98, 71.711], [-48.898, 93.647], [-88.376, 91.721], [-98.112, 84.649], [-92.145, 16.616], [-37.808, -28.36], [-18.883, -15.978], [-18.883, 69.641], [-18.98, 71.711]], "c": false}]}, {"i": {"x": 0.3, "y": 1}, "o": {"x": 0.7, "y": 0}, "t": 104.334, "s": [{"i": [[0, 0], [13.403, 1.986], [17.677, -3.823], [0.131, 5.15], [-5.72, 25.29], [-16.746, 6.186], [0, -10.013], [0, 0], [0.056, -0.773]], "o": [[-1.115, 15.259], [-9.127, -1.353], [-4.385, 0.948], [-0.436, -17.074], [6.943, -30.697], [8.258, -3.05], [0, 0], [0, 0.791], [0, 0]], "v": [[-18.705, 83.076], [-45.11, 107.932], [-79.954, 105.75], [-88.547, 97.737], [-83.28, 20.643], [-35.323, -30.323], [-18.619, -16.292], [-18.619, 80.73], [-18.705, 83.076]], "c": false}]}, {"i": {"x": 0.3, "y": 1}, "o": {"x": 0.7, "y": 0}, "t": 127.211, "s": [{"i": [[0, 0], [15.186, 1.753], [20.028, -3.374], [0.149, 4.544], [-6.48, 22.317], [-18.974, 5.459], [0, -8.836], [0, 0], [0.064, -0.682]], "o": [[-1.264, 13.466], [-10.342, -1.194], [-4.968, 0.837], [-0.494, -15.067], [7.866, -27.089], [9.357, -2.692], [0, 0], [0, 0.698], [0, 0]], "v": [[-18.98, 71.711], [-48.898, 93.647], [-88.376, 91.721], [-98.112, 84.649], [-92.145, 16.616], [-37.808, -28.36], [-18.883, -15.978], [-18.883, 69.641], [-18.98, 71.711]], "c": false}]}, {"i": {"x": 0.13, "y": 1}, "o": {"x": 0.7, "y": 0}, "t": 149, "s": [{"i": [[0, 0], [13.403, 1.986], [17.677, -3.823], [0.131, 5.15], [-5.72, 25.29], [-16.746, 6.186], [0, -10.013], [0, 0], [0.056, -0.773]], "o": [[-1.115, 15.259], [-9.127, -1.353], [-4.385, 0.948], [-0.436, -17.074], [6.943, -30.697], [8.258, -3.05], [0, 0], [0, 0.791], [0, 0]], "v": [[-18.705, 83.076], [-45.11, 107.932], [-79.954, 105.75], [-88.547, 97.737], [-83.28, 20.643], [-35.323, -30.323], [-18.619, -16.292], [-18.619, 80.73], [-18.705, 83.076]], "c": false}]}, {"t": 180, "s": [{"i": [[0, 0], [14.381, 1.845], [18.968, -3.552], [0.141, 4.784], [-6.137, 23.496], [-17.969, 5.747], [0, -9.303], [0, 0], [0.061, -0.718]], "o": [[-1.197, 14.177], [-9.794, -1.257], [-4.705, 0.881], [-0.467, -15.863], [7.449, -28.52], [8.861, -2.834], [0, 0], [0, 0.735], [0, 0]], "v": [[-18.803, 77.262], [-47.136, 100.355], [-84.524, 98.328], [-93.743, 90.882], [-88.092, 19.256], [-36.634, -28.096], [-18.712, -15.06], [-18.712, 75.082], [-18.803, 77.262]], "c": false}]}], "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.188, 0.137, 0.682, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1257-x-ray').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-1257-x-ray').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "fl", "c": {"a": 0, "k": [0.753, 0.141, 0.714, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1257-x-ray').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [250, 236.081], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 11.367], [0, 0]], "o": [[0, 0], [0, 11.367], [0, 0]], "v": [[-18.712, 9.409], [0, -9.409], [18.712, 9.409]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -72.724], [0, -9.409]], "c": false}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.753, 0.141, 0.714, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1257-x-ray').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-1257-x-ray').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [250, 236.081], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 4, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Warstwa 4", "sr": 1, "ks": {"o": {"a": 0, "k": 50, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [82.443, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 11.558], [0, 0], [-11.558, 0], [0, 0], [0, -11.558], [0, 0], [-11.558, 0], [0, 0]], "o": [[0, 0], [0, -11.558], [0, 0], [-11.558, 0], [0, 0], [0, 11.558], [0, 0], [-11.558, 0]], "v": [[8.714, 112.504], [8.714, -112.504], [29.642, -133.433], [-8.714, -133.433], [-29.642, -112.504], [-29.642, 112.504], [-8.714, 133.433], [29.642, 133.433]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.973, 0.98, 0.988, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1257-x-ray').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 1, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 1}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Warstwa 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[11.558, 0], [0, 0], [0, 11.558], [0, 0], [-11.558, 0], [0, 0], [0, -11.558], [0, 0]], "o": [[0, 0], [-11.558, 0], [0, 0], [0, -11.558], [0, 0], [11.558, 0], [0, 0], [0, 11.558]], "v": [[141.271, 133.433], [-141.271, 133.433], [-162.199, 112.504], [-162.199, -112.504], [-141.271, -133.433], [141.271, -133.433], [162.199, -112.504], [162.199, 112.504]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.973, 0.98, 0.988, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1257-x-ray').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "outline 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215.001, 214.531, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [250.001, 249.531, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.13, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[13.366, 13.298], [-13.32, 13.343], [-13.366, -13.343]], "c": false}]}, {"i": {"x": 0.13, "y": 1}, "o": {"x": 0.87, "y": 0}, "t": 38, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[21.991, 17.08], [-18.132, 17.127], [-18.179, -22.996]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.87, "y": 0}, "t": 149, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[21.991, 17.08], [-18.132, 17.127], [-18.179, -22.996]], "c": false}]}, {"t": 180, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[13.366, 13.298], [-13.32, 13.343], [-13.366, -13.343]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 1, "k": [{"i": {"x": 0.13, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[234.323, 13.343], [261.01, 13.343], [261.01, -13.343]], "c": false}]}, {"i": {"x": 0.13, "y": 1}, "o": {"x": 0.87, "y": 0}, "t": 38, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[225.7, 17.127], [265.823, 17.127], [265.823, -22.996]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.87, "y": 0}, "t": 149, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[225.7, 17.127], [265.823, 17.127], [265.823, -22.996]], "c": false}]}, {"t": 180, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[234.323, 13.343], [261.01, 13.343], [261.01, -13.343]], "c": false}]}], "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 1, "k": [{"i": {"x": 0.13, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[234.323, -202.344], [261.01, -202.344], [261.01, -175.658]], "c": false}]}, {"i": {"x": 0.13, "y": 1}, "o": {"x": 0.87, "y": 0}, "t": 38, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[225.7, -206.128], [265.823, -206.128], [265.823, -166.005]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.87, "y": 0}, "t": 149, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[225.7, -206.128], [265.823, -206.128], [265.823, -166.005]], "c": false}]}, {"t": 180, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[234.323, -202.344], [261.01, -202.344], [261.01, -175.658]], "c": false}]}], "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 1, "k": [{"i": {"x": 0.13, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[13.32, -202.357], [-13.366, -202.311], [-13.32, -175.624]], "c": false}]}, {"i": {"x": 0.13, "y": 1}, "o": {"x": 0.87, "y": 0}, "t": 38, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[21.943, -206.141], [-18.179, -206.093], [-18.132, -165.97]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.87, "y": 0}, "t": 149, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[21.943, -206.141], [-18.179, -206.093], [-18.132, -165.97]], "c": false}]}, {"t": 180, "s": [{"i": [[0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0]], "v": [[13.32, -202.357], [-13.366, -202.311], [-13.32, -175.624]], "c": false}]}], "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.973, 0.98, 0.988, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1257-x-ray').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-1257-x-ray').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "tertiary"}, {"ty": "tr", "p": {"a": 0, "k": [126.179, 344.038], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 908, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "outline 4", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.22], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 18, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.78], "y": [0]}, "t": 30, "s": [40]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 45, "s": [100]}, {"i": {"x": [0.22], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 78, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.78], "y": [0]}, "t": 90, "s": [40]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 105, "s": [100]}, {"i": {"x": [0.22], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 138, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.78], "y": [0]}, "t": 150, "s": [40]}, {"t": 165, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.22, "y": 1}, "o": {"x": 0.78, "y": 0}, "t": 0, "s": [214.811, 102.96, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.22, "y": 1}, "o": {"x": 0.78, "y": 0}, "t": 60, "s": [214.811, 325.96, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.22, "y": 1}, "o": {"x": 0.78, "y": 0}, "t": 120, "s": [214.811, 102.96, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 180, "s": [214.811, 322.96, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [249.811, 137.96, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 1, "s": [100, 23, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 18, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 30, "s": [100, 500, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 45, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 78, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 90, "s": [100, 500, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 105, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 138, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 150, "s": [100, 500, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 0.833, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, -0.004, 0]}, "t": 165, "s": [100, 100, 100]}, {"t": 180, "s": [100, 0, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.13, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[259.943, -203.118], [-12.679, -202.038]], "c": false}]}, {"i": {"x": 0.13, "y": 1}, "o": {"x": 0.87, "y": 0}, "t": 38, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[265.443, -206.062], [-18.179, -206.093]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.87, "y": 0}, "t": 149, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[265.443, -206.062], [-18.179, -206.093]], "c": false}]}, {"t": 180, "s": [{"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[259.943, -203.118], [-12.679, -202.038]], "c": false}]}], "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.753, 0.141, 0.714, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1257-x-ray').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-1257-x-ray').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [126.179, 344.038], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 1, "op": 244, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "outline 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [250, 250, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 11.367], [0, 0]], "o": [[0, 0], [0, 11.367], [0, 0]], "v": [[-18.712, 9.409], [0, -9.409], [18.712, 9.409]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, -72.724], [0, -9.409]], "c": false}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 1, "k": [{"i": {"x": 0.3, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [-14.381, 1.845], [-18.968, -3.552], [-0.141, 4.784], [6.137, 23.496], [17.969, 5.747], [0, -9.303], [0, 0], [-0.061, -0.718]], "o": [[1.197, 14.177], [9.794, -1.257], [4.705, 0.881], [0.467, -15.863], [-7.449, -28.52], [-8.861, -2.834], [0, 0], [0, 0.735], [0, 0]], "v": [[18.803, 75.21], [47.136, 98.304], [84.524, 96.277], [93.743, 88.831], [88.092, 17.205], [36.634, -30.147], [18.712, -17.111], [18.712, 73.031], [18.803, 75.21]], "c": false}]}, {"i": {"x": 0.3, "y": 1}, "o": {"x": 0.7, "y": 0}, "t": 15, "s": [{"i": [[0, 0], [-13.368, 1.986], [-17.631, -3.823], [-0.131, 5.15], [5.705, 25.29], [16.703, 6.186], [0, -10.013], [0, 0], [-0.056, -0.773]], "o": [[1.112, 15.259], [9.104, -1.353], [4.373, 0.948], [0.434, -17.074], [-6.924, -30.697], [-8.236, -3.05], [0, 0], [0, 0.791], [0, 0]], "v": [[18.889, 80.868], [45.226, 105.725], [79.978, 103.542], [88.548, 95.529], [83.295, 18.435], [35.464, -32.531], [18.804, -18.5], [18.804, 78.522], [18.889, 80.868]], "c": false}]}, {"i": {"x": 0.3, "y": 1}, "o": {"x": 0.7, "y": 0}, "t": 37.879, "s": [{"i": [[0, 0], [-15.186, 1.753], [-20.028, -3.374], [-0.149, 4.544], [6.48, 22.317], [18.974, 5.459], [0, -8.836], [0, 0], [-0.064, -0.682]], "o": [[1.264, 13.466], [10.342, -1.194], [4.968, 0.837], [0.494, -15.067], [-7.866, -27.089], [-9.357, -2.692], [0, 0], [0, 0.698], [0, 0]], "v": [[18.73, 69.815], [48.648, 91.75], [88.126, 89.824], [97.862, 82.752], [91.895, 14.719], [37.558, -30.257], [18.633, -17.875], [18.633, 67.744], [18.73, 69.815]], "c": false}]}, {"i": {"x": 0.3, "y": 1}, "o": {"x": 0.7, "y": 0}, "t": 59.666, "s": [{"i": [[0, 0], [-13.368, 1.986], [-17.631, -3.823], [-0.131, 5.149], [5.705, 25.289], [16.703, 6.186], [0, -10.013], [0, 0], [-0.056, -0.773]], "o": [[1.112, 15.259], [9.104, -1.353], [4.373, 0.948], [0.434, -17.074], [-6.924, -30.697], [-8.236, -3.05], [0, 0], [0, 0.791], [0, 0]], "v": [[18.889, 80.868], [45.226, 105.725], [79.978, 103.542], [88.548, 95.529], [83.295, 18.435], [35.464, -32.531], [18.804, -18.5], [18.804, 78.522], [18.889, 80.868]], "c": false}]}, {"i": {"x": 0.3, "y": 1}, "o": {"x": 0.7, "y": 0}, "t": 82.545, "s": [{"i": [[0, 0], [-15.186, 1.753], [-20.028, -3.374], [-0.149, 4.544], [6.48, 22.317], [18.974, 5.459], [0, -8.836], [0, 0], [-0.064, -0.682]], "o": [[1.264, 13.466], [10.342, -1.194], [4.968, 0.837], [0.494, -15.067], [-7.866, -27.089], [-9.357, -2.692], [0, 0], [0, 0.698], [0, 0]], "v": [[18.73, 69.815], [48.648, 91.75], [88.126, 89.824], [97.862, 82.752], [91.895, 14.719], [37.558, -30.257], [18.633, -17.875], [18.633, 67.744], [18.73, 69.815]], "c": false}]}, {"i": {"x": 0.3, "y": 1}, "o": {"x": 0.7, "y": 0}, "t": 104.334, "s": [{"i": [[0, 0], [-13.368, 1.986], [-17.631, -3.823], [-0.131, 5.149], [5.705, 25.289], [16.703, 6.186], [0, -10.013], [0, 0], [-0.056, -0.773]], "o": [[1.112, 15.259], [9.104, -1.353], [4.373, 0.948], [0.434, -17.074], [-6.924, -30.697], [-8.236, -3.05], [0, 0], [0, 0.791], [0, 0]], "v": [[18.889, 80.868], [45.226, 105.725], [79.978, 103.542], [88.548, 95.529], [83.295, 18.435], [35.464, -32.531], [18.804, -18.5], [18.804, 78.522], [18.889, 80.868]], "c": false}]}, {"i": {"x": 0.3, "y": 1}, "o": {"x": 0.7, "y": 0}, "t": 127.211, "s": [{"i": [[0, 0], [-15.186, 1.753], [-20.028, -3.374], [-0.149, 4.544], [6.48, 22.317], [18.974, 5.459], [0, -8.836], [0, 0], [-0.064, -0.682]], "o": [[1.264, 13.466], [10.342, -1.194], [4.968, 0.837], [0.494, -15.067], [-7.866, -27.089], [-9.357, -2.692], [0, 0], [0, 0.698], [0, 0]], "v": [[18.73, 69.815], [48.648, 91.75], [88.126, 89.824], [97.862, 82.752], [91.895, 14.719], [37.558, -30.257], [18.633, -17.875], [18.633, 67.744], [18.73, 69.815]], "c": false}]}, {"i": {"x": 0.13, "y": 1}, "o": {"x": 0.7, "y": 0}, "t": 149, "s": [{"i": [[0, 0], [-13.368, 1.986], [-17.631, -3.823], [-0.131, 5.149], [5.705, 25.289], [16.703, 6.186], [0, -10.013], [0, 0], [-0.056, -0.773]], "o": [[1.112, 15.259], [9.104, -1.353], [4.373, 0.948], [0.434, -17.074], [-6.924, -30.697], [-8.236, -3.05], [0, 0], [0, 0.791], [0, 0]], "v": [[18.889, 80.868], [45.226, 105.725], [79.978, 103.542], [88.548, 95.529], [83.295, 18.435], [35.464, -32.531], [18.804, -18.5], [18.804, 78.522], [18.889, 80.868]], "c": false}]}, {"t": 180, "s": [{"i": [[0, 0], [-14.381, 1.845], [-18.968, -3.552], [-0.141, 4.784], [6.137, 23.496], [17.969, 5.747], [0, -9.303], [0, 0], [-0.061, -0.718]], "o": [[1.197, 14.177], [9.794, -1.257], [4.705, 0.881], [0.467, -15.863], [-7.449, -28.52], [-8.861, -2.834], [0, 0], [0, 0.735], [0, 0]], "v": [[18.803, 75.21], [47.136, 98.304], [84.524, 96.277], [93.743, 88.831], [88.092, 17.205], [36.634, -30.147], [18.712, -17.111], [18.712, 73.031], [18.803, 75.21]], "c": false}]}], "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 1, "k": [{"i": {"x": 0.3, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [14.381, 1.845], [18.968, -3.552], [0.141, 4.784], [-6.137, 23.496], [-17.969, 5.747], [0, -9.303], [0, 0], [0.061, -0.718]], "o": [[-1.197, 14.177], [-9.794, -1.257], [-4.705, 0.881], [-0.467, -15.863], [7.449, -28.52], [8.861, -2.834], [0, 0], [0, 0.735], [0, 0]], "v": [[-18.803, 77.262], [-47.136, 100.355], [-84.524, 98.328], [-93.743, 90.882], [-88.092, 19.256], [-36.634, -28.096], [-18.712, -15.06], [-18.712, 75.082], [-18.803, 77.262]], "c": false}]}, {"i": {"x": 0.3, "y": 1}, "o": {"x": 0.7, "y": 0}, "t": 15, "s": [{"i": [[0, 0], [13.403, 1.986], [17.677, -3.823], [0.131, 5.15], [-5.72, 25.29], [-16.746, 6.186], [0, -10.013], [0, 0], [0.056, -0.773]], "o": [[-1.115, 15.259], [-9.127, -1.353], [-4.385, 0.948], [-0.436, -17.074], [6.943, -30.697], [8.258, -3.05], [0, 0], [0, 0.791], [0, 0]], "v": [[-18.705, 83.076], [-45.11, 107.932], [-79.954, 105.75], [-88.547, 97.737], [-83.28, 20.643], [-35.323, -30.323], [-18.619, -16.292], [-18.619, 80.73], [-18.705, 83.076]], "c": false}]}, {"i": {"x": 0.3, "y": 1}, "o": {"x": 0.7, "y": 0}, "t": 37.879, "s": [{"i": [[0, 0], [15.186, 1.753], [20.028, -3.374], [0.149, 4.544], [-6.48, 22.317], [-18.974, 5.459], [0, -8.836], [0, 0], [0.064, -0.682]], "o": [[-1.264, 13.466], [-10.342, -1.194], [-4.968, 0.837], [-0.494, -15.067], [7.866, -27.089], [9.357, -2.692], [0, 0], [0, 0.698], [0, 0]], "v": [[-18.98, 71.711], [-48.898, 93.647], [-88.376, 91.721], [-98.112, 84.649], [-92.145, 16.616], [-37.808, -28.36], [-18.883, -15.978], [-18.883, 69.641], [-18.98, 71.711]], "c": false}]}, {"i": {"x": 0.3, "y": 1}, "o": {"x": 0.7, "y": 0}, "t": 59.666, "s": [{"i": [[0, 0], [13.403, 1.986], [17.677, -3.823], [0.131, 5.15], [-5.72, 25.29], [-16.746, 6.186], [0, -10.013], [0, 0], [0.056, -0.773]], "o": [[-1.115, 15.259], [-9.127, -1.353], [-4.385, 0.948], [-0.436, -17.074], [6.943, -30.697], [8.258, -3.05], [0, 0], [0, 0.791], [0, 0]], "v": [[-18.705, 83.076], [-45.11, 107.932], [-79.954, 105.75], [-88.547, 97.737], [-83.28, 20.643], [-35.323, -30.323], [-18.619, -16.292], [-18.619, 80.73], [-18.705, 83.076]], "c": false}]}, {"i": {"x": 0.3, "y": 1}, "o": {"x": 0.7, "y": 0}, "t": 82.545, "s": [{"i": [[0, 0], [15.186, 1.753], [20.028, -3.374], [0.149, 4.544], [-6.48, 22.317], [-18.974, 5.459], [0, -8.836], [0, 0], [0.064, -0.682]], "o": [[-1.264, 13.466], [-10.342, -1.194], [-4.968, 0.837], [-0.494, -15.067], [7.866, -27.089], [9.357, -2.692], [0, 0], [0, 0.698], [0, 0]], "v": [[-18.98, 71.711], [-48.898, 93.647], [-88.376, 91.721], [-98.112, 84.649], [-92.145, 16.616], [-37.808, -28.36], [-18.883, -15.978], [-18.883, 69.641], [-18.98, 71.711]], "c": false}]}, {"i": {"x": 0.3, "y": 1}, "o": {"x": 0.7, "y": 0}, "t": 104.334, "s": [{"i": [[0, 0], [13.403, 1.986], [17.677, -3.823], [0.131, 5.15], [-5.72, 25.29], [-16.746, 6.186], [0, -10.013], [0, 0], [0.056, -0.773]], "o": [[-1.115, 15.259], [-9.127, -1.353], [-4.385, 0.948], [-0.436, -17.074], [6.943, -30.697], [8.258, -3.05], [0, 0], [0, 0.791], [0, 0]], "v": [[-18.705, 83.076], [-45.11, 107.932], [-79.954, 105.75], [-88.547, 97.737], [-83.28, 20.643], [-35.323, -30.323], [-18.619, -16.292], [-18.619, 80.73], [-18.705, 83.076]], "c": false}]}, {"i": {"x": 0.3, "y": 1}, "o": {"x": 0.7, "y": 0}, "t": 127.211, "s": [{"i": [[0, 0], [15.186, 1.753], [20.028, -3.374], [0.149, 4.544], [-6.48, 22.317], [-18.974, 5.459], [0, -8.836], [0, 0], [0.064, -0.682]], "o": [[-1.264, 13.466], [-10.342, -1.194], [-4.968, 0.837], [-0.494, -15.067], [7.866, -27.089], [9.357, -2.692], [0, 0], [0, 0.698], [0, 0]], "v": [[-18.98, 71.711], [-48.898, 93.647], [-88.376, 91.721], [-98.112, 84.649], [-92.145, 16.616], [-37.808, -28.36], [-18.883, -15.978], [-18.883, 69.641], [-18.98, 71.711]], "c": false}]}, {"i": {"x": 0.13, "y": 1}, "o": {"x": 0.7, "y": 0}, "t": 149, "s": [{"i": [[0, 0], [13.403, 1.986], [17.677, -3.823], [0.131, 5.15], [-5.72, 25.29], [-16.746, 6.186], [0, -10.013], [0, 0], [0.056, -0.773]], "o": [[-1.115, 15.259], [-9.127, -1.353], [-4.385, 0.948], [-0.436, -17.074], [6.943, -30.697], [8.258, -3.05], [0, 0], [0, 0.791], [0, 0]], "v": [[-18.705, 83.076], [-45.11, 107.932], [-79.954, 105.75], [-88.547, 97.737], [-83.28, 20.643], [-35.323, -30.323], [-18.619, -16.292], [-18.619, 80.73], [-18.705, 83.076]], "c": false}]}, {"t": 180, "s": [{"i": [[0, 0], [14.381, 1.845], [18.968, -3.552], [0.141, 4.784], [-6.137, 23.496], [-17.969, 5.747], [0, -9.303], [0, 0], [0.061, -0.718]], "o": [[-1.197, 14.177], [-9.794, -1.257], [-4.705, 0.881], [-0.467, -15.863], [7.449, -28.52], [8.861, -2.834], [0, 0], [0, 0.735], [0, 0]], "v": [[-18.803, 77.262], [-47.136, 100.355], [-84.524, 98.328], [-93.743, 90.882], [-88.092, 19.256], [-36.634, -28.096], [-18.712, -15.06], [-18.712, 75.082], [-18.803, 77.262]], "c": false}]}], "ix": 2}, "nm": "Path 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.753, 0.141, 0.714, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1257-x-ray').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 12, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-1257-x-ray').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [250, 236.081], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 908, "st": 0, "ct": 1, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "control", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "stroke", "np": 3, "mn": "Pseudo/@@YulNbUm7QMm/IxpAYXKqjg", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "<PERSON><PERSON>", "mn": "Pseudo/@@YulNbUm7QMm/IxpAYXKqjg-0001", "ix": 1, "v": {"a": 0, "k": 2, "ix": 1}}]}, {"ty": 5, "nm": "primary", "np": 3, "mn": "ADBE Color Control", "ix": 2, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.188, 0.137, 0.682], "ix": 1}}]}, {"ty": 5, "nm": "secondary", "np": 3, "mn": "ADBE Color Control", "ix": 3, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.753, 0.141, 0.714], "ix": 1}}]}, {"ty": 5, "nm": "tertiary", "np": 3, "mn": "ADBE Color Control", "ix": 4, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.973, 0.98, 0.988], "ix": 1}}]}, {"ty": 5, "nm": "quaternary", "np": 3, "mn": "ADBE Color Control", "ix": 5, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.188, 0.137, 0.682], "ix": 1}}]}], "ip": 0, "op": 411, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "hover-pinch", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 0, "op": 190, "st": 0, "bm": 0}], "markers": [{"tm": 0, "cm": "default:hover-pinch", "dr": 180}], "props": {}}