{"v": "5.8.1", "fr": 60, "ip": 0, "op": 90, "w": 430, "h": 430, "nm": "wired-lineal-40-gears-settings-double", "ddd": 0, "assets": [{"id": "comp_1", "nm": "small", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Vector fill 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": -7, "s": [180]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 33, "s": [30]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 44, "s": [-5]}, {"t": 53, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.234, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [226.223, 223.25, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.875, "y": 0}, "t": 20, "s": [334.452, 332.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [294.452, 292.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 38.264, "s": [299.452, 297.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 43, "s": [294.452, 292.5, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.455, -8.23], [0, 0], [0, 0], [-0.88, -1.145], [-0.375, -1.394], [0.188, -1.431], [0.721, -1.25], [0, 0], [2.817, -0.755], [2.526, 1.458], [0, 0], [7.858, -2.845], [0, 0], [0.552, -1.333], [1.021, -1.02], [1.334, -0.552], [1.443, 0.001], [0, 0], [1.334, 0.552], [1.021, 1.02], [0.552, 1.333], [0, 1.443], [0, 0], [6.384, 5.392], [0, 0], [2.817, 0.755], [1.458, 2.526], [0, 0], [0.189, 1.432], [-0.374, 1.395], [-0.88, 1.146], [-1.251, 0.721], [0, 0], [-1.455, 8.23], [0, 0], [0.88, 1.146], [0.374, 1.395], [-0.189, 1.432], [-0.723, 1.25], [0, 0], [-2.817, 0.755], [-2.526, -1.458], [0, 0], [-7.858, 2.845], [0, 0], [-0.552, 1.333], [-1.021, 1.02], [-1.334, 0.552], [-1.443, -0.001], [0, 0], [-1.334, -0.552], [-1.021, -1.02], [-0.552, -1.333], [0, -1.443], [0, 0], [-6.384, -5.392], [0, 0], [-2.817, -0.755], [-1.458, -2.526], [0, 0], [-0.189, -1.432], [0.374, -1.395], [0.88, -1.146], [1.251, -0.721], [0, 0]], "o": [[0, 0], [0, 0], [1.251, 0.72], [0.88, 1.145], [0.375, 1.394], [-0.188, 1.431], [0, 0], [-1.458, 2.526], [-2.817, 0.755], [0, 0], [-6.384, 5.392], [0, 0], [0, 1.443], [-0.552, 1.333], [-1.021, 1.02], [-1.334, 0.552], [0, 0], [-1.443, 0.001], [-1.334, -0.552], [-1.021, -1.02], [-0.552, -1.333], [0, 0], [-7.858, -2.845], [0, 0], [-2.526, 1.458], [-2.817, -0.755], [0, 0], [-0.723, -1.25], [-0.189, -1.432], [0.374, -1.395], [0.88, -1.146], [0, 0], [-1.455, -8.23], [0, 0], [-1.251, -0.721], [-0.88, -1.146], [-0.374, -1.395], [0.189, -1.432], [0, 0], [1.458, -2.526], [2.817, -0.755], [0, 0], [6.384, -5.392], [0, 0], [0, -1.443], [0.552, -1.333], [1.021, -1.02], [1.334, -0.552], [0, 0], [1.443, -0.001], [1.334, 0.552], [1.021, 1.02], [0.552, 1.333], [0, 0], [7.858, 2.845], [0, 0], [2.526, -1.458], [2.817, 0.755], [0, 0], [0.723, 1.25], [0.189, 1.432], [-0.374, 1.395], [-0.88, 1.146], [0, 0], [1.455, 8.23]], "v": [[69.649, 12.432], [73.948, 14.915], [73.948, 14.915], [77.175, 17.74], [79.075, 21.585], [79.358, 25.865], [77.981, 29.927], [64.922, 52.547], [58.245, 57.67], [49.901, 56.571], [45.589, 54.08], [24.056, 66.534], [24.056, 71.513], [23.22, 75.718], [20.837, 79.283], [17.271, 81.665], [13.065, 82.5], [-13.054, 82.5], [-17.26, 81.665], [-20.826, 79.283], [-23.209, 75.718], [-24.046, 71.513], [-24.046, 66.551], [-45.579, 54.097], [-49.891, 56.589], [-58.235, 57.687], [-64.912, 52.564], [-77.975, 29.945], [-79.357, 25.882], [-79.077, 21.6], [-77.179, 17.751], [-73.951, 14.924], [-69.652, 12.441], [-69.652, -12.441], [-73.951, -14.924], [-77.179, -17.751], [-79.077, -21.6], [-79.357, -25.882], [-77.975, -29.945], [-64.916, -52.564], [-58.239, -57.687], [-49.895, -56.589], [-45.583, -54.097], [-24.05, -66.551], [-24.05, -71.513], [-23.214, -75.718], [-20.831, -79.283], [-17.265, -81.665], [-13.059, -82.5], [13.056, -82.5], [17.262, -81.665], [20.828, -79.283], [23.211, -75.718], [24.047, -71.513], [24.047, -66.569], [45.58, -54.115], [49.892, -56.607], [58.236, -57.705], [64.913, -52.582], [77.972, -29.953], [79.354, -25.891], [79.075, -21.608], [77.176, -17.76], [73.948, -14.933], [69.649, -12.45]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.188, 0.137, 0.682, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-40-gears-settings-double').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-40-gears-settings-double').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[5.756, 3.846], [6.922, 0], [6.564, -6.564], [0.001, -9.282], [-3.846, -5.756], [-6.395, -2.649], [-6.789, 1.35], [-4.895, 4.895], [-1.35, 6.789], [2.649, 6.395]], "o": [[-5.756, -3.846], [-9.282, 0.001], [-6.564, 6.564], [0, 6.922], [3.846, 5.756], [6.395, 2.649], [6.789, -1.35], [4.895, -4.895], [1.35, -6.789], [-2.649, -6.395]], "v": [[19.445, -29.101], [0, -35], [-24.748, -24.748], [-35, 0], [-29.101, 19.445], [-13.394, 32.336], [6.828, 34.327], [24.749, 24.749], [34.327, 6.828], [32.336, -13.394]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.188, 0.137, 0.682, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-40-gears-settings-double').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-40-gears-settings-double').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 893, "st": -7, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "mask", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": -7, "s": [180]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 33, "s": [30]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 44, "s": [-5]}, {"t": 53, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.234, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [226.223, 223.25, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.875, "y": 0}, "t": 20, "s": [334.452, 332.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [294.452, 292.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 38.264, "s": [299.452, 297.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 43, "s": [294.452, 292.5, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.455, -8.23], [0, 0], [0, 0], [-0.88, -1.145], [-0.375, -1.394], [0.188, -1.431], [0.721, -1.25], [0, 0], [2.817, -0.755], [2.526, 1.458], [0, 0], [7.858, -2.845], [0, 0], [0.552, -1.333], [1.021, -1.02], [1.334, -0.552], [1.443, 0.001], [0, 0], [1.334, 0.552], [1.021, 1.02], [0.552, 1.333], [0, 1.443], [0, 0], [6.384, 5.392], [0, 0], [2.817, 0.755], [1.458, 2.526], [0, 0], [0.189, 1.432], [-0.374, 1.395], [-0.88, 1.146], [-1.251, 0.721], [0, 0], [-1.455, 8.23], [0, 0], [0.88, 1.146], [0.374, 1.395], [-0.189, 1.432], [-0.723, 1.25], [0, 0], [-2.817, 0.755], [-2.526, -1.458], [0, 0], [-7.858, 2.845], [0, 0], [-0.552, 1.333], [-1.021, 1.02], [-1.334, 0.552], [-1.443, -0.001], [0, 0], [-1.334, -0.552], [-1.021, -1.02], [-0.552, -1.333], [0, -1.443], [0, 0], [-6.384, -5.392], [0, 0], [-2.817, -0.755], [-1.458, -2.526], [0, 0], [-0.189, -1.432], [0.374, -1.395], [0.88, -1.146], [1.251, -0.721], [0, 0]], "o": [[0, 0], [0, 0], [1.251, 0.72], [0.88, 1.145], [0.375, 1.394], [-0.188, 1.431], [0, 0], [-1.458, 2.526], [-2.817, 0.755], [0, 0], [-6.384, 5.392], [0, 0], [0, 1.443], [-0.552, 1.333], [-1.021, 1.02], [-1.334, 0.552], [0, 0], [-1.443, 0.001], [-1.334, -0.552], [-1.021, -1.02], [-0.552, -1.333], [0, 0], [-7.858, -2.845], [0, 0], [-2.526, 1.458], [-2.817, -0.755], [0, 0], [-0.723, -1.25], [-0.189, -1.432], [0.374, -1.395], [0.88, -1.146], [0, 0], [-1.455, -8.23], [0, 0], [-1.251, -0.721], [-0.88, -1.146], [-0.374, -1.395], [0.189, -1.432], [0, 0], [1.458, -2.526], [2.817, -0.755], [0, 0], [6.384, -5.392], [0, 0], [0, -1.443], [0.552, -1.333], [1.021, -1.02], [1.334, -0.552], [0, 0], [1.443, -0.001], [1.334, 0.552], [1.021, 1.02], [0.552, 1.333], [0, 0], [7.858, 2.845], [0, 0], [2.526, -1.458], [2.817, 0.755], [0, 0], [0.723, 1.25], [0.189, 1.432], [-0.374, 1.395], [-0.88, 1.146], [0, 0], [1.455, 8.23]], "v": [[69.649, 12.432], [73.948, 14.915], [73.948, 14.915], [77.175, 17.74], [79.075, 21.585], [79.358, 25.865], [77.981, 29.927], [64.922, 52.547], [58.245, 57.67], [49.901, 56.571], [45.589, 54.08], [24.056, 66.534], [24.056, 71.513], [23.22, 75.718], [20.837, 79.283], [17.271, 81.665], [13.065, 82.5], [-13.054, 82.5], [-17.26, 81.665], [-20.826, 79.283], [-23.209, 75.718], [-24.046, 71.513], [-24.046, 66.551], [-45.579, 54.097], [-49.891, 56.589], [-58.235, 57.687], [-64.912, 52.564], [-77.975, 29.945], [-79.357, 25.882], [-79.077, 21.6], [-77.179, 17.751], [-73.951, 14.924], [-69.652, 12.441], [-69.652, -12.441], [-73.951, -14.924], [-77.179, -17.751], [-79.077, -21.6], [-79.357, -25.882], [-77.975, -29.945], [-64.916, -52.564], [-58.239, -57.687], [-49.895, -56.589], [-45.583, -54.097], [-24.05, -66.551], [-24.05, -71.513], [-23.214, -75.718], [-20.831, -79.283], [-17.265, -81.665], [-13.059, -82.5], [13.056, -82.5], [17.262, -81.665], [20.828, -79.283], [23.211, -75.718], [24.047, -71.513], [24.047, -66.569], [45.58, -54.115], [49.892, -56.607], [58.236, -57.705], [64.913, -52.582], [77.972, -29.953], [79.354, -25.891], [79.075, -21.608], [77.176, -17.76], [73.948, -14.933], [69.649, -12.45]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[5.756, 3.846], [6.922, 0], [6.564, -6.564], [0.001, -9.282], [-3.846, -5.756], [-6.395, -2.649], [-6.789, 1.35], [-4.895, 4.895], [-1.35, 6.789], [2.649, 6.395]], "o": [[-5.756, -3.846], [-9.282, 0.001], [-6.564, 6.564], [0, 6.922], [3.846, 5.756], [6.395, 2.649], [6.789, -1.35], [4.895, -4.895], [1.35, -6.789], [-2.649, -6.395]], "v": [[19.445, -29.101], [0, -35], [-24.748, -24.748], [-35, 0], [-29.101, 19.445], [-13.394, 32.336], [6.828, 34.327], [24.749, 24.749], [34.327, 6.828], [32.336, -13.394]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.227450996637, 0.200000017881, 0.278431385756, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 893, "st": -7, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "shadow 2", "tt": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.33, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [90.273, 110, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.689, "y": 0}, "t": 20, "s": [198.273, 215.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [156.773, 175, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 38, "s": [161.773, 179, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 43, "s": [155.773, 171, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [-51.5, -14, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [147.551, 260], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.753, 0.141, 0.714, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-40-gears-settings-double').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "fl", "c": {"a": 0, "k": [0.753, 0.141, 0.714, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-40-gears-settings-double').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [12, 105], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 893, "st": -7, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Vector fill", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": -7, "s": [180]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 33, "s": [30]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 44, "s": [-5]}, {"t": 53, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.234, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [226.223, 223.25, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.875, "y": 0}, "t": 20, "s": [334.452, 332.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 33, "s": [294.452, 292.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 38.264, "s": [299.452, 297.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 43, "s": [294.452, 292.5, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.455, -8.23], [0, 0], [0, 0], [-0.88, -1.145], [-0.375, -1.394], [0.188, -1.431], [0.721, -1.25], [0, 0], [2.817, -0.755], [2.526, 1.458], [0, 0], [7.858, -2.845], [0, 0], [0.552, -1.333], [1.021, -1.02], [1.334, -0.552], [1.443, 0.001], [0, 0], [1.334, 0.552], [1.021, 1.02], [0.552, 1.333], [0, 1.443], [0, 0], [6.384, 5.392], [0, 0], [2.817, 0.755], [1.458, 2.526], [0, 0], [0.189, 1.432], [-0.374, 1.395], [-0.88, 1.146], [-1.251, 0.721], [0, 0], [-1.455, 8.23], [0, 0], [0.88, 1.146], [0.374, 1.395], [-0.189, 1.432], [-0.723, 1.25], [0, 0], [-2.817, 0.755], [-2.526, -1.458], [0, 0], [-7.858, 2.845], [0, 0], [-0.552, 1.333], [-1.021, 1.02], [-1.334, 0.552], [-1.443, -0.001], [0, 0], [-1.334, -0.552], [-1.021, -1.02], [-0.552, -1.333], [0, -1.443], [0, 0], [-6.384, -5.392], [0, 0], [-2.817, -0.755], [-1.458, -2.526], [0, 0], [-0.189, -1.432], [0.374, -1.395], [0.88, -1.146], [1.251, -0.721], [0, 0]], "o": [[0, 0], [0, 0], [1.251, 0.72], [0.88, 1.145], [0.375, 1.394], [-0.188, 1.431], [0, 0], [-1.458, 2.526], [-2.817, 0.755], [0, 0], [-6.384, 5.392], [0, 0], [0, 1.443], [-0.552, 1.333], [-1.021, 1.02], [-1.334, 0.552], [0, 0], [-1.443, 0.001], [-1.334, -0.552], [-1.021, -1.02], [-0.552, -1.333], [0, 0], [-7.858, -2.845], [0, 0], [-2.526, 1.458], [-2.817, -0.755], [0, 0], [-0.723, -1.25], [-0.189, -1.432], [0.374, -1.395], [0.88, -1.146], [0, 0], [-1.455, -8.23], [0, 0], [-1.251, -0.721], [-0.88, -1.146], [-0.374, -1.395], [0.189, -1.432], [0, 0], [1.458, -2.526], [2.817, -0.755], [0, 0], [6.384, -5.392], [0, 0], [0, -1.443], [0.552, -1.333], [1.021, -1.02], [1.334, -0.552], [0, 0], [1.443, -0.001], [1.334, 0.552], [1.021, 1.02], [0.552, 1.333], [0, 0], [7.858, 2.845], [0, 0], [2.526, -1.458], [2.817, 0.755], [0, 0], [0.723, 1.25], [0.189, 1.432], [-0.374, 1.395], [-0.88, 1.146], [0, 0], [1.455, 8.23]], "v": [[69.649, 12.432], [73.948, 14.915], [73.948, 14.915], [77.175, 17.74], [79.075, 21.585], [79.358, 25.865], [77.981, 29.927], [64.922, 52.547], [58.245, 57.67], [49.901, 56.571], [45.589, 54.08], [24.056, 66.534], [24.056, 71.513], [23.22, 75.718], [20.837, 79.283], [17.271, 81.665], [13.065, 82.5], [-13.054, 82.5], [-17.26, 81.665], [-20.826, 79.283], [-23.209, 75.718], [-24.046, 71.513], [-24.046, 66.551], [-45.579, 54.097], [-49.891, 56.589], [-58.235, 57.687], [-64.912, 52.564], [-77.975, 29.945], [-79.357, 25.882], [-79.077, 21.6], [-77.179, 17.751], [-73.951, 14.924], [-69.652, 12.441], [-69.652, -12.441], [-73.951, -14.924], [-77.179, -17.751], [-79.077, -21.6], [-79.357, -25.882], [-77.975, -29.945], [-64.916, -52.564], [-58.239, -57.687], [-49.895, -56.589], [-45.583, -54.097], [-24.05, -66.551], [-24.05, -71.513], [-23.214, -75.718], [-20.831, -79.283], [-17.265, -81.665], [-13.059, -82.5], [13.056, -82.5], [17.262, -81.665], [20.828, -79.283], [23.211, -75.718], [24.047, -71.513], [24.047, -66.569], [45.58, -54.115], [49.892, -56.607], [58.236, -57.705], [64.913, -52.582], [77.972, -29.953], [79.354, -25.891], [79.075, -21.608], [77.176, -17.76], [73.948, -14.933], [69.649, -12.45]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[5.756, 3.846], [6.922, 0], [6.564, -6.564], [0.001, -9.282], [-3.846, -5.756], [-6.395, -2.649], [-6.789, 1.35], [-4.895, 4.895], [-1.35, 6.789], [2.649, 6.395]], "o": [[-5.756, -3.846], [-9.282, 0.001], [-6.564, 6.564], [0, 6.922], [3.846, 5.756], [6.395, 2.649], [6.789, -1.35], [4.895, -4.895], [1.35, -6.789], [-2.649, -6.395]], "v": [[19.445, -29.101], [0, -35], [-24.748, -24.748], [-35, 0], [-29.101, 19.445], [-13.394, 32.336], [6.828, 34.327], [24.749, 24.749], [34.327, 6.828], [32.336, -13.394]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.753, 0.141, 0.714, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-40-gears-settings-double').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}], "ip": 0, "op": 893, "st": -7, "bm": 0}]}, {"id": "comp_2", "nm": "hover-mechanic", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [120]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 26, "s": [54]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 40, "s": [60]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 50, "s": [60]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 76, "s": [-6]}, {"t": 90, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [294.452, 292.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.455, -8.23], [0, 0], [0, 0], [-0.88, -1.145], [-0.375, -1.394], [0.188, -1.431], [0.721, -1.25], [0, 0], [2.817, -0.755], [2.526, 1.458], [0, 0], [7.858, -2.845], [0, 0], [0.552, -1.333], [1.021, -1.02], [1.334, -0.552], [1.443, 0.001], [0, 0], [1.334, 0.552], [1.021, 1.02], [0.552, 1.333], [0, 1.443], [0, 0], [6.384, 5.392], [0, 0], [2.817, 0.755], [1.458, 2.526], [0, 0], [0.189, 1.432], [-0.374, 1.395], [-0.88, 1.146], [-1.251, 0.721], [0, 0], [-1.455, 8.23], [0, 0], [0.88, 1.146], [0.374, 1.395], [-0.189, 1.432], [-0.723, 1.25], [0, 0], [-2.817, 0.755], [-2.526, -1.458], [0, 0], [-7.858, 2.845], [0, 0], [-0.552, 1.333], [-1.021, 1.02], [-1.334, 0.552], [-1.443, -0.001], [0, 0], [-1.334, -0.552], [-1.021, -1.02], [-0.552, -1.333], [0, -1.443], [0, 0], [-6.384, -5.392], [0, 0], [-2.817, -0.755], [-1.458, -2.526], [0, 0], [-0.189, -1.432], [0.374, -1.395], [0.88, -1.146], [1.251, -0.721], [0, 0]], "o": [[0, 0], [0, 0], [1.251, 0.72], [0.88, 1.145], [0.375, 1.394], [-0.188, 1.431], [0, 0], [-1.458, 2.526], [-2.817, 0.755], [0, 0], [-6.384, 5.392], [0, 0], [0, 1.443], [-0.552, 1.333], [-1.021, 1.02], [-1.334, 0.552], [0, 0], [-1.443, 0.001], [-1.334, -0.552], [-1.021, -1.02], [-0.552, -1.333], [0, 0], [-7.858, -2.845], [0, 0], [-2.526, 1.458], [-2.817, -0.755], [0, 0], [-0.723, -1.25], [-0.189, -1.432], [0.374, -1.395], [0.88, -1.146], [0, 0], [-1.455, -8.23], [0, 0], [-1.251, -0.721], [-0.88, -1.146], [-0.374, -1.395], [0.189, -1.432], [0, 0], [1.458, -2.526], [2.817, -0.755], [0, 0], [6.384, -5.392], [0, 0], [0, -1.443], [0.552, -1.333], [1.021, -1.02], [1.334, -0.552], [0, 0], [1.443, -0.001], [1.334, 0.552], [1.021, 1.02], [0.552, 1.333], [0, 0], [7.858, 2.845], [0, 0], [2.526, -1.458], [2.817, 0.755], [0, 0], [0.723, 1.25], [0.189, 1.432], [-0.374, 1.395], [-0.88, 1.146], [0, 0], [1.455, 8.23]], "v": [[69.649, 12.432], [73.948, 14.915], [73.948, 14.915], [77.175, 17.74], [79.075, 21.585], [79.358, 25.865], [77.981, 29.927], [64.922, 52.547], [58.245, 57.67], [49.901, 56.571], [45.589, 54.08], [24.056, 66.534], [24.056, 71.513], [23.22, 75.718], [20.837, 79.283], [17.271, 81.665], [13.065, 82.5], [-13.054, 82.5], [-17.26, 81.665], [-20.826, 79.283], [-23.209, 75.718], [-24.046, 71.513], [-24.046, 66.551], [-45.579, 54.097], [-49.891, 56.589], [-58.235, 57.687], [-64.912, 52.564], [-77.975, 29.945], [-79.357, 25.882], [-79.077, 21.6], [-77.179, 17.751], [-73.951, 14.924], [-69.652, 12.441], [-69.652, -12.441], [-73.951, -14.924], [-77.179, -17.751], [-79.077, -21.6], [-79.357, -25.882], [-77.975, -29.945], [-64.916, -52.564], [-58.239, -57.687], [-49.895, -56.589], [-45.583, -54.097], [-24.05, -66.551], [-24.05, -71.513], [-23.214, -75.718], [-20.831, -79.283], [-17.265, -81.665], [-13.059, -82.5], [13.056, -82.5], [17.262, -81.665], [20.828, -79.283], [23.211, -75.718], [24.047, -71.513], [24.047, -66.569], [45.58, -54.115], [49.892, -56.607], [58.236, -57.705], [64.913, -52.582], [77.972, -29.953], [79.354, -25.891], [79.075, -21.608], [77.176, -17.76], [73.948, -14.933], [69.649, -12.45]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.188, 0.137, 0.682, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-40-gears-settings-double').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-40-gears-settings-double').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[5.756, 3.846], [6.922, 0], [6.564, -6.564], [0.001, -9.282], [-3.846, -5.756], [-6.395, -2.649], [-6.789, 1.35], [-4.895, 4.895], [-1.35, 6.789], [2.649, 6.395]], "o": [[-5.756, -3.846], [-9.282, 0.001], [-6.564, 6.564], [0, 6.922], [3.846, 5.756], [6.395, 2.649], [6.789, -1.35], [4.895, -4.895], [1.35, -6.789], [-2.649, -6.395]], "v": [[19.445, -29.101], [0, -35], [-24.748, -24.748], [-35, 0], [-29.101, 19.445], [-13.394, 32.336], [6.828, 34.327], [24.749, 24.749], [34.327, 6.828], [32.336, -13.394]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.188, 0.137, 0.682, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-40-gears-settings-double').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-40-gears-settings-double').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 900, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "mask", "parent": 1, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -120, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.251, 0.72], [0, 0], [1.455, 8.23], [0, 0], [-0.88, 1.146], [-0.374, 1.395], [0.189, 1.432], [0.723, 1.25], [0, 0], [2.817, 0.755], [2.526, -1.458], [0, 0], [7.858, 2.845], [0, 0], [0.552, 1.333], [1.021, 1.02], [1.334, 0.552], [1.443, -0.001], [0, 0], [1.334, -0.552], [1.021, -1.02], [0.552, -1.333], [0, -1.443], [0, 0], [6.384, -5.392], [0, 0], [2.817, -0.755], [1.458, -2.526], [0, 0], [0.189, -1.432], [-0.374, -1.395], [-0.88, -1.146], [-1.251, -0.721], [0, 0], [-1.455, -8.23], [0, 0], [0.88, -1.146], [0.374, -1.395], [-0.189, -1.432], [-0.723, -1.25], [0, 0], [-2.817, -0.755], [-2.526, 1.458], [0, 0], [-7.858, -2.845], [0, 0], [-0.552, -1.333], [-1.021, -1.02], [-1.334, -0.552], [-1.443, 0.001], [0, 0], [-1.334, 0.552], [-1.021, 1.02], [-0.552, 1.333], [0, 1.443], [0, 0], [-6.384, 5.392], [0, 0], [-2.817, 0.755], [-1.458, 2.526], [0, 0], [-0.188, 1.431], [0.375, 1.394], [0.88, 1.145]], "o": [[0, 0], [1.455, -8.23], [0, 0], [1.251, -0.721], [0.88, -1.146], [0.374, -1.395], [-0.189, -1.432], [0, 0], [-1.458, -2.526], [-2.817, -0.755], [0, 0], [-6.384, -5.392], [0, 0], [0, -1.443], [-0.552, -1.333], [-1.021, -1.02], [-1.334, -0.552], [0, 0], [-1.443, -0.001], [-1.334, 0.552], [-1.021, 1.02], [-0.552, 1.333], [0, 0], [-7.858, 2.845], [0, 0], [-2.526, -1.458], [-2.817, 0.755], [0, 0], [-0.723, 1.25], [-0.189, 1.432], [0.374, 1.395], [0.88, 1.146], [0, 0], [-1.455, 8.23], [0, 0], [-1.251, 0.721], [-0.88, 1.146], [-0.374, 1.395], [0.189, 1.432], [0, 0], [1.458, 2.526], [2.817, 0.755], [0, 0], [6.384, 5.392], [0, 0], [0, 1.443], [0.552, 1.333], [1.021, 1.02], [1.334, 0.552], [0, 0], [1.443, 0.001], [1.334, -0.552], [1.021, -1.02], [0.552, -1.333], [0, 0], [7.858, -2.845], [0, 0], [2.526, 1.458], [2.817, -0.755], [0, 0], [0.721, -1.25], [0.188, -1.431], [-0.375, -1.394], [-0.88, -1.145]], "v": [[73.948, 14.915], [69.649, 12.432], [69.649, -12.45], [73.948, -14.933], [77.176, -17.76], [79.075, -21.608], [79.354, -25.891], [77.972, -29.953], [64.913, -52.582], [58.236, -57.705], [49.892, -56.607], [45.58, -54.115], [24.047, -66.569], [24.047, -71.513], [23.211, -75.718], [20.828, -79.283], [17.262, -81.665], [13.056, -82.5], [-13.059, -82.5], [-17.265, -81.665], [-20.831, -79.283], [-23.214, -75.718], [-24.05, -71.513], [-24.05, -66.551], [-45.583, -54.097], [-49.895, -56.589], [-58.239, -57.687], [-64.916, -52.564], [-77.975, -29.945], [-79.357, -25.882], [-79.077, -21.6], [-77.179, -17.751], [-73.951, -14.924], [-69.652, -12.441], [-69.652, 12.441], [-73.951, 14.924], [-77.179, 17.751], [-79.077, 21.6], [-79.357, 25.882], [-77.975, 29.945], [-64.912, 52.564], [-58.235, 57.687], [-49.891, 56.589], [-45.579, 54.097], [-24.046, 66.551], [-24.046, 71.513], [-23.209, 75.718], [-20.826, 79.283], [-17.26, 81.665], [-13.054, 82.5], [13.065, 82.5], [17.271, 81.665], [20.837, 79.283], [23.22, 75.718], [24.056, 71.513], [24.056, 66.534], [45.589, 54.08], [49.901, 56.571], [58.245, 57.67], [64.922, 52.547], [77.981, 29.927], [79.358, 25.865], [79.075, 21.585], [77.175, 17.74]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[-9.282, 0.001], [-5.756, -3.846], [-2.649, -6.395], [1.35, -6.789], [4.895, -4.895], [6.789, -1.35], [6.395, 2.649], [3.846, 5.756], [0, 6.922], [-6.564, 6.564]], "o": [[6.922, 0], [5.756, 3.846], [2.649, 6.395], [-1.35, 6.789], [-4.895, 4.895], [-6.789, 1.35], [-6.395, -2.649], [-3.846, -5.756], [0.001, -9.282], [6.564, -6.564]], "v": [[-0.452, -35.5], [18.993, -29.601], [31.884, -13.894], [33.876, 6.328], [24.297, 24.249], [6.376, 33.827], [-13.846, 31.836], [-29.553, 18.945], [-35.452, -0.5], [-25.199, -25.248]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.227450981736, 0.20000000298, 0.278431385756, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Exclude", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "shadow 2", "tt": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [354, 309.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [133.931, 236], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.753, 0.141, 0.714, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-40-gears-settings-double').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "fl", "c": {"a": 0, "k": [0.753, 0.141, 0.714, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-40-gears-settings-double').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [-126, -16], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 900, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Exclude 2", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -120, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.251, 0.72], [0, 0], [1.455, 8.23], [0, 0], [-0.88, 1.146], [-0.374, 1.395], [0.189, 1.432], [0.723, 1.25], [0, 0], [2.817, 0.755], [2.526, -1.458], [0, 0], [7.858, 2.845], [0, 0], [0.552, 1.333], [1.021, 1.02], [1.334, 0.552], [1.443, -0.001], [0, 0], [1.334, -0.552], [1.021, -1.02], [0.552, -1.333], [0, -1.443], [0, 0], [6.384, -5.392], [0, 0], [2.817, -0.755], [1.458, -2.526], [0, 0], [0.189, -1.432], [-0.374, -1.395], [-0.88, -1.146], [-1.251, -0.721], [0, 0], [-1.455, -8.23], [0, 0], [0.88, -1.146], [0.374, -1.395], [-0.189, -1.432], [-0.723, -1.25], [0, 0], [-2.817, -0.755], [-2.526, 1.458], [0, 0], [-7.858, -2.845], [0, 0], [-0.552, -1.333], [-1.021, -1.02], [-1.334, -0.552], [-1.443, 0.001], [0, 0], [-1.334, 0.552], [-1.021, 1.02], [-0.552, 1.333], [0, 1.443], [0, 0], [-6.384, 5.392], [0, 0], [-2.817, 0.755], [-1.458, 2.526], [0, 0], [-0.188, 1.431], [0.375, 1.394], [0.88, 1.145]], "o": [[0, 0], [1.455, -8.23], [0, 0], [1.251, -0.721], [0.88, -1.146], [0.374, -1.395], [-0.189, -1.432], [0, 0], [-1.458, -2.526], [-2.817, -0.755], [0, 0], [-6.384, -5.392], [0, 0], [0, -1.443], [-0.552, -1.333], [-1.021, -1.02], [-1.334, -0.552], [0, 0], [-1.443, -0.001], [-1.334, 0.552], [-1.021, 1.02], [-0.552, 1.333], [0, 0], [-7.858, 2.845], [0, 0], [-2.526, -1.458], [-2.817, 0.755], [0, 0], [-0.723, 1.25], [-0.189, 1.432], [0.374, 1.395], [0.88, 1.146], [0, 0], [-1.455, 8.23], [0, 0], [-1.251, 0.721], [-0.88, 1.146], [-0.374, 1.395], [0.189, 1.432], [0, 0], [1.458, 2.526], [2.817, 0.755], [0, 0], [6.384, 5.392], [0, 0], [0, 1.443], [0.552, 1.333], [1.021, 1.02], [1.334, 0.552], [0, 0], [1.443, 0.001], [1.334, -0.552], [1.021, -1.02], [0.552, -1.333], [0, 0], [7.858, -2.845], [0, 0], [2.526, 1.458], [2.817, -0.755], [0, 0], [0.721, -1.25], [0.188, -1.431], [-0.375, -1.394], [-0.88, -1.145]], "v": [[73.948, 14.915], [69.649, 12.432], [69.649, -12.45], [73.948, -14.933], [77.176, -17.76], [79.075, -21.608], [79.354, -25.891], [77.972, -29.953], [64.913, -52.582], [58.236, -57.705], [49.892, -56.607], [45.58, -54.115], [24.047, -66.569], [24.047, -71.513], [23.211, -75.718], [20.828, -79.283], [17.262, -81.665], [13.056, -82.5], [-13.059, -82.5], [-17.265, -81.665], [-20.831, -79.283], [-23.214, -75.718], [-24.05, -71.513], [-24.05, -66.551], [-45.583, -54.097], [-49.895, -56.589], [-58.239, -57.687], [-64.916, -52.564], [-77.975, -29.945], [-79.357, -25.882], [-79.077, -21.6], [-77.179, -17.751], [-73.951, -14.924], [-69.652, -12.441], [-69.652, 12.441], [-73.951, 14.924], [-77.179, 17.751], [-79.077, 21.6], [-79.357, 25.882], [-77.975, 29.945], [-64.912, 52.564], [-58.235, 57.687], [-49.891, 56.589], [-45.579, 54.097], [-24.046, 66.551], [-24.046, 71.513], [-23.209, 75.718], [-20.826, 79.283], [-17.26, 81.665], [-13.054, 82.5], [13.065, 82.5], [17.271, 81.665], [20.837, 79.283], [23.22, 75.718], [24.056, 71.513], [24.056, 66.534], [45.589, 54.08], [49.901, 56.571], [58.245, 57.67], [64.922, 52.547], [77.981, 29.927], [79.358, 25.865], [79.075, 21.585], [77.175, 17.74]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[-9.282, 0.001], [-5.756, -3.846], [-2.649, -6.395], [1.35, -6.789], [4.895, -4.895], [6.789, -1.35], [6.395, 2.649], [3.846, 5.756], [0, 6.922], [-6.564, 6.564]], "o": [[6.922, 0], [5.756, 3.846], [2.649, 6.395], [-1.35, 6.789], [-4.895, 4.895], [-6.789, 1.35], [-6.395, -2.649], [-3.846, -5.756], [0.001, -9.282], [6.564, -6.564]], "v": [[-0.452, -35.5], [18.993, -29.601], [31.884, -13.894], [33.876, 6.328], [24.297, 24.249], [6.376, 33.827], [-13.846, 31.836], [-29.553, 18.945], [-35.452, -0.5], [-25.199, -25.248]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.753, 0.141, 0.714, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-40-gears-settings-double').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Exclude", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [-120]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 26, "s": [-54]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 40, "s": [-60]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 50, "s": [-60]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 76, "s": [6]}, {"t": 90, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [158.243, 154, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[7.036, -8.377], [0, 0], [-0.248, -1.873], [0.488, -1.826], [1.15, -1.5], [1.636, -0.945], [0, 0], [3.687, 0.988], [1.909, 3.306], [0, 0], [10.769, 1.918], [0, 0], [1.499, -1.15], [1.825, -0.489], [1.873, 0.247], [1.636, 0.945], [0, 0], [1.15, 1.498], [0.489, 1.825], [-0.246, 1.873], [-0.945, 1.636], [0, 0], [3.708, 10.291], [0, 0], [2.699, 2.699], [0, 3.817], [0, 0], [-0.723, 1.747], [-1.337, 1.337], [-1.747, 0.723], [-1.891, -0.001], [0, 0], [-7.036, 8.377], [0, 0], [0.247, 1.874], [-0.489, 1.826], [-1.152, 1.499], [-1.638, 0.944], [0, 0], [-3.687, -0.988], [-1.909, -3.306], [0, 0], [-10.769, -1.918], [0, 0], [-1.499, 1.15], [-1.825, 0.489], [-1.873, -0.247], [-1.636, -0.945], [0, 0], [-1.15, -1.498], [-0.489, -1.825], [0.246, -1.873], [0.945, -1.636], [0, 0], [-3.708, -10.291], [0, 0], [-2.699, -2.699], [0, -3.817], [0, 0], [0.723, -1.747], [1.337, -1.337], [1.747, -0.723], [1.891, 0.001], [0, 0]], "o": [[0, 0], [0.947, 1.635], [0.248, 1.873], [-0.488, 1.826], [-1.15, 1.5], [0, 0], [-3.306, 1.909], [-3.687, -0.988], [0, 0], [-10.766, 1.934], [0, 0], [-0.945, 1.636], [-1.499, 1.15], [-1.825, 0.489], [-1.873, -0.247], [0, 0], [-1.636, -0.944], [-1.15, -1.498], [-0.489, -1.825], [0.246, -1.873], [0, 0], [-7.046, -8.368], [0, 0], [-3.817, 0], [-2.699, -2.699], [0, 0], [-0.002, -1.891], [0.723, -1.747], [1.337, -1.337], [1.747, -0.723], [0, 0], [3.737, -10.282], [0, 0], [-0.947, -1.637], [-0.247, -1.874], [0.489, -1.826], [1.152, -1.499], [0, 0], [3.306, -1.909], [3.687, 0.988], [0, 0], [10.766, -1.934], [0, 0], [0.945, -1.636], [1.499, -1.15], [1.825, -0.489], [1.873, 0.247], [0, 0], [1.636, 0.944], [1.15, 1.498], [0.489, 1.825], [-0.246, 1.873], [0, 0], [7.046, 8.368], [0, 0], [3.817, 0], [2.699, 2.699], [0, 0], [0.002, 1.891], [-0.723, 1.747], [-1.337, 1.337], [-1.747, 0.723], [0, 0], [-3.737, 10.282]], "v": [[70.812, 59.676], [74.06, 65.304], [75.87, 70.618], [75.507, 76.221], [73.027, 81.257], [68.807, 84.961], [39.2, 102.055], [28.278, 103.493], [19.538, 96.787], [16.281, 91.14], [-16.279, 91.165], [-19.538, 96.809], [-23.239, 101.029], [-28.273, 103.511], [-33.874, 103.876], [-39.188, 102.07], [-68.796, 84.976], [-73.017, 81.277], [-75.501, 76.244], [-75.869, 70.643], [-74.065, 65.328], [-70.818, 59.704], [-87.077, 31.493], [-93.595, 31.496], [-103.773, 27.28], [-107.988, 17.103], [-107.993, -17.088], [-106.901, -22.598], [-103.781, -27.269], [-99.11, -30.389], [-93.6, -31.482], [-87.102, -31.483], [-70.818, -59.688], [-74.066, -65.316], [-75.875, -70.634], [-75.508, -76.239], [-73.023, -81.276], [-68.797, -84.978], [-39.19, -102.072], [-28.268, -103.509], [-19.528, -96.803], [-16.271, -91.157], [16.289, -91.182], [19.536, -96.806], [23.237, -101.026], [28.271, -103.507], [33.872, -103.873], [39.187, -102.067], [68.789, -84.976], [73.011, -81.277], [75.495, -76.243], [75.862, -70.642], [74.059, -65.327], [70.823, -59.723], [87.081, -31.513], [93.6, -31.515], [103.778, -27.3], [107.993, -17.122], [107.987, 17.076], [106.895, 22.586], [103.775, 27.257], [99.104, 30.377], [93.594, 31.469], [87.097, 31.47]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.188, 0.137, 0.682, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-40-gears-settings-double').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-40-gears-settings-double').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [100, 100], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.188, 0.137, 0.682, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-40-gears-settings-double').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-40-gears-settings-double').layer('control').effect('stroke')('Menu'));"}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 900, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "mask", "parent": 5, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 120, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-3.737, 10.282], [0, 0], [-1.747, 0.723], [-1.337, 1.337], [-0.723, 1.747], [0.002, 1.891], [0, 0], [2.699, 2.699], [3.817, 0], [0, 0], [7.046, 8.368], [0, 0], [-0.246, 1.873], [0.489, 1.825], [1.15, 1.498], [1.636, 0.944], [0, 0], [1.873, 0.247], [1.825, -0.489], [1.499, -1.15], [0.945, -1.636], [0, 0], [10.766, -1.934], [0, 0], [3.687, 0.988], [3.306, -1.909], [0, 0], [1.152, -1.499], [0.489, -1.826], [-0.247, -1.874], [-0.947, -1.637], [0, 0], [3.737, -10.282], [0, 0], [1.747, -0.723], [1.337, -1.337], [0.723, -1.747], [-0.002, -1.891], [0, 0], [-2.699, -2.699], [-3.817, 0], [0, 0], [-7.046, -8.368], [0, 0], [0.246, -1.873], [-0.489, -1.825], [-1.15, -1.498], [-1.636, -0.944], [0, 0], [-1.873, -0.247], [-1.825, 0.489], [-1.499, 1.15], [-0.945, 1.636], [0, 0], [-10.766, 1.934], [0, 0], [-3.687, -0.988], [-3.306, 1.909], [0, 0], [-1.15, 1.5], [-0.488, 1.826], [0.248, 1.873], [0.947, 1.635]], "o": [[7.036, -8.377], [0, 0], [1.891, 0.001], [1.747, -0.723], [1.337, -1.337], [0.723, -1.747], [0, 0], [0, -3.817], [-2.699, -2.699], [0, 0], [-3.708, -10.291], [0, 0], [0.945, -1.636], [0.246, -1.873], [-0.489, -1.825], [-1.15, -1.498], [0, 0], [-1.636, -0.945], [-1.873, -0.247], [-1.825, 0.489], [-1.499, 1.15], [0, 0], [-10.769, -1.918], [0, 0], [-1.909, -3.306], [-3.687, -0.988], [0, 0], [-1.638, 0.944], [-1.152, 1.499], [-0.489, 1.826], [0.247, 1.874], [0, 0], [-7.036, 8.377], [0, 0], [-1.891, -0.001], [-1.747, 0.723], [-1.337, 1.337], [-0.723, 1.747], [0, 0], [0, 3.817], [2.699, 2.699], [0, 0], [3.708, 10.291], [0, 0], [-0.945, 1.636], [-0.246, 1.873], [0.489, 1.825], [1.15, 1.498], [0, 0], [1.636, 0.945], [1.873, 0.247], [1.825, -0.489], [1.499, -1.15], [0, 0], [10.769, 1.918], [0, 0], [1.909, 3.306], [3.687, 0.988], [0, 0], [1.636, -0.945], [1.15, -1.5], [0.488, -1.826], [-0.248, -1.873], [0, 0]], "v": [[70.812, 59.676], [87.096, 31.47], [93.594, 31.469], [99.104, 30.377], [103.775, 27.257], [106.895, 22.586], [107.987, 17.076], [107.993, -17.122], [103.777, -27.3], [93.6, -31.515], [87.081, -31.513], [70.823, -59.723], [74.058, -65.327], [75.862, -70.642], [75.494, -76.243], [73.011, -81.277], [68.789, -84.976], [39.187, -102.067], [33.872, -103.873], [28.271, -103.507], [23.237, -101.026], [19.536, -96.806], [16.289, -91.182], [-16.271, -91.157], [-19.528, -96.803], [-28.268, -103.51], [-39.19, -102.072], [-68.798, -84.978], [-73.023, -81.276], [-75.508, -76.239], [-75.875, -70.634], [-74.066, -65.316], [-70.818, -59.688], [-87.102, -31.483], [-93.6, -31.482], [-99.11, -30.389], [-103.781, -27.269], [-106.901, -22.598], [-107.993, -17.088], [-107.988, 17.103], [-103.773, 27.28], [-93.595, 31.496], [-87.077, 31.493], [-70.818, 59.704], [-74.065, 65.328], [-75.869, 70.643], [-75.501, 76.244], [-73.017, 81.277], [-68.796, 84.976], [-39.189, 102.07], [-33.874, 103.876], [-28.273, 103.511], [-23.239, 101.029], [-19.538, 96.809], [-16.279, 91.165], [16.281, 91.14], [19.538, 96.787], [28.278, 103.493], [39.2, 102.055], [68.807, 84.961], [73.027, 81.257], [75.507, 76.221], [75.87, 70.618], [74.06, 65.304]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, -27.614], [27.614, 0], [0, 27.614], [-27.614, 0]], "o": [[0, 27.614], [-27.614, 0], [0, -27.614], [27.614, 0]], "v": [[50.007, 0], [0.007, 50], [-49.993, 0], [0.007, -50]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.227450981736, 0.20000000298, 0.278431385756, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Exclude", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "shadow", "tt": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [218.75, 218, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [183.872, 324], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "Rectangle Path 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.753, 0.141, 0.714, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-40-gears-settings-double').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "fl", "c": {"a": 0, "k": [0.753, 0.141, 0.714, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-40-gears-settings-double').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [-153, -43.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 900, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Exclude", "parent": 5, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 120, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-3.737, 10.282], [0, 0], [-1.747, 0.723], [-1.337, 1.337], [-0.723, 1.747], [0.002, 1.891], [0, 0], [2.699, 2.699], [3.817, 0], [0, 0], [7.046, 8.368], [0, 0], [-0.246, 1.873], [0.489, 1.825], [1.15, 1.498], [1.636, 0.944], [0, 0], [1.873, 0.247], [1.825, -0.489], [1.499, -1.15], [0.945, -1.636], [0, 0], [10.766, -1.934], [0, 0], [3.687, 0.988], [3.306, -1.909], [0, 0], [1.152, -1.499], [0.489, -1.826], [-0.247, -1.874], [-0.947, -1.637], [0, 0], [3.737, -10.282], [0, 0], [1.747, -0.723], [1.337, -1.337], [0.723, -1.747], [-0.002, -1.891], [0, 0], [-2.699, -2.699], [-3.817, 0], [0, 0], [-7.046, -8.368], [0, 0], [0.246, -1.873], [-0.489, -1.825], [-1.15, -1.498], [-1.636, -0.944], [0, 0], [-1.873, -0.247], [-1.825, 0.489], [-1.499, 1.15], [-0.945, 1.636], [0, 0], [-10.766, 1.934], [0, 0], [-3.687, -0.988], [-3.306, 1.909], [0, 0], [-1.15, 1.5], [-0.488, 1.826], [0.248, 1.873], [0.947, 1.635]], "o": [[7.036, -8.377], [0, 0], [1.891, 0.001], [1.747, -0.723], [1.337, -1.337], [0.723, -1.747], [0, 0], [0, -3.817], [-2.699, -2.699], [0, 0], [-3.708, -10.291], [0, 0], [0.945, -1.636], [0.246, -1.873], [-0.489, -1.825], [-1.15, -1.498], [0, 0], [-1.636, -0.945], [-1.873, -0.247], [-1.825, 0.489], [-1.499, 1.15], [0, 0], [-10.769, -1.918], [0, 0], [-1.909, -3.306], [-3.687, -0.988], [0, 0], [-1.638, 0.944], [-1.152, 1.499], [-0.489, 1.826], [0.247, 1.874], [0, 0], [-7.036, 8.377], [0, 0], [-1.891, -0.001], [-1.747, 0.723], [-1.337, 1.337], [-0.723, 1.747], [0, 0], [0, 3.817], [2.699, 2.699], [0, 0], [3.708, 10.291], [0, 0], [-0.945, 1.636], [-0.246, 1.873], [0.489, 1.825], [1.15, 1.498], [0, 0], [1.636, 0.945], [1.873, 0.247], [1.825, -0.489], [1.499, -1.15], [0, 0], [10.769, 1.918], [0, 0], [1.909, 3.306], [3.687, 0.988], [0, 0], [1.636, -0.945], [1.15, -1.5], [0.488, -1.826], [-0.248, -1.873], [0, 0]], "v": [[70.812, 59.676], [87.096, 31.47], [93.594, 31.469], [99.104, 30.377], [103.775, 27.257], [106.895, 22.586], [107.987, 17.076], [107.993, -17.122], [103.777, -27.3], [93.6, -31.515], [87.081, -31.513], [70.823, -59.723], [74.058, -65.327], [75.862, -70.642], [75.494, -76.243], [73.011, -81.277], [68.789, -84.976], [39.187, -102.067], [33.872, -103.873], [28.271, -103.507], [23.237, -101.026], [19.536, -96.806], [16.289, -91.182], [-16.271, -91.157], [-19.528, -96.803], [-28.268, -103.51], [-39.19, -102.072], [-68.798, -84.978], [-73.023, -81.276], [-75.508, -76.239], [-75.875, -70.634], [-74.066, -65.316], [-70.818, -59.688], [-87.102, -31.483], [-93.6, -31.482], [-99.11, -30.389], [-103.781, -27.269], [-106.901, -22.598], [-107.993, -17.088], [-107.988, 17.103], [-103.773, 27.28], [-93.595, 31.496], [-87.077, 31.493], [-70.818, 59.704], [-74.065, 65.328], [-75.869, 70.643], [-75.501, 76.244], [-73.017, 81.277], [-68.796, 84.976], [-39.189, 102.07], [-33.874, 103.876], [-28.273, 103.511], [-23.239, 101.029], [-19.538, 96.809], [-16.279, 91.165], [16.281, 91.14], [19.538, 96.787], [28.278, 103.493], [39.2, 102.055], [68.807, 84.961], [73.027, 81.257], [75.507, 76.221], [75.87, 70.618], [74.06, 65.304]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, -27.614], [27.614, 0], [0, 27.614], [-27.614, 0]], "o": [[0, 27.614], [-27.614, 0], [0, -27.614], [27.614, 0]], "v": [[50.007, 0], [0.007, 50], [-49.993, 0], [0.007, -50]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.753, 0.141, 0.714, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-40-gears-settings-double').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Exclude", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "control", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "stroke", "np": 3, "mn": "Pseudo/@@mkW9EeFsRpS3w8bdL9qdwg", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "<PERSON><PERSON>", "mn": "Pseudo/@@mkW9EeFsRpS3w8bdL9qdwg-0001", "ix": 1, "v": {"a": 0, "k": 2, "ix": 1}}]}, {"ty": 5, "nm": "primary", "np": 3, "mn": "ADBE Color Control", "ix": 2, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.188, 0.137, 0.682], "ix": 1}}]}, {"ty": 5, "nm": "secondary", "np": 3, "mn": "ADBE Color Control", "ix": 3, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.753, 0.141, 0.714], "ix": 1}}]}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "hover-mechanic", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 0, "op": 100, "st": 0, "bm": 0}], "markers": [{"tm": 0, "cm": "default:hover-mechanic", "dr": 90}]}