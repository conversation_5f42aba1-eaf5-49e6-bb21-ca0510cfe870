{"v": "5.10.2", "fr": 60, "ip": 0, "op": 57, "w": 430, "h": 430, "nm": "wired-lineal-946-equity-security", "ddd": 0, "assets": [{"id": "comp_1", "nm": "hover-locked", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Vector 2", "parent": 6, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, -10.886], [-10.886, 0], [0, 0], [0.031, -10.876], [10.529, 0.082], [0, 0]], "o": [[0, 0], [-10.886, 0], [0, 10.886], [0, 0], [10.876, 0], [-0.031, 10.896], [0, 0], [0, 0]], "v": [[23.031, -39.115], [-7.514, -39.115], [-27.225, -19.415], [-7.514, 0.296], [7.575, 0.296], [27.225, 20.006], [7.779, 39.605], [-26.643, 39.309]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0.005, 39.533], [0.005, 55]], "c": false}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0.005, -55], [0.005, -39.105]], "c": false}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-946-equity-security').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-946-equity-security').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.1], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 25, "s": [100]}, {"t": 57, "s": [0]}], "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 5, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 2", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, -10.886], [-10.886, 0], [0, 0], [0.031, -10.876], [10.529, 0.082], [0, 0]], "o": [[0, 0], [-10.886, 0], [0, 10.886], [0, 0], [10.876, 0], [-0.031, 10.896], [0, 0], [0, 0]], "v": [[23.031, -39.115], [-7.514, -39.115], [-27.225, -19.415], [-7.514, 0.296], [7.575, 0.296], [27.225, 20.006], [7.779, 39.605], [-26.643, 39.309]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0.005, 39.533], [0.005, 55]], "c": false}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0.005, -55], [0.005, -39.105]], "c": false}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-946-equity-security').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-946-equity-security').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.9], "y": [0]}, "t": 0, "s": [100]}, {"t": 32, "s": [0]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 5, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 5, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Rectangle 31", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.299], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [16]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 32, "s": [-15]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 44, "s": [4]}, {"t": 57, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.207, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [215, 285, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 9, "s": [245, 285, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 26, "s": [195, 285, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 38, "s": [225, 285, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 51, "s": [215, 285, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [-22.091, 0], [0, 0], [0, 22.091]], "o": [[0, 0], [0, 0], [0, 22.091], [0, 0], [22.091, 0], [0, 0]], "v": [[130, -95], [-130, -95], [-130, 55], [-90, 95], [90, 95], [130, 55]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 54, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-946-equity-security').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-946-equity-security').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 28", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "mask", "parent": 6, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [-22.091, 0], [0, 0], [0, 22.091]], "o": [[0, 0], [0, 0], [0, 22.091], [0, 0], [22.091, 0], [0, 0]], "v": [[130, -95], [-130, -95], [-130, 55], [-90, 95], [90, 95], [130, 55]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 54, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 2", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 28", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Rectangle 30", "parent": 6, "tt": 1, "tp": 4, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [59, 0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [-22.091, 0], [0, 0], [0, 22.091]], "o": [[0, 0], [0, 0], [0, 22.091], [0, 0], [22.091, 0], [0, 0]], "v": [[130, -95], [-130, -95], [-130, 55], [-90, 95], [90, 95], [130, 55]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 54, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.753, 0.141, 0.714, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-946-equity-security').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 28", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Rectangle 29", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.299], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [16]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 32, "s": [-15]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 44, "s": [4]}, {"t": 57, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.207, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [215, 285, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 9, "s": [245, 285, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 26, "s": [195, 285, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 38, "s": [225, 285, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 51, "s": [215, 285, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [-22.091, 0], [0, 0], [0, 22.091]], "o": [[0, 0], [0, 0], [0, 22.091], [0, 0], [22.091, 0], [0, 0]], "v": [[130, -95], [-130, -95], [-130, 55], [-90, 95], [90, 95], [130, 55]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 54, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.753, 0.141, 0.714, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-946-equity-security').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "fl", "c": {"a": 0, "k": [0.753, 0.141, 0.714, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-946-equity-security').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 28", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Shape Layer 2", "parent": 6, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0.283, -160.344, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0.283, -107.344, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 6, "s": [{"i": [[0, -47.532], [0, 0], [46.935, 0], [0, 47.532], [0, 0], [-46.935, 0]], "o": [[0, 0], [0, 47.532], [-46.935, 0], [0, 0], [0, -47.532], [46.935, 0]], "v": [[84.76, 1.389], [84.76, 62.111], [-0.224, 148.175], [-85.208, 62.111], [-85.208, 1.389], [-0.224, -84.675]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 14, "s": [{"i": [[0, -42.538], [0, 0], [46.935, 0], [0, 42.538], [0, 0], [-46.935, 0]], "o": [[0, 0], [0, 42.538], [-46.935, 0], [0, 0], [0, -42.538], [46.935, 0]], "v": [[84.76, 16.809], [84.76, 71.152], [-0.224, 148.175], [-85.208, 71.152], [-85.208, 16.809], [-0.224, -60.214]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 26, "s": [{"i": [[0, -42.438], [0, 0], [46.935, 0], [0, 42.438], [0, 0], [-46.935, 0]], "o": [[0, 0], [0, 42.438], [-46.935, 0], [0, 0], [0, -42.438], [46.935, 0]], "v": [[84.76, 17.12], [84.76, 71.335], [-0.224, 148.175], [-85.208, 71.335], [-85.208, 17.12], [-0.224, -59.72]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 37, "s": [{"i": [[0, -50.074], [0, 0], [46.935, 0], [0, 50.074], [0, 0], [-46.935, 0]], "o": [[0, 0], [0, 50.074], [-46.935, 0], [0, 0], [0, -50.074], [46.935, 0]], "v": [[84.76, -6.463], [84.76, 57.508], [-0.224, 148.175], [-85.208, 57.508], [-85.208, -6.463], [-0.224, -97.13]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 49, "s": [{"i": [[0, -46.011], [0, 0], [46.935, 0], [0, 46.011], [0, 0], [-46.935, 0]], "o": [[0, 0], [0, 46.011], [-46.935, 0], [0, 0], [0, -46.011], [46.935, 0]], "v": [[84.76, 6.085], [84.76, 64.865], [-0.224, 148.175], [-85.208, 64.865], [-85.208, 6.085], [-0.224, -77.225]], "c": true}]}, {"t": 57, "s": [{"i": [[0, -47.532], [0, 0], [46.935, 0], [0, 47.532], [0, 0], [-46.935, 0]], "o": [[0, 0], [0, 47.532], [-46.935, 0], [0, 0], [0, -47.532], [46.935, 0]], "v": [[84.76, 1.389], [84.76, 62.111], [-0.224, 148.175], [-85.208, 62.111], [-85.208, 1.389], [-0.224, -84.675]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 21, "ix": 1}, "e": {"a": 0, "k": 79.5, "ix": 2}, "o": {"a": 0, "k": 106, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-946-equity-security').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-946-equity-security').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0.283, -107.094], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "control", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "stroke", "np": 3, "mn": "Pseudo/@@aaoUXRhWSimzoifFxOJ1CA", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "<PERSON><PERSON>", "mn": "Pseudo/@@aaoUXRhWSimzoifFxOJ1CA-0001", "ix": 1, "v": {"a": 0, "k": 2, "ix": 1}}]}, {"ty": 5, "nm": "primary", "np": 3, "mn": "ADBE Color Control", "ix": 2, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.071, 0.075, 0.192], "ix": 1}}]}, {"ty": 5, "nm": "secondary", "np": 3, "mn": "ADBE Color Control", "ix": 3, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.753, 0.141, 0.714], "ix": 1}}]}], "ip": 0, "op": 198, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "hover-locked", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 0, "op": 67, "st": 0, "bm": 0}], "markers": [{"tm": 0, "cm": "default:hover-locked", "dr": 57}]}