{"v": "5.12.1", "fr": 60, "ip": 100, "op": 160, "w": 430, "h": 430, "nm": "wired-lineal-21-avatar", "ddd": 0, "assets": [{"id": "comp_0", "nm": "in-reveal", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Head ", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.285, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [215, 371, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.463, "y": 1}, "o": {"x": 0.373, "y": 0}, "t": 34, "s": [215, 92, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 64, "s": [215, 145, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 90, "s": [215, 125, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[-24.301, 0], [0, 24.301], [24.301, 0], [0, -24.301]], "o": [[24.301, 0], [0, -24.301], [-24.301, 0], [0, 24.301]], "v": [[0, 29], [44, -15], [0, -59], [-44, -15]], "c": true}]}, {"t": 22, "s": [{"i": [[-41.421, 0], [0, 41.421], [41.421, 0], [0, -41.421]], "o": [[41.421, 0], [0, -41.421], [-41.421, 0], [0, 41.421]], "v": [[0, 75], [75, 0], [0, -75], [-75, 0]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.2, 0.2, 0.2, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-21-avatar').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 1, "op": 156, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "mask", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.35, "y": 0.801}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [215, 371, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.624, "y": 0.83}, "o": {"x": 0.293, "y": 0.647}, "t": 24, "s": [240, 107.455, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.463, "y": 1}, "o": {"x": 0.33, "y": 0.134}, "t": 34, "s": [240.297, 92.184, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 65, "s": [240, 145, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 90, "s": [242.25, 126.75, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[-24.301, 0], [0, 24.301], [24.301, 0], [0, -24.301]], "o": [[24.301, 0], [0, -24.301], [-24.301, 0], [0, 24.301]], "v": [[0, 29], [44, -15], [0, -59], [-44, -15]], "c": true}]}, {"t": 22, "s": [{"i": [[-41.421, 0], [0, 41.421], [41.421, 0], [0, -41.421]], "o": [[41.421, 0], [0, -41.421], [-41.421, 0], [0, 41.421]], "v": [[0, 75], [75, 0], [0, -75], [-75, 0]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.780392169952, 0.219607844949, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 1, "op": 156, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Head shadow", "tt": 2, "tp": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.285, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [215, 371, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.463, "y": 1}, "o": {"x": 0.373, "y": 0}, "t": 34, "s": [215, 92, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 64, "s": [215, 145, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 90, "s": [215, 125, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[-24.301, 0], [0, 24.301], [24.301, 0], [0, -24.301]], "o": [[24.301, 0], [0, -24.301], [-24.301, 0], [0, 24.301]], "v": [[0, 29], [44, -15], [0, -59], [-44, -15]], "c": true}]}, {"t": 22, "s": [{"i": [[-41.421, 0], [0, 41.421], [41.421, 0], [0, -41.421]], "o": [[41.421, 0], [0, -41.421], [-41.421, 0], [0, 41.421]], "v": [[0, 75], [75, 0], [0, -75], [-75, 0]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}], "ip": 1, "op": 156, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Head fill", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.285, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [215, 371, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.463, "y": 1}, "o": {"x": 0.373, "y": 0}, "t": 34, "s": [215, 92, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 64, "s": [215, 145, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 90, "s": [215, 125, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[-24.301, 0], [0, 24.301], [24.301, 0], [0, -24.301]], "o": [[24.301, 0], [0, -24.301], [-24.301, 0], [0, 24.301]], "v": [[0, 29], [44, -15], [0, -59], [-44, -15]], "c": true}]}, {"t": 22, "s": [{"i": [[-41.421, 0], [0, 41.421], [41.421, 0], [0, -41.421]], "o": [[41.421, 0], [0, -41.421], [-41.421, 0], [0, 41.421]], "v": [[0, 75], [75, 0], [0, -75], [-75, 0]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}], "ip": 1, "op": 156, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Body fill 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 305, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.293, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, -1.105], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [2.209, 0]], "o": [[-1.105, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -2.209], [0, 0]], "v": [[-158, 66], [-160, 68], [-160, 69.87], [-160, 70], [-159.335, 70], [159.3, 70], [160, 70], [160, 70], [160, 70], [156, 66]], "c": true}]}, {"i": {"x": 0.36, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 25, "s": [{"i": [[0, 0], [0, -63.482], [0, 0], [-2.112, -2.36], [0, 0], [0, 0], [-1.945, 2.06], [0, 0], [0, 0], [63.483, 0]], "o": [[-63.482, 0], [0, 0], [0, 0], [1.889, 2.112], [0, 0], [0, 0], [2.06, -2.181], [0, 0], [0, -63.483], [0, 0]], "v": [[-45.056, -106.077], [-160, 8.867], [-160, 60.5], [-158, 69], [-151.5, 70], [151.5, 70], [158.5, 68], [160, 61], [160, 8.868], [45.055, -106.077]], "c": true}]}, {"t": 63, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-1.064, -1.115], [0, 0], [0, 0], [-0.766, 1.019], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [1.016, 1.064], [0, 0], [0, 0], [1.019, -1.357], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 65.376], [-158.75, 69], [-155.585, 70], [156.175, 70], [159.375, 69.125], [160, 64.909], [160, 45], [45, -70]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.2, 0.2, 0.2, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-21-avatar').layer('control').effect('stroke')('Menu'));"}, "lc": 1, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 22", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 1, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "mask 2", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [265, 305, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.307, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, -1.105], [0, 0], [0, 0], [0, 0], [2.209, 0]], "o": [[-1.105, 0], [0, 0], [0, 0], [0, 0], [0, -2.209], [0, 0]], "v": [[-158, 66], [-160, 68], [-160, 70], [160, 70], [160, 70], [156, 66]], "c": true}]}, {"i": {"x": 0.36, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 26, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -106.162], [-160, 8.838], [-160, 70], [160, 70], [160, 8.838], [45, -106.162]], "c": true}]}, {"t": 63, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 70], [160, 70], [160, 45], [45, -70]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.29411765933, 0.701960802078, 0.992156863213, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 22", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 1, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Body shadow 2", "tt": 2, "tp": 6, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 305, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.293, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, -1.105], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [2.209, 0]], "o": [[-1.105, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -2.209], [0, 0]], "v": [[-158, 66], [-160, 68], [-160, 69.87], [-160, 70], [-159.335, 70], [159.3, 70], [160, 70], [160, 70], [160, 70], [156, 66]], "c": true}]}, {"i": {"x": 0.36, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 25, "s": [{"i": [[0, 0], [0, -63.482], [0, 0], [-2.112, -2.36], [0, 0], [0, 0], [-1.945, 2.06], [0, 0], [0, 0], [63.483, 0]], "o": [[-63.482, 0], [0, 0], [0, 0], [1.889, 2.112], [0, 0], [0, 0], [2.06, -2.181], [0, 0], [0, -63.483], [0, 0]], "v": [[-45.056, -106.077], [-160, 8.867], [-160, 60.5], [-158, 69], [-151.5, 70], [151.5, 70], [158.5, 68], [160, 61], [160, 8.868], [45.055, -106.077]], "c": true}]}, {"t": 63, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-1.064, -1.115], [0, 0], [0, 0], [-0.766, 1.019], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [1.016, 1.064], [0, 0], [0, 0], [1.019, -1.357], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 65.376], [-158.75, 69], [-155.585, 70], [156.175, 70], [159.375, 69.125], [160, 64.909], [160, 45], [45, -70]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 22", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 1, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Body fill 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 305, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.293, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, -1.105], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [2.209, 0]], "o": [[-1.105, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -2.209], [0, 0]], "v": [[-158, 66], [-160, 68], [-160, 69.87], [-160, 70], [-159.335, 70], [159.3, 70], [160, 70], [160, 70], [160, 70], [156, 66]], "c": true}]}, {"i": {"x": 0.36, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 25, "s": [{"i": [[0, 0], [0, -63.482], [0, 0], [-2.112, -2.36], [0, 0], [0, 0], [-1.945, 2.06], [0, 0], [0, 0], [63.483, 0]], "o": [[-63.482, 0], [0, 0], [0, 0], [1.889, 2.112], [0, 0], [0, 0], [2.06, -2.181], [0, 0], [0, -63.483], [0, 0]], "v": [[-45.056, -106.077], [-160, 8.867], [-160, 60.5], [-158, 69], [-151.5, 70], [151.5, 70], [158.5, 68], [160, 61], [160, 8.868], [45.055, -106.077]], "c": true}]}, {"t": 63, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-1.064, -1.115], [0, 0], [0, 0], [-0.766, 1.019], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [1.016, 1.064], [0, 0], [0, 0], [1.019, -1.357], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 65.376], [-158.75, 69], [-155.585, 70], [156.175, 70], [159.375, 69.125], [160, 64.909], [160, 45], [45, -70]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 22", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 1, "op": 1800, "st": 0, "ct": 1, "bm": 0}]}, {"id": "comp_1", "nm": "hover-jumping", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.061, "y": 1}, "o": {"x": 0.363, "y": 0}, "t": 0, "s": [215, 125, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.53, "y": 1}, "o": {"x": 0.47, "y": 0}, "t": 21, "s": [215, 80, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.306, "y": 1}, "o": {"x": 0.406, "y": 0}, "t": 40, "s": [215, 160, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 60, "s": [215, 125, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-41.421, 0], [0, 41.421], [41.421, 0], [0, -41.421]], "o": [[41.421, 0], [0, -41.421], [-41.421, 0], [0, 41.421]], "v": [[0, 75], [75, 0], [0, -75], [-75, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.2, 0.2, 0.2, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-21-avatar').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "mask 2", "parent": 1, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [27.427, 1.742, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [7.898, -3.227, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [697, 697, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -5.945], [5.945, 0], [0, 5.945], [-5.945, 0]], "o": [[0, 5.945], [-5.945, 0], [0, -5.945], [5.945, 0]], "v": [[18.661, -3.227], [7.898, 7.536], [-2.866, -3.227], [7.898, -13.991]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": -40, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.780392229557, 0.21960785985, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 217.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Head shadow", "parent": 1, "tt": 2, "tp": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-0.073, -0.008, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [7.898, -3.227, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [697, 697, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -5.945], [5.945, 0], [0, 5.945], [-5.945, 0]], "o": [[0, 5.945], [-5.945, 0], [0, -5.945], [5.945, 0]], "v": [[18.661, -3.227], [7.898, 7.536], [-2.866, -3.227], [7.898, -13.991]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": -40, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}], "ip": 0, "op": 217.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Head fill", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-0.073, -0.008, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [7.898, -3.227, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [697, 697, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -5.945], [5.945, 0], [0, 5.945], [-5.945, 0]], "o": [[0, 5.945], [-5.945, 0], [0, -5.945], [5.945, 0]], "v": [[18.661, -3.227], [7.898, 7.536], [-2.866, -3.227], [7.898, -13.991]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": -40, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}], "ip": 0, "op": 217.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Body", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 305, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.165, "y": 1}, "o": {"x": 0.079, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-0.724, -1.069], [0, 0], [0, 0], [-0.619, 0.676], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0.491, 0.724], [0, 0], [0, 0], [0.676, -0.737], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 66.125], [-159.5, 68.75], [-157.375, 70], [157.25, 70], [159.25, 68.875], [160, 67], [160, 45], [45, -70]], "c": true}]}, {"i": {"x": 0.53, "y": 1}, "o": {"x": 0.47, "y": 0}, "t": 18, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-0.961, -0.965], [0, 0], [0, 0], [-0.479, 0.662], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0.869, 0.872], [0, 0], [0, 0], [0.671, -0.927], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -106.162], [-160, 8.838], [-160, 66.047], [-159.25, 69.125], [-156.061, 70], [156.553, 70], [159.25, 69.125], [160.125, 66.569], [160, 8.838], [45, -106.162]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.406, "y": 0}, "t": 37, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-0.895, -1.025], [0, 0], [0, 0], [-1.047, 0.864], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0.781, 0.895], [0, 0], [0, 0], [0.891, -0.735], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -59.809], [-160, 55.191], [-160, 65.918], [-159.625, 69.25], [-156.436, 70], [155.928, 70], [159.375, 69.25], [160.125, 66.536], [160, 55.191], [45, -59.809]], "c": true}]}, {"t": 60, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-0.724, -1.069], [0, 0], [0, 0], [-0.619, 0.676], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0.491, 0.724], [0, 0], [0, 0], [0.676, -0.737], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 66.125], [-159.5, 68.75], [-157.375, 70], [157.25, 70], [159.25, 68.875], [160, 67], [160, 45], [45, -70]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 22", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.2, 0.2, 0.2, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-21-avatar').layer('control').effect('stroke')('Menu'));"}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}], "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "mask", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [265, 305, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.165, "y": 1}, "o": {"x": 0.079, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 70], [160, 70], [160, 45], [45, -70]], "c": true}]}, {"i": {"x": 0.53, "y": 1}, "o": {"x": 0.47, "y": 0}, "t": 18, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -106.162], [-160, 8.838], [-160, 70], [160, 70], [160, 8.838], [45, -106.162]], "c": true}]}, {"i": {"x": 0.306, "y": 1}, "o": {"x": 0.406, "y": 0}, "t": 37, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -59.809], [-160, 55.191], [-160, 70], [160, 70], [160, 55.191], [45, -59.809]], "c": true}]}, {"t": 60, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 70], [160, 70], [160, 45], [45, -70]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 22", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.29411765933, 0.701960802078, 0.992156863213, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Body shadow", "tt": 2, "tp": 6, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 305, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.165, "y": 1}, "o": {"x": 0.079, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-0.724, -1.069], [0, 0], [0, 0], [-0.619, 0.676], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0.491, 0.724], [0, 0], [0, 0], [0.676, -0.737], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 66.125], [-159.5, 68.75], [-157.375, 70], [157.25, 70], [159.25, 68.875], [160, 67], [160, 45], [45, -70]], "c": true}]}, {"i": {"x": 0.53, "y": 1}, "o": {"x": 0.47, "y": 0}, "t": 18, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-0.961, -0.965], [0, 0], [0, 0], [-0.479, 0.662], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0.869, 0.872], [0, 0], [0, 0], [0.671, -0.927], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -106.162], [-160, 8.838], [-160, 66.047], [-159.25, 69.125], [-156.061, 70], [156.553, 70], [159.25, 69.125], [160.125, 66.569], [160, 8.838], [45, -106.162]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.406, "y": 0}, "t": 37, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-0.895, -1.025], [0, 0], [0, 0], [-1.047, 0.864], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0.781, 0.895], [0, 0], [0, 0], [0.891, -0.735], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -59.809], [-160, 55.191], [-160, 65.918], [-159.625, 69.25], [-156.436, 70], [155.928, 70], [159.375, 69.25], [160.125, 66.536], [160, 55.191], [45, -59.809]], "c": true}]}, {"t": 60, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-0.724, -1.069], [0, 0], [0, 0], [-0.619, 0.676], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0.491, 0.724], [0, 0], [0, 0], [0.676, -0.737], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 66.125], [-159.5, 68.75], [-157.375, 70], [157.25, 70], [159.25, 68.875], [160, 67], [160, 45], [45, -70]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 22", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}], "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Body fill", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 305, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.165, "y": 1}, "o": {"x": 0.079, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-0.724, -1.069], [0, 0], [0, 0], [-0.619, 0.676], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0.491, 0.724], [0, 0], [0, 0], [0.676, -0.737], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 66.125], [-159.5, 68.75], [-157.375, 70], [157.25, 70], [159.25, 68.875], [160, 67], [160, 45], [45, -70]], "c": true}]}, {"i": {"x": 0.53, "y": 1}, "o": {"x": 0.47, "y": 0}, "t": 18, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-0.961, -0.965], [0, 0], [0, 0], [-0.479, 0.662], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0.869, 0.872], [0, 0], [0, 0], [0.671, -0.927], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -106.162], [-160, 8.838], [-160, 66.047], [-159.25, 69.125], [-156.061, 70], [156.553, 70], [159.25, 69.125], [160.125, 66.569], [160, 8.838], [45, -106.162]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.406, "y": 0}, "t": 37, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-0.895, -1.025], [0, 0], [0, 0], [-1.047, 0.864], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0.781, 0.895], [0, 0], [0, 0], [0.891, -0.735], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -59.809], [-160, 55.191], [-160, 65.918], [-159.625, 69.25], [-156.436, 70], [155.928, 70], [159.375, 69.25], [160.125, 66.536], [160, 55.191], [45, -59.809]], "c": true}]}, {"t": 60, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-0.724, -1.069], [0, 0], [0, 0], [-0.619, 0.676], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0.491, 0.724], [0, 0], [0, 0], [0.676, -0.737], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 66.125], [-159.5, 68.75], [-157.375, 70], [157.25, 70], [159.25, 68.875], [160, 67], [160, 45], [45, -70]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 22", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}], "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}]}, {"id": "comp_2", "nm": "hover-looking-around", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Head  ", "parent": 5, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.094, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [-0.073, -180.008, 0], "to": [0, -1.452, 0], "ti": [0, -0.072, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 12, "s": [-0.073, -223.035, 0], "to": [0.013, 49.491, 0], "ti": [6.026, 1.059, 0]}, {"t": 22, "s": [-56.584, -165.356, 0], "h": 1}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 44.066, "s": [-56.584, -165.356, 0], "to": [44.902, 32.338, 0], "ti": [-13.08, 17.895, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 62, "s": [39.896, -166.227, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.257, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 76, "s": [39.896, -166.227, 0], "to": [-50.607, 2.063, 0], "ti": [0.726, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 96.217, "s": [0.254, -219.848, 0], "to": [-0.726, 0, 0], "ti": [0, 1.452, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 108.35, "s": [-0.073, -166.068, 0], "to": [0, -1.452, 0], "ti": [0, 1.452, 0]}, {"t": 120, "s": [-0.073, -180.008, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [7.898, -3.227, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [697, 697, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -5.945], [5.945, 0], [0, 5.945], [-5.945, 0]], "o": [[0, 5.945], [-5.945, 0], [0, -5.945], [5.945, 0]], "v": [[18.661, -3.227], [7.898, 7.536], [-2.866, -3.227], [7.898, -13.991]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.2, 0.2, 0.2, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-21-avatar').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": -40, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 217.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "mask", "parent": 5, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.094, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [27.427, -178.258, 0], "to": [0, -1.452, 0], "ti": [0, -0.072, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 12, "s": [27.427, -221.285, 0], "to": [0.013, 49.491, 0], "ti": [6.026, 1.059, 0]}, {"t": 22, "s": [-29.084, -163.606, 0], "h": 1}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 44.066, "s": [-29.084, -163.606, 0], "to": [44.902, 32.338, 0], "ti": [-13.08, 17.895, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 62, "s": [67.396, -164.477, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.257, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 76, "s": [67.396, -164.477, 0], "to": [-50.607, 2.063, 0], "ti": [0.726, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 96.217, "s": [27.754, -218.098, 0], "to": [-0.726, 0, 0], "ti": [0, 1.452, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 108.35, "s": [27.427, -164.318, 0], "to": [0, -1.452, 0], "ti": [0, 1.452, 0]}, {"t": 120, "s": [27.427, -178.258, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [7.898, -3.227, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [697, 697, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -5.945], [5.945, 0], [0, 5.945], [-5.945, 0]], "o": [[0, 5.945], [-5.945, 0], [0, -5.945], [5.945, 0]], "v": [[18.661, -3.227], [7.898, 7.536], [-2.866, -3.227], [7.898, -13.991]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": -40, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.780392229557, 0.21960785985, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 217.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Head shadow", "parent": 5, "tt": 2, "tp": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.094, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [-0.073, -180.008, 0], "to": [0, -1.452, 0], "ti": [0, -0.072, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 12, "s": [-0.073, -223.035, 0], "to": [0.013, 49.491, 0], "ti": [6.026, 1.059, 0]}, {"t": 22, "s": [-56.584, -165.356, 0], "h": 1}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 44.066, "s": [-56.584, -165.356, 0], "to": [44.902, 32.338, 0], "ti": [-13.08, 17.895, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 62, "s": [39.896, -166.227, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.257, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 76, "s": [39.896, -166.227, 0], "to": [-50.607, 2.063, 0], "ti": [0.726, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 96.217, "s": [0.254, -219.848, 0], "to": [-0.726, 0, 0], "ti": [0, 1.452, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 108.35, "s": [-0.073, -166.068, 0], "to": [0, -1.452, 0], "ti": [0, 1.452, 0]}, {"t": 120, "s": [-0.073, -180.008, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [7.898, -3.227, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [697, 697, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -5.945], [5.945, 0], [0, 5.945], [-5.945, 0]], "o": [[0, 5.945], [-5.945, 0], [0, -5.945], [5.945, 0]], "v": [[18.661, -3.227], [7.898, 7.536], [-2.866, -3.227], [7.898, -13.991]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": -40, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}], "ip": 0, "op": 217.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Head fill", "parent": 5, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.094, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [-0.073, -180.008, 0], "to": [0, -1.452, 0], "ti": [0, -0.072, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 12, "s": [-0.073, -223.035, 0], "to": [0.013, 49.491, 0], "ti": [6.026, 1.059, 0]}, {"t": 22, "s": [-56.584, -165.356, 0], "h": 1}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 44.066, "s": [-56.584, -165.356, 0], "to": [44.902, 32.338, 0], "ti": [-13.08, 17.895, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 62, "s": [39.896, -166.227, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.257, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 76, "s": [39.896, -166.227, 0], "to": [-50.607, 2.063, 0], "ti": [0.726, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 96.217, "s": [0.254, -219.848, 0], "to": [-0.726, 0, 0], "ti": [0, 1.452, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 108.35, "s": [-0.073, -166.068, 0], "to": [0, -1.452, 0], "ti": [0, 1.452, 0]}, {"t": 120, "s": [-0.073, -180.008, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [7.898, -3.227, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [697, 697, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -5.945], [5.945, 0], [0, 5.945], [-5.945, 0]], "o": [[0, 5.945], [-5.945, 0], [0, -5.945], [5.945, 0]], "v": [[18.661, -3.227], [7.898, 7.536], [-2.866, -3.227], [7.898, -13.991]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": -40, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}], "ip": 0, "op": 217.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Body", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 375, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 70, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 44, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 52.182, "s": [103, 97, 100]}, {"t": 62, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.138, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 70], [160, 70], [160, 45], [45, -70]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 13, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0, 0], [0, -63.513], [0, 0]], "v": [[-45.291, -107.477], [-160.291, 7.523], [-160, 56.85], [160, 56.85], [159.709, 7.523], [44.709, -107.477]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 23, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -60.138], [-160, 54.862], [-160, 70], [160, 70], [160, 54.862], [45, -60.138]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 32, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 70], [160, 70], [160, 45], [45, -70]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 44, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 70], [160, 70], [160, 45], [45, -70]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 52.182, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -60.138], [-160, 54.862], [-160, 70], [160, 70], [160, 54.862], [45, -60.138]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 62, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 70], [160, 70], [160, 45], [45, -70]], "c": true}]}, {"i": {"x": 0.086, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 81.945, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 70], [160, 70], [160, 45], [45, -70]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 96.217, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0, 0], [0, -63.513], [0, 0]], "v": [[-45.291, -107.477], [-160.291, 7.523], [-160, 56.85], [160, 56.85], [159.709, 7.523], [44.709, -107.477]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 109.297, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -60.138], [-160, 54.862], [-160, 70], [160, 70], [160, 54.862], [45, -60.138]], "c": true}]}, {"t": 120, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 70], [160, 70], [160, 45], [45, -70]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.2, 0.2, 0.2, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-21-avatar').layer('control').effect('stroke')('Menu'));"}, "lc": 1, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 22", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "mask ", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [265, 375, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 70, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 44, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 52.182, "s": [103, 97, 100]}, {"t": 62, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.138, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 70], [160, 70], [160, 45], [45, -70]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 13, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0, 0], [0, -63.513], [0, 0]], "v": [[-45.291, -107.477], [-160.291, 7.523], [-160, 56.85], [160, 56.85], [159.709, 7.523], [44.709, -107.477]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 23, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -60.138], [-160, 54.862], [-160, 70], [160, 70], [160, 54.862], [45, -60.138]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 32, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 70], [160, 70], [160, 45], [45, -70]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 44, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 70], [160, 70], [160, 45], [45, -70]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 52.182, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -60.138], [-160, 54.862], [-160, 70], [160, 70], [160, 54.862], [45, -60.138]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 62, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 70], [160, 70], [160, 45], [45, -70]], "c": true}]}, {"i": {"x": 0.086, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 81.945, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 70], [160, 70], [160, 45], [45, -70]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 96.217, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0, 0], [0, -63.513], [0, 0]], "v": [[-45.291, -107.477], [-160.291, 7.523], [-160, 56.85], [160, 56.85], [159.709, 7.523], [44.709, -107.477]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 109.297, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -60.138], [-160, 54.862], [-160, 70], [160, 70], [160, 54.862], [45, -60.138]], "c": true}]}, {"t": 120, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 70], [160, 70], [160, 45], [45, -70]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 22", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.29411765933, 0.701960802078, 0.992156922817, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Body shadow", "tt": 2, "tp": 6, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 375, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 70, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 44, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 52.182, "s": [103, 97, 100]}, {"t": 62, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.138, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-1.791, -1.976], [0, 0], [0, 0], [-1.51, 1.779], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [1.623, 1.791], [0, 0], [0, 0], [1.779, -2.097], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 62], [-158.875, 68.125], [-152.75, 70], [153, 70], [158.875, 67.875], [160, 61.75], [160, 45], [45, -70]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 13, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-2.04, -3.41], [0, 0], [0, 0], [-1.569, 2.348], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [1.195, 1.999], [0, 0], [0, 0], [2.328, -3.484], [0, 0], [0, -63.513], [0, 0]], "v": [[-45.291, -107.477], [-160.291, 7.523], [-160.07, 44.93], [-159.125, 54.35], [-153.015, 56.725], [151.527, 56.85], [158.5, 54.725], [159.926, 44.28], [159.71, 7.523], [44.709, -107.477]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 23, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-1.048, -1.03], [0, 0], [0, 0], [-1.039, 0.949], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [1.096, 1.078], [0, 0], [0, 0], [0.949, -0.867], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -60.138], [-160, 54.862], [-160, 65.592], [-159, 69.125], [-155.39, 70.125], [155.777, 70], [159.5, 69], [160, 66.143], [160, 54.862], [45, -60.138]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 32, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-1.298, -1.54], [0, 0], [0, 0], [-1.189, 1.384], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [1.071, 1.271], [0, 0], [0, 0], [1.384, -1.611], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 63.958], [-159, 69.125], [-155.015, 69.875], [154.527, 70], [159.125, 68.375], [160, 63.629], [160, 45], [45, -70]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 44, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-1.319, -1.522], [0, 0], [0, 0], [-1.152, 1.311], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [1.143, 1.319], [0, 0], [0, 0], [1.322, -1.504], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 63.958], [-158.625, 68.625], [-154.765, 70], [154.402, 70], [158.5, 68.625], [160, 63.629], [160, 45], [45, -70]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 52, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-1.356, -1.519], [0, 0], [0, 0], [-0.754, 1.051], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [1.134, 1.27], [0, 0], [0, 0], [1.075, -1.499], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -60.152], [-160, 54.848], [-160.121, 63.89], [-159.148, 68.967], [-154.896, 69.742], [156.121, 69.871], [159.27, 68.838], [160, 64.464], [160, 54.848], [45, -60.152]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 62, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-1.791, -1.976], [0, 0], [0, 0], [-1.51, 1.779], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [1.623, 1.791], [0, 0], [0, 0], [1.779, -2.097], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 62], [-158.875, 68.125], [-152.75, 70], [153, 70], [158.875, 67.875], [160, 61.75], [160, 45], [45, -70]], "c": true}]}, {"i": {"x": 0.138, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 82, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-1.791, -1.976], [0, 0], [0, 0], [-1.51, 1.779], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [1.623, 1.791], [0, 0], [0, 0], [1.779, -2.097], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 62], [-158.875, 68.125], [-152.75, 70], [153, 70], [158.875, 67.875], [160, 61.75], [160, 45], [45, -70]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 96, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-2.04, -3.41], [0, 0], [0, 0], [-1.569, 2.348], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [1.195, 1.999], [0, 0], [0, 0], [2.328, -3.484], [0, 0], [0, -63.513], [0, 0]], "v": [[-45.291, -107.477], [-160.291, 7.523], [-160.07, 44.93], [-159.125, 54.35], [-153.015, 56.725], [151.527, 56.85], [158.5, 54.725], [159.926, 44.28], [159.71, 7.523], [44.709, -107.477]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 109, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-0.884, -0.84], [0, 0], [0, 0], [-1.129, 0.974], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0.995, 0.946], [0, 0], [0, 0], [0.974, -0.84], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -60.138], [-160, 54.862], [-160, 66.342], [-159.25, 68.875], [-155.89, 70.25], [155.527, 70], [159.375, 68.75], [160, 66.143], [160, 54.862], [45, -60.138]], "c": true}]}, {"t": 120, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-1.791, -1.976], [0, 0], [0, 0], [-1.51, 1.779], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [1.623, 1.791], [0, 0], [0, 0], [1.779, -2.097], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 62], [-158.875, 68.125], [-152.75, 70], [153, 70], [158.875, 67.875], [160, 61.75], [160, 45], [45, -70]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 22", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}], "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Body fill", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 375, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 70, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 44, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 52.182, "s": [103, 97, 100]}, {"t": 62, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.138, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-1.125, -1.125], [0, 0], [0, 0], [-1.312, 1.312], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [1.25, 1.25], [0, 0], [0, 0], [1.172, -1.172], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 64.07], [-159.188, 68.562], [-155.484, 70], [155.891, 70], [158.938, 68.688], [160, 63.791], [160, 45], [45, -70]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 13, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-2.04, -3.41], [0, 0], [0, 0], [-1.569, 2.348], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [1.195, 1.999], [0, 0], [0, 0], [2.328, -3.484], [0, 0], [0, -63.513], [0, 0]], "v": [[-45.291, -107.477], [-160.291, 7.523], [-160.07, 44.93], [-159.125, 54.35], [-153.015, 56.725], [151.527, 56.85], [158.5, 54.725], [159.926, 44.28], [159.71, 7.523], [44.709, -107.477]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 23, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-1.048, -1.03], [0, 0], [0, 0], [-1.039, 0.949], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [1.096, 1.078], [0, 0], [0, 0], [0.949, -0.867], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -60.138], [-160, 54.862], [-160, 65.592], [-159, 69.125], [-155.39, 70.125], [155.777, 70], [159.5, 69], [160, 66.143], [160, 54.862], [45, -60.138]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 32, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-1.298, -1.54], [0, 0], [0, 0], [-1.189, 1.384], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [1.071, 1.271], [0, 0], [0, 0], [1.384, -1.611], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 63.958], [-159, 69.125], [-155.015, 69.875], [154.527, 70], [159.125, 68.375], [160, 63.629], [160, 45], [45, -70]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 44, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-1.319, -1.522], [0, 0], [0, 0], [-1.152, 1.311], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [1.143, 1.319], [0, 0], [0, 0], [1.322, -1.504], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 63.958], [-158.625, 68.625], [-154.765, 70], [154.402, 70], [158.5, 68.625], [160, 63.629], [160, 45], [45, -70]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 52, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-1.356, -1.519], [0, 0], [0, 0], [-0.754, 1.051], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [1.134, 1.27], [0, 0], [0, 0], [1.075, -1.499], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -60.152], [-160, 54.848], [-160.121, 63.89], [-159.148, 68.967], [-154.896, 69.742], [156.121, 69.871], [159.27, 68.838], [160, 64.464], [160, 54.848], [45, -60.152]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 62, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-1.791, -1.976], [0, 0], [0, 0], [-1.51, 1.779], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [1.623, 1.791], [0, 0], [0, 0], [1.779, -2.097], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 62], [-158.875, 68.125], [-152.75, 70], [153, 70], [158.875, 67.875], [160, 61.75], [160, 45], [45, -70]], "c": true}]}, {"i": {"x": 0.138, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 82, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-1.791, -1.976], [0, 0], [0, 0], [-1.51, 1.779], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [1.623, 1.791], [0, 0], [0, 0], [1.779, -2.097], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 62], [-158.875, 68.125], [-152.75, 70], [153, 70], [158.875, 67.875], [160, 61.75], [160, 45], [45, -70]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 96, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-2.04, -3.41], [0, 0], [0, 0], [-1.569, 2.348], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [1.195, 1.999], [0, 0], [0, 0], [2.328, -3.484], [0, 0], [0, -63.513], [0, 0]], "v": [[-45.291, -107.477], [-160.291, 7.523], [-160.07, 44.93], [-159.125, 54.35], [-153.015, 56.725], [151.527, 56.85], [158.5, 54.725], [159.926, 44.28], [159.71, 7.523], [44.709, -107.477]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 109, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-0.884, -0.84], [0, 0], [0, 0], [-1.129, 0.974], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0.995, 0.946], [0, 0], [0, 0], [0.974, -0.84], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -60.138], [-160, 54.862], [-160, 66.342], [-159.25, 68.875], [-155.89, 70.25], [155.527, 70], [159.375, 68.75], [160, 66.143], [160, 54.862], [45, -60.138]], "c": true}]}, {"t": 120, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-1.125, -1.125], [0, 0], [0, 0], [-1.312, 1.312], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [1.25, 1.25], [0, 0], [0, 0], [1.172, -1.172], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 64.07], [-159.188, 68.562], [-155.484, 70], [155.891, 70], [158.938, 68.688], [160, 63.791], [160, 45], [45, -70]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 22", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}], "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}]}, {"id": "comp_3", "nm": "hover-nodding", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.412, "y": 1}, "o": {"x": 0.452, "y": 0}, "t": 0, "s": [215, 125, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.603, "y": 1}, "o": {"x": 0.319, "y": 0}, "t": 23, "s": [215, 195, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.412, "y": 1}, "o": {"x": 0.452, "y": 0}, "t": 60, "s": [215, 125, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.603, "y": 1}, "o": {"x": 0.319, "y": 0}, "t": 83, "s": [215, 195, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 120, "s": [215, 125, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-41.421, 0], [0, 41.421], [41.421, 0], [0, -41.421]], "o": [[41.421, 0], [0, -41.421], [-41.421, 0], [0, 41.421]], "v": [[0, 75], [75, 0], [0, -75], [-75, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.2, 0.2, 0.2, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-21-avatar').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "mask 2", "parent": 1, "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [27.427, 1.742, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [7.898, -3.227, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [697, 697, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -5.945], [5.945, 0], [0, 5.945], [-5.945, 0]], "o": [[0, 5.945], [-5.945, 0], [0, -5.945], [5.945, 0]], "v": [[18.661, -3.227], [7.898, 7.536], [-2.866, -3.227], [7.898, -13.991]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": -40, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.780392229557, 0.21960785985, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 217.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Head shadow", "parent": 1, "tt": 2, "tp": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-0.073, -0.008, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [7.898, -3.227, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [697, 697, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -5.945], [5.945, 0], [0, 5.945], [-5.945, 0]], "o": [[0, 5.945], [-5.945, 0], [0, -5.945], [5.945, 0]], "v": [[18.661, -3.227], [7.898, 7.536], [-2.866, -3.227], [7.898, -13.991]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": -40, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}], "ip": 0, "op": 217.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Head fill", "parent": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [-0.073, -0.008, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [7.898, -3.227, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [697, 697, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -5.945], [5.945, 0], [0, 5.945], [-5.945, 0]], "o": [[0, 5.945], [-5.945, 0], [0, -5.945], [5.945, 0]], "v": [[18.661, -3.227], [7.898, 7.536], [-2.866, -3.227], [7.898, -13.991]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": -40, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}], "ip": 0, "op": 217.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "mask", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [265, 305, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.412, "y": 1}, "o": {"x": 0.452, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 70], [160, 70], [160, 45], [45, -70]], "c": true}]}, {"i": {"x": 0.603, "y": 1}, "o": {"x": 0.319, "y": 0}, "t": 23, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -46.988], [-160, 68.012], [-160, 70], [160, 70], [160, 68.012], [45, -46.988]], "c": true}]}, {"i": {"x": 0.412, "y": 1}, "o": {"x": 0.452, "y": 0}, "t": 60, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 70], [160, 70], [160, 45], [45, -70]], "c": true}]}, {"i": {"x": 0.603, "y": 1}, "o": {"x": 0.319, "y": 0}, "t": 83, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -46.988], [-160, 68.012], [-160, 70], [160, 70], [160, 68.012], [45, -46.988]], "c": true}]}, {"t": 120, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 70], [160, 70], [160, 45], [45, -70]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 22", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.29411765933, 0.701960802078, 0.992156863213, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Body", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 305, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.412, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-1.622, -1.315], [0, 0], [0, 0], [-1.252, 1.187], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [1.099, 0.891], [0, 0], [0, 0], [1.033, -0.98], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -69.951], [-160, 45.049], [-160, 65.871], [-158.566, 68.878], [-156.291, 70], [156.582, 70], [159.064, 69.188], [160, 66.53], [160, 45.049], [45, -69.951]], "c": true}]}, {"i": {"x": 0.603, "y": 1}, "o": {"x": 0.319, "y": 0}, "t": 23, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-0.906, -0.685], [0, 0], [0, 0], [-0.987, 0.579], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [1.121, 0.847], [0, 0], [0, 0], [0.579, -0.339], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -46.988], [-160.25, 66.137], [-160.125, 66.595], [-159, 68.75], [-155.788, 69.875], [156.567, 70], [159.125, 69.188], [160, 67.988], [160, 68.012], [45, -46.988]], "c": true}]}, {"i": {"x": 0.412, "y": 1}, "o": {"x": 0.452, "y": 0}, "t": 60, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-1.564, -3.455], [0, 0], [0, 0], [-1.481, 1.856], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0.708, 1.564], [0, 0], [0, 0], [1.829, -2.293], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 58.624], [-158.812, 68.312], [-154.85, 70], [152.879, 70.125], [158.625, 68.25], [160, 61.2], [160, 45], [45, -70]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.319, "y": 0}, "t": 83, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-0.497, -0.395], [0, 0], [0, 0], [-1.111, 0.522], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0.627, 0.497], [0, 0], [0, 0], [0.513, -0.241], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -46.988], [-160, 68.012], [-160, 68.095], [-159.5, 69.5], [-157.6, 70], [156.317, 70], [159.25, 69.25], [159.938, 68.3], [160, 68.012], [45, -46.988]], "c": true}]}, {"t": 120, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-1.622, -1.315], [0, 0], [0, 0], [-1.252, 1.187], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [1.099, 0.891], [0, 0], [0, 0], [1.033, -0.98], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -69.951], [-160, 45.049], [-160, 65.871], [-158.566, 68.878], [-156.291, 70], [156.582, 70], [159.064, 69.188], [160, 66.53], [160, 45.049], [45, -69.951]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.2, 0.2, 0.2, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 7, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-21-avatar').layer('control').effect('stroke')('Menu'));"}, "lc": 1, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 22", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Body shadow", "tt": 2, "tp": 5, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 305, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.412, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-1.622, -1.315], [0, 0], [0, 0], [-1.252, 1.187], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [1.099, 0.891], [0, 0], [0, 0], [1.033, -0.98], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -69.951], [-160, 45.049], [-160, 65.871], [-158.566, 68.878], [-156.291, 70], [156.582, 70], [159.064, 69.188], [160, 66.53], [160, 45.049], [45, -69.951]], "c": true}]}, {"i": {"x": 0.603, "y": 1}, "o": {"x": 0.319, "y": 0}, "t": 23, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-0.906, -0.685], [0, 0], [0, 0], [-0.987, 0.579], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [1.121, 0.847], [0, 0], [0, 0], [0.579, -0.339], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -46.988], [-160.25, 66.137], [-160.125, 66.595], [-159, 68.75], [-155.788, 69.875], [156.567, 70], [159.125, 69.188], [160, 67.988], [160, 68.012], [45, -46.988]], "c": true}]}, {"i": {"x": 0.412, "y": 1}, "o": {"x": 0.452, "y": 0}, "t": 60, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-1.564, -3.455], [0, 0], [0, 0], [-1.481, 1.856], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0.708, 1.564], [0, 0], [0, 0], [1.829, -2.293], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 58.624], [-158.812, 68.312], [-154.85, 70], [152.879, 70.125], [158.625, 68.25], [160, 61.2], [160, 45], [45, -70]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.319, "y": 0}, "t": 83, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-0.497, -0.395], [0, 0], [0, 0], [-1.111, 0.522], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0.627, 0.497], [0, 0], [0, 0], [0.513, -0.241], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -46.988], [-160, 68.012], [-160, 68.095], [-159.5, 69.5], [-157.6, 70], [156.317, 70], [159.25, 69.25], [159.938, 68.3], [160, 68.012], [45, -46.988]], "c": true}]}, {"t": 120, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-1.622, -1.315], [0, 0], [0, 0], [-1.252, 1.187], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [1.099, 0.891], [0, 0], [0, 0], [1.033, -0.98], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -69.951], [-160, 45.049], [-160, 65.871], [-158.566, 68.878], [-156.291, 70], [156.582, 70], [159.064, 69.188], [160, 66.53], [160, 45.049], [45, -69.951]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 22", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}], "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Body fill", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 305, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.412, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-1.622, -1.315], [0, 0], [0, 0], [-1.252, 1.187], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [1.099, 0.891], [0, 0], [0, 0], [1.033, -0.98], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -69.951], [-160, 45.049], [-160, 65.871], [-158.566, 68.878], [-156.291, 70], [156.582, 70], [159.064, 69.188], [160, 66.53], [160, 45.049], [45, -69.951]], "c": true}]}, {"i": {"x": 0.603, "y": 1}, "o": {"x": 0.319, "y": 0}, "t": 23, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-0.906, -0.685], [0, 0], [0, 0], [-0.987, 0.579], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [1.121, 0.847], [0, 0], [0, 0], [0.579, -0.339], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -46.988], [-160.25, 66.137], [-160.125, 66.595], [-159, 68.75], [-155.788, 69.875], [156.567, 70], [159.125, 69.188], [160, 67.988], [160, 68.012], [45, -46.988]], "c": true}]}, {"i": {"x": 0.412, "y": 1}, "o": {"x": 0.452, "y": 0}, "t": 60, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-1.564, -3.455], [0, 0], [0, 0], [-1.481, 1.856], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0.708, 1.564], [0, 0], [0, 0], [1.829, -2.293], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -70], [-160, 45], [-160, 58.624], [-158.812, 68.312], [-154.85, 70], [152.879, 70.125], [158.625, 68.25], [160, 61.2], [160, 45], [45, -70]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.319, "y": 0}, "t": 83, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-0.497, -0.395], [0, 0], [0, 0], [-1.111, 0.522], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [0.627, 0.497], [0, 0], [0, 0], [0.513, -0.241], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -46.988], [-160, 68.012], [-160, 68.095], [-159.5, 69.5], [-157.6, 70], [156.317, 70], [159.25, 69.25], [159.938, 68.3], [160, 68.012], [45, -46.988]], "c": true}]}, {"t": 120, "s": [{"i": [[0, 0], [0, -63.513], [0, 0], [-1.622, -1.315], [0, 0], [0, 0], [-1.252, 1.187], [0, 0], [0, 0], [63.513, 0]], "o": [[-63.513, 0], [0, 0], [0, 0], [1.099, 0.891], [0, 0], [0, 0], [1.033, -0.98], [0, 0], [0, -63.513], [0, 0]], "v": [[-45, -69.951], [-160, 45.049], [-160, 65.871], [-158.566, 68.878], [-156.291, 70], [156.582, 70], [159.064, 69.188], [160, 66.53], [160, 45.049], [45, -69.951]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 22", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}], "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}]}, {"id": "comp_4", "nm": "morph-group", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "head", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [214.695, 359.73, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [9.534, 31.836, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [697, 697, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.4, "y": 1}, "o": {"x": 0.6, "y": 0}, "t": 0, "s": [{"i": [[0, -5.943], [5.943, 0], [0, 5.943], [-5.943, 0]], "o": [[0, 5.943], [-5.943, 0], [0, -5.943], [5.943, 0]], "v": [[20.337, -1.841], [9.576, 8.919], [-1.184, -1.841], [9.576, -12.602]], "c": true}]}, {"t": 30, "s": [{"i": [[0, -4.726], [4.726, 0], [0, 4.726], [-4.726, 0]], "o": [[0, 4.726], [-4.726, 0], [0, -4.726], [4.726, 0]], "v": [[18.132, 1.121], [9.575, 9.678], [1.018, 1.121], [9.575, -7.437]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.2, 0.2, 0.2, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-21-avatar').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 76, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 217.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "mask", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [242.195, 361.105, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [9.534, 31.836, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [697, 697, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.4, "y": 1}, "o": {"x": 0.6, "y": 0}, "t": 0, "s": [{"i": [[0, -5.943], [5.943, 0], [0, 5.943], [-5.943, 0]], "o": [[0, 5.943], [-5.943, 0], [0, -5.943], [5.943, 0]], "v": [[20.337, -1.841], [9.576, 8.919], [-1.184, -1.841], [9.576, -12.602]], "c": true}]}, {"t": 30, "s": [{"i": [[0, -4.726], [4.726, 0], [0, 4.726], [-4.726, 0]], "o": [[0, 4.726], [-4.726, 0], [0, -4.726], [4.726, 0]], "v": [[18.132, 1.121], [9.575, 9.678], [1.018, 1.121], [9.575, -7.437]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.780392169952, 0.219607844949, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 76, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 217.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "head shadow", "tt": 2, "tp": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [214.695, 359.73, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [9.534, 31.836, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [697, 697, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.4, "y": 1}, "o": {"x": 0.6, "y": 0}, "t": 0, "s": [{"i": [[0, -5.943], [5.943, 0], [0, 5.943], [-5.943, 0]], "o": [[0, 5.943], [-5.943, 0], [0, -5.943], [5.943, 0]], "v": [[20.337, -1.841], [9.576, 8.919], [-1.184, -1.841], [9.576, -12.602]], "c": true}]}, {"t": 30, "s": [{"i": [[0, -4.726], [4.726, 0], [0, 4.726], [-4.726, 0]], "o": [[0, 4.726], [-4.726, 0], [0, -4.726], [4.726, 0]], "v": [[18.132, 1.121], [9.575, 9.678], [1.018, 1.121], [9.575, -7.437]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 217.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "head", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [214.695, 359.73, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [9.534, 31.836, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [697, 697, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.4, "y": 1}, "o": {"x": 0.6, "y": 0}, "t": 0, "s": [{"i": [[0, -5.943], [5.943, 0], [0, 5.943], [-5.943, 0]], "o": [[0, 5.943], [-5.943, 0], [0, -5.943], [5.943, 0]], "v": [[20.337, -1.841], [9.576, 8.919], [-1.184, -1.841], [9.576, -12.602]], "c": true}]}, {"t": 30, "s": [{"i": [[0, -4.726], [4.726, 0], [0, 4.726], [-4.726, 0]], "o": [[0, 4.726], [-4.726, 0], [0, -4.726], [4.726, 0]], "v": [[18.132, 1.121], [9.575, 9.678], [1.018, 1.121], [9.575, -7.437]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 76, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 217.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "body", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [214.695, 359.73, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [9.534, 31.836, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [697, 697, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 1}, "o": {"x": 0.6, "y": 0}, "t": 0, "s": [{"i": [[0.253, -0.284], [0, 0], [0, 0], [0.144, 0.198], [0, 0], [0, 0], [-8.888, 0], [0, 0], [0, -9.033], [0, 0]], "o": [[-0.223, 0.251], [0, 0], [0, 0], [-0.108, -0.183], [0, 0], [0, -8.888], [0, 0], [9.033, 0], [0, 0], [0, 0]], "v": [[32.357, 33.77], [31.458, 33.985], [-12.642, 33.985], [-13.288, 33.806], [-13.369, 32.986], [-13.344, 30.153], [2.749, 14.06], [16.142, 14.06], [32.498, 30.416], [32.475, 32.843]], "c": true}]}, {"t": 31, "s": [{"i": [[0.156, -0.192], [0, 0], [0, 0], [0.15, 0.126], [0, 0], [0, 0], [-7.035, 0], [0, 0], [0, -6.916], [0, 0]], "o": [[-0.141, 0.173], [0, 0], [0, 0], [-0.121, -0.102], [0, 0], [0, -7.035], [0, 0], [6.916, 0], [0, 0], [0, 0]], "v": [[27.655, 29.688], [27.081, 29.869], [-8.075, 29.869], [-8.587, 29.742], [-8.721, 29.324], [-8.675, 26.638], [4.063, 13.9], [15.276, 13.9], [27.799, 26.423], [27.757, 29.036]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.2, 0.2, 0.2, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-21-avatar').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 217.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "mask", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [265.945, 359.73, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [9.534, 31.836, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [697, 697, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 1}, "o": {"x": 0.6, "y": 0}, "t": 0, "s": [{"i": [[0.253, -0.284], [0, 0], [0, 0], [0.144, 0.198], [0, 0], [0, 0], [-8.888, 0], [0, 0], [0, -9.033], [0, 0]], "o": [[-0.223, 0.251], [0, 0], [0, 0], [-0.108, -0.183], [0, 0], [0, -8.888], [0, 0], [9.033, 0], [0, 0], [0, 0]], "v": [[32.357, 33.77], [31.458, 33.985], [-12.642, 33.985], [-13.288, 33.806], [-13.369, 32.986], [-13.344, 30.153], [2.749, 14.06], [16.142, 14.06], [32.498, 30.416], [32.475, 32.843]], "c": true}]}, {"t": 31, "s": [{"i": [[0.156, -0.192], [0, 0], [0, 0], [0.15, 0.126], [0, 0], [0, 0], [-7.035, 0], [0, 0], [0, -6.916], [0, 0]], "o": [[-0.141, 0.173], [0, 0], [0, 0], [-0.121, -0.102], [0, 0], [0, -7.035], [0, 0], [6.916, 0], [0, 0], [0, 0]], "v": [[27.655, 29.688], [27.081, 29.869], [-8.075, 29.869], [-8.587, 29.742], [-8.721, 29.324], [-8.675, 26.638], [4.063, 13.9], [15.276, 13.9], [27.799, 26.423], [27.757, 29.036]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.29411765933, 0.701960802078, 0.992156863213, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 76, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 217.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "body shadow", "tt": 2, "tp": 6, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [214.695, 359.73, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [9.534, 31.836, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [697, 697, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 1}, "o": {"x": 0.6, "y": 0}, "t": 0, "s": [{"i": [[0.253, -0.284], [0, 0], [0, 0], [0.144, 0.198], [0, 0], [0, 0], [-8.888, 0], [0, 0], [0, -9.033], [0, 0]], "o": [[-0.223, 0.251], [0, 0], [0, 0], [-0.13, -0.187], [0, 0], [0, -8.888], [0, 0], [9.033, 0], [0, 0], [0, 0]], "v": [[32.357, 33.77], [31.458, 33.985], [-12.642, 33.985], [-13.288, 33.806], [-13.369, 32.986], [-13.344, 30.153], [2.749, 14.06], [16.142, 14.06], [32.498, 30.416], [32.475, 32.843]], "c": true}]}, {"t": 31, "s": [{"i": [[0.156, -0.192], [0, 0], [0, 0], [0.15, 0.126], [0, 0], [0, 0], [-7.035, 0], [0, 0], [0, -6.916], [0, 0]], "o": [[-0.141, 0.173], [0, 0], [0, 0], [-0.121, -0.102], [0, 0], [0, -7.035], [0, 0], [6.916, 0], [0, 0], [0, 0]], "v": [[27.655, 29.688], [27.081, 29.869], [-8.075, 29.869], [-8.587, 29.742], [-8.721, 29.324], [-8.675, 26.638], [4.063, 13.9], [15.276, 13.9], [27.799, 26.423], [27.757, 29.036]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 217.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "body fill", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [214.695, 359.73, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [9.534, 31.836, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [697, 697, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 1}, "o": {"x": 0.6, "y": 0}, "t": 0, "s": [{"i": [[0.253, -0.284], [0, 0], [0, 0], [0.144, 0.198], [0, 0], [0, 0], [-8.888, 0], [0, 0], [0, -9.033], [0, 0]], "o": [[-0.223, 0.251], [0, 0], [0, 0], [-0.108, -0.183], [0, 0], [0, -8.888], [0, 0], [9.033, 0], [0, 0], [0, 0]], "v": [[32.357, 33.77], [31.458, 33.985], [-12.642, 33.985], [-13.288, 33.806], [-13.369, 32.986], [-13.344, 30.153], [2.749, 14.06], [16.142, 14.06], [32.498, 30.416], [32.475, 32.843]], "c": true}]}, {"t": 31, "s": [{"i": [[0.156, -0.192], [0, 0], [0, 0], [0.15, 0.126], [0, 0], [0, 0], [-7.035, 0], [0, 0], [0, -6.916], [0, 0]], "o": [[-0.141, 0.173], [0, 0], [0, 0], [-0.121, -0.102], [0, 0], [0, -7.035], [0, 0], [6.916, 0], [0, 0], [0, 0]], "v": [[27.655, 29.688], [27.081, 29.869], [-8.075, 29.869], [-8.587, 29.742], [-8.721, 29.324], [-8.675, 26.638], [4.063, 13.9], [15.276, 13.9], [27.799, 26.423], [27.757, 29.036]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 76, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 217.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "Head L ", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.4, "y": 1}, "o": {"x": 0.6, "y": 0}, "t": 0, "s": [-193.092, 217.318, 0], "to": [58.333, 0, 0], "ti": [-58.333, 0, 0]}, {"t": 30, "s": [156.908, 217.318, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [17.886, 11.404, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [697, 697, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -4.141], [4.141, 0], [0, 4.141], [-4.141, 0]], "o": [[0, 4.141], [-4.141, 0], [0, -4.141], [4.141, 0]], "v": [[25.255, 3.361], [17.757, 10.859], [10.259, 3.361], [17.757, -4.137]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.2, 0.2, 0.2, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-21-avatar').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 217.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "mask", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.4, "y": 1}, "o": {"x": 0.6, "y": 0}, "t": 0, "s": [-173.092, 217.318, 0], "to": [58.333, 0, 0], "ti": [-58.333, 0, 0]}, {"t": 30, "s": [176.908, 217.318, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [17.886, 11.404, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [697, 697, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -4.141], [4.141, 0], [0, 4.141], [-4.141, 0]], "o": [[0, 4.141], [-4.141, 0], [0, -4.141], [4.141, 0]], "v": [[25.255, 3.361], [17.757, 10.859], [10.259, 3.361], [17.757, -4.137]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 76, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.780392229557, 0.21960785985, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 217.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "Head <PERSON> shadow", "tt": 2, "tp": 10, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.4, "y": 1}, "o": {"x": 0.6, "y": 0}, "t": 0, "s": [-193.092, 217.318, 0], "to": [58.333, 0, 0], "ti": [-58.333, 0, 0]}, {"t": 30, "s": [156.908, 217.318, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [17.886, 11.404, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [697, 697, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -4.141], [4.141, 0], [0, 4.141], [-4.141, 0]], "o": [[0, 4.141], [-4.141, 0], [0, -4.141], [4.141, 0]], "v": [[25.255, 3.361], [17.757, 10.859], [10.259, 3.361], [17.757, -4.137]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 76, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}], "ip": 0, "op": 217.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "Head L fill", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.4, "y": 1}, "o": {"x": 0.6, "y": 0}, "t": 0, "s": [-193.092, 217.318, 0], "to": [58.333, 0, 0], "ti": [-58.333, 0, 0]}, {"t": 30, "s": [156.908, 217.318, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [17.886, 11.404, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [697, 697, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -4.141], [4.141, 0], [0, 4.141], [-4.141, 0]], "o": [[0, 4.141], [-4.141, 0], [0, -4.141], [4.141, 0]], "v": [[25.255, 3.361], [17.757, 10.859], [10.259, 3.361], [17.757, -4.137]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 76, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}], "ip": 0, "op": 217.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "Warstwa 12", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.4, "y": 1}, "o": {"x": 0.6, "y": 0}, "t": 0, "s": [-193.092, 217.318, 0], "to": [58.333, 0, 0], "ti": [-58.333, 0, 0]}, {"t": 30, "s": [156.908, 217.318, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [17.886, 11.404, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [697, 697, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [-5.743, 0], [0, 0], [0, -5.743]], "o": [[0, 0], [0, 0], [0, -5.743], [0, 0], [5.743, 0], [0, 0]], "v": [[34.206, 26.944], [1.517, 26.944], [1.567, 25.411], [11.965, 15.012], [23.857, 15.012], [34.255, 25.411]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.2, 0.2, 0.2, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-21-avatar').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 1, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 76, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 217.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "Head R", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.4, "y": 1}, "o": {"x": 0.6, "y": 0}, "t": 0, "s": [586.908, 217.318, 0], "to": [-52.333, 0, 0], "ti": [52.333, 0, 0]}, {"t": 30, "s": [272.908, 217.318, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [17.886, 11.404, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [697, 697, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -4.141], [4.141, 0], [0, 4.141], [-4.141, 0]], "o": [[0, 4.141], [-4.141, 0], [0, -4.141], [4.141, 0]], "v": [[25.255, 3.361], [17.757, 10.859], [10.259, 3.361], [17.757, -4.137]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.2, 0.2, 0.2, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-21-avatar').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 217.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 4, "nm": "mask", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.4, "y": 1}, "o": {"x": 0.6, "y": 0}, "t": 0, "s": [601.908, 217.318, 0], "to": [-51.5, 0, 0], "ti": [51.5, 0, 0]}, {"t": 30, "s": [292.908, 217.318, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [17.886, 11.404, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [697, 697, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -4.141], [4.141, 0], [0, 4.141], [-4.141, 0]], "o": [[0, 4.141], [-4.141, 0], [0, -4.141], [4.141, 0]], "v": [[25.255, 3.361], [17.757, 10.859], [10.259, 3.361], [17.757, -4.137]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.780392229557, 0.21960785985, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 76, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 217.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 4, "nm": "Head <PERSON> shadow", "tt": 2, "tp": 15, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.4, "y": 1}, "o": {"x": 0.6, "y": 0}, "t": 0, "s": [586.908, 217.318, 0], "to": [-52.333, 0, 0], "ti": [52.333, 0, 0]}, {"t": 30, "s": [272.908, 217.318, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [17.886, 11.404, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [697, 697, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -4.141], [4.141, 0], [0, 4.141], [-4.141, 0]], "o": [[0, 4.141], [-4.141, 0], [0, -4.141], [4.141, 0]], "v": [[25.255, 3.361], [17.757, 10.859], [10.259, 3.361], [17.757, -4.137]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 50, "ix": 5}, "r": 1, "bm": 1, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 76, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 217.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 17, "ty": 4, "nm": "Head R fill", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.4, "y": 1}, "o": {"x": 0.6, "y": 0}, "t": 0, "s": [586.908, 217.318, 0], "to": [-52.333, 0, 0], "ti": [52.333, 0, 0]}, {"t": 30, "s": [272.908, 217.318, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [17.886, 11.404, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [697, 697, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -4.141], [4.141, 0], [0, 4.141], [-4.141, 0]], "o": [[0, 4.141], [-4.141, 0], [0, -4.141], [4.141, 0]], "v": [[25.255, 3.361], [17.757, 10.859], [10.259, 3.361], [17.757, -4.137]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 76, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 217.5, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 18, "ty": 4, "nm": "Warstwa 11", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.4, "y": 1}, "o": {"x": 0.6, "y": 0}, "t": 0, "s": [586.908, 217.318, 0], "to": [-52.333, 0, 0], "ti": [52.333, 0, 0]}, {"t": 30, "s": [272.908, 217.318, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [17.886, 11.404, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [697, 697, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [-5.743, 0], [0, 0], [0, -5.743]], "o": [[0, 0], [0, 0], [0, -5.743], [0, 0], [5.743, 0], [0, 0]], "v": [[34.206, 26.944], [1.517, 26.944], [1.567, 25.411], [11.965, 15.012], [23.857, 15.012], [34.255, 25.411]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.2, 0.2, 0.2, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 2), comp('wired-lineal-21-avatar').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 1, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "fl", "c": {"a": 0, "k": [0.4, 0.4, 0.4, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-21-avatar').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 76, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 217.5, "st": 0, "ct": 1, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "control", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "stroke", "np": 3, "mn": "Pseudo/@@1tRDZ54TR8StyaTtD+slSg", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "<PERSON><PERSON>", "mn": "Pseudo/@@1tRDZ54TR8StyaTtD+slSg-0001", "ix": 1, "v": {"a": 0, "k": 2, "ix": 1}}]}, {"ty": 5, "nm": "primary", "np": 3, "mn": "ADBE Color Control", "ix": 2, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.2, 0.2, 0.2], "ix": 1}}]}, {"ty": 5, "nm": "secondary", "np": 3, "mn": "ADBE Color Control", "ix": 3, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.4, 0.4, 0.4], "ix": 1}}]}, {"ty": 5, "nm": "tertiary", "np": 3, "mn": "ADBE Color Control", "ix": 4, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.4, 0.4, 0.4], "ix": 1}}]}], "ip": 0, "op": 462, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "in-reveal", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 0, "op": 100, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "hover-jumping", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 100, "op": 170, "st": 100, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "hover-looking-around", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 170, "op": 300, "st": 170, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 0, "nm": "hover-nodding", "refId": "comp_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 300, "op": 430, "st": 300, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 0, "nm": "morph-group", "refId": "comp_4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 430, "op": 471, "st": 430, "bm": 0}], "markers": [{"tm": 0, "cm": "in-reveal", "dr": 90}, {"tm": 100, "cm": "default:hover-jumping", "dr": 60}, {"tm": 170, "cm": "hover-looking-around", "dr": 120}, {"tm": 300, "cm": "hover-nodding", "dr": 120}, {"tm": 430, "cm": "morph-group", "dr": 31}], "props": {}}