{"v": "5.12.1", "fr": 60, "ip": 0, "op": 120, "w": 430, "h": 430, "nm": "wired-lineal-821-airconditioner", "ddd": 0, "assets": [{"id": "comp_1", "nm": "machine", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "outline 12", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [214, 131.351, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": -1, "s": [{"i": [[0, 0], [0, 0], [0, 32.21], [0, 0], [0, 0], [0, 0], [0.121, -3.563]], "o": [[0, 0], [-0.145, 0], [0, 0], [0, 0], [0, 0], [-0.011, 38.179], [0, 0]], "v": [[2.758, 66.174], [2.763, 66.411], [2.5, 8.101], [2.5, -66.149], [3, -66.267], [3, 7.983], [2.757, 66.224]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 21, "s": [{"i": [[0, 0], [0, 0], [0, 32.21], [0, 0], [0, 0], [0, 0], [26.85, -3.563]], "o": [[0, 0], [-32.2, 0], [0, 0], [0, 0], [0, 0], [-2.37, 38.179], [0, 0]], "v": [[4.56, 66.174], [5.69, 66.411], [-52.62, 8.101], [-52.62, -66.149], [58.12, -66.267], [58.12, 7.983], [4.4, 66.224]], "c": false}]}, {"t": 59, "s": [{"i": [[0, 0], [0, 0], [0, 32.21], [0, 0], [0, 0], [0, 0], [26.85, -3.563]], "o": [[0, 0], [-32.2, 0], [0, 0], [0, 0], [0, 0], [-2.37, 38.179], [0, 0]], "v": [[106.56, 66.161], [-101.81, 66.28], [-160.12, 7.97], [-160.12, -66.28], [160.12, -66.28], [160.12, 7.97], [106.4, 66.211]], "c": false}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-821-airconditioner').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10.5, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-lineal-821-airconditioner').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 843, "st": -1, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Vector :<PERSON>", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215.021, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215.021, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0.039, -10.935], [-0.015, -0.651], [0, 0], [-0.114, 12.295], [0, 15.465], [0, 0], [-0.536, 0.563], [-0.758, 0]], "o": [[0, 0], [0, 0], [0, 15.465], [-0.039, 10.935], [0, 0], [-3.18, -0.058], [0.101, -10.935], [0, 0], [0, -0.796], [0.536, -0.563], [0, 0]], "v": [[122.974, -66.465], [122.958, 7.687], [122.958, 7.687], [123, 50.68], [122.97, 66.004], [122.963, 66.019], [119.975, 48.93], [120.064, 7.749], [120.064, -63.481], [120.902, -65.602], [122.924, -66.481]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 21, "s": [{"i": [[0, 0], [0, 0], [0, 0], [-10.424, -10.935], [-4, -1.933], [0, 0], [10.424, 10.935], [0, 15.465], [0, 0], [-0.536, 0.563], [-0.758, 0]], "o": [[0, 0], [0, 0], [0, 15.465], [10.424, 10.935], [0, 0], [-14.742, 0], [-10.424, -10.935], [0, 0], [0, -0.796], [0.536, -0.563], [0, 0]], "v": [[87.474, -66.522], [87.474, 7.708], [87.474, 7.708], [103.755, 48.939], [123.205, 66.018], [123.041, 66.018], [83.736, 48.939], [67.455, 7.708], [67.455, -63.522], [68.292, -65.644], [70.315, -66.522]], "c": true}]}, {"t": 59, "s": [{"i": [[0, 0], [0, 0], [0, 0], [-10.935, -10.935], [-15.465, 0], [0, 0], [10.935, 10.935], [0, 15.465], [0, 0], [-0.563, 0.563], [-0.796, 0]], "o": [[0, 0], [0, 0], [0, 15.465], [10.935, 10.935], [0, 0], [-15.465, 0], [-10.935, -10.935], [0, 0], [0, -0.796], [0.563, -0.563], [0, 0]], "v": [[-18.655, -66.27], [-18.655, 7.96], [-18.655, 7.96], [-1.576, 49.191], [39.655, 66.27], [18.655, 66.27], [-22.576, 49.191], [-39.655, 7.96], [-39.655, -63.27], [-38.776, -65.391], [-36.655, -66.27]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.969, 0.973, 0.976, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-821-airconditioner').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [94.045, 131.04], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 50, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 1, "cix": 2, "bm": 1, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [{"i": [[0.001, -0.46], [0.001, -0.46], [0.001, -0.58], [0.002, -0.57], [0, -0.18], [-0.04, -12.041], [3.07, -0.108], [0, 0], [-0.038, 8.713], [0.011, 13.395], [0, 0.19], [0, 0.44], [0, 0.45], [0, 0.59], [0, 0.59], [0, 0.34], [0, 1], [0, 0], [-0.536, 0.563], [-0.758, 0], [0, 0], [-0.536, -0.563], [0, -0.796], [0, 0], [0.001, -0.95], [0, -0.34]], "o": [[-0.001, 0.46], [-0.001, 0.44], [-0.001, 0.58], [-0.001, 0.18], [-0.045, 13.397], [0.026, 7.648], [0, 0], [-3.139, -0.028], [0.038, -8.713], [0, -0.18], [0, -0.57], [0, -0.44], [0, -0.45], [0, -0.59], [0, -0.32], [0, -0.95], [0, 0], [0, -0.796], [0.536, -0.563], [0, 0], [0.758, 0], [0.536, 0.563], [0, 0], [0, 0.96], [0, 0.32], [-0.001, 0.59]], "v": [[5.365, 13.45], [5.362, 14.82], [5.359, 16.09], [5.354, 17.81], [5.352, 18.35], [5.532, 52.515], [2.471, 66.022], [2.493, 66.004], [-0.499, 52.505], [-0.391, 18.344], [-0.392, 17.794], [-0.393, 16.084], [-0.394, 14.754], [-0.394, 13.394], [-0.395, 11.624], [-0.395, 10.624], [-0.396, 7.764], [-0.396, -63.466], [0.442, -65.587], [2.464, -66.466], [2.512, -66.46], [4.533, -65.581], [5.37, -63.46], [5.37, 7.82], [5.369, 10.68], [5.367, 11.68]], "c": true}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 21, "s": [{"i": [[0.048, -0.46], [0.048, -0.46], [0.076, -0.58], [0.095, -0.57], [0, -0.18], [9.977, -8.714], [12.373, 0], [0, 0], [9.983, 8.713], [2.365, 13.395], [0, 0.19], [0.067, 0.44], [0.057, 0.45], [0.057, 0.59], [0, 0.59], [0, 0.34], [0, 1], [0, 0], [-0.536, 0.563], [-0.758, 0], [0, 0], [-0.536, -0.563], [0, -0.796], [0, 0], [0.048, -0.95], [0, -0.34]], "o": [[-0.048, 0.46], [-0.048, 0.44], [-0.076, 0.58], [-0.029, 0.18], [-2.36, 13.397], [-9.977, 8.714], [0, 0], [-12.986, -0.003], [-9.983, -8.713], [-0.076, -0.18], [-0.095, -0.57], [-0.067, -0.44], [-0.057, -0.45], [-0.057, -0.59], [-0.067, -0.32], [-0.076, -0.95], [0, 0], [0, -0.796], [0.536, -0.563], [0, 0], [0.758, 0], [0.536, 0.563], [0, 0], [0, 0.96], [-0.01, 0.32], [-0.038, 0.59]], "v": [[57.238, 13.42], [57.085, 14.79], [56.914, 16.06], [56.647, 17.78], [56.561, 18.32], [37.469, 52.536], [2.55, 66.02], [2.572, 66.003], [-32.973, 52.514], [-52.08, 18.303], [-52.157, 17.753], [-52.424, 16.043], [-52.586, 14.713], [-52.738, 13.353], [-52.862, 11.583], [-52.929, 10.583], [-53.005, 7.723], [-53.005, -63.507], [-52.168, -65.629], [-50.145, -66.507], [54.637, -66.49], [56.658, -65.611], [57.495, -63.49], [57.495, 7.79], [57.428, 10.65], [57.362, 11.65]], "c": true}]}, {"t": 59, "s": [{"i": [[0.05, -0.46], [0.05, -0.46], [0.08, -0.58], [0.1, -0.57], [0, -0.18], [10.473, -8.714], [13.624, 0], [0, 0], [10.472, 8.713], [2.481, 13.395], [0, 0.19], [0.07, 0.44], [0.06, 0.45], [0.06, 0.59], [0, 0.59], [0, 0.34], [0, 1], [0, 0], [-0.563, 0.563], [-0.796, 0], [0, 0], [-0.563, -0.563], [0, -0.796], [0, 0], [0.05, -0.95], [0, -0.34]], "o": [[-0.05, 0.46], [-0.05, 0.44], [-0.08, 0.58], [-0.03, 0.18], [-2.477, 13.397], [-10.473, 8.714], [0, 0], [-13.623, -0.003], [-10.472, -8.713], [-0.08, -0.18], [-0.1, -0.57], [-0.07, -0.44], [-0.06, -0.45], [-0.06, -0.59], [-0.07, -0.32], [-0.08, -0.95], [0, 0], [0, -0.796], [0.563, -0.563], [0, 0], [0.796, 0], [0.563, 0.563], [0, 0], [0, 0.96], [-0.01, 0.32], [-0.04, 0.59]], "v": [[159.845, 13.655], [159.685, 15.025], [159.505, 16.295], [159.225, 18.015], [159.135, 18.555], [139.095, 52.771], [101.805, 66.255], [-101.815, 66.255], [-139.101, 52.767], [-159.145, 18.555], [-159.225, 18.005], [-159.505, 16.295], [-159.675, 14.965], [-159.835, 13.605], [-159.965, 11.835], [-160.035, 10.835], [-160.115, 7.975], [-160.115, -63.255], [-159.236, -65.376], [-157.115, -66.255], [157.115, -66.255], [159.236, -65.376], [160.115, -63.255], [160.115, 8.025], [160.045, 10.885], [159.975, 11.885]], "c": true}]}], "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.969, 0.973, 0.976, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-821-airconditioner').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [214.505, 131.025], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 116", "np": 1, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}]}, {"id": "comp_2", "nm": "hover-pinch", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "outline 20", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -180, "ix": 10}, "p": {"a": 0, "k": [181.935, 112.578, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-4.504, 3.225], [0, -12.655], [10.066, 0], [4.03, 3.966], [0, 12.888], [-18.767, 0], [0, -18.539], [0, 0]], "o": [[12.142, -8.695], [0, 9.944], [-5.561, 0], [-4.159, -4.092], [0, -18.539], [18.767, 0], [0, 0], [0, 0]], "v": [[-60.232, -218.655], [-35.333, -207.36], [-53.56, -189.355], [-68.168, -195.694], [-76.397, -218.353], [-42.416, -251.922], [-8.435, -218.353], [-8.444, -112.981]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 56, "s": [100]}, {"t": 102, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 50, "s": [100]}, {"t": 96, "s": [0]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-821-airconditioner').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10.5, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-lineal-821-airconditioner').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 50, "op": 104, "st": 50, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "outline 19", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -180, "ix": 10}, "p": {"a": 0, "k": [181.935, 112.578, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-4.504, 3.225], [0, -12.655], [10.066, 0], [4.03, 3.966], [0, 12.888], [-18.767, 0], [0, -18.539], [0, 0]], "o": [[12.142, -8.695], [0, 9.944], [-5.561, 0], [-4.159, -4.092], [0, -18.539], [18.767, 0], [0, 0], [0, 0]], "v": [[-60.232, -218.655], [-35.333, -207.36], [-53.56, -189.355], [-68.168, -195.694], [-76.397, -218.353], [-42.416, -251.922], [-8.435, -218.353], [-8.444, -112.981]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 23, "s": [100]}, {"t": 69, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 17, "s": [100]}, {"t": 63, "s": [0]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-821-airconditioner').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10.5, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-lineal-821-airconditioner').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 17, "op": 71, "st": 17, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "outline 21", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -180, "ix": 10}, "p": {"a": 0, "k": [181.935, 112.578, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-4.504, 3.225], [0, -12.655], [10.066, 0], [4.03, 3.966], [0, 12.888], [-18.767, 0], [0, -18.539], [0, 0]], "o": [[12.142, -8.695], [0, 9.944], [-5.561, 0], [-4.159, -4.092], [0, -18.539], [18.767, 0], [0, 0], [0, 0]], "v": [[-60.232, -218.655], [-35.333, -207.36], [-53.56, -189.355], [-68.168, -195.694], [-76.397, -218.353], [-42.416, -251.922], [-8.435, -218.353], [-8.444, -112.981]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.21], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 90, "s": [100]}, {"t": 120, "s": [25]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.21], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 90, "s": [100]}, {"t": 120, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-821-airconditioner').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10.5, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-lineal-821-airconditioner').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 90, "op": 355, "st": 90, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "outline 18", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": -180, "ix": 10}, "p": {"a": 0, "k": [181.935, 112.578, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [-100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-4.504, 3.225], [0, -12.655], [10.066, 0], [4.03, 3.966], [0, 12.888], [-18.767, 0], [0, -18.539], [0, 0]], "o": [[12.142, -8.695], [0, 9.944], [-5.561, 0], [-4.159, -4.092], [0, -18.539], [18.767, 0], [0, 0], [0, 0]], "v": [[-60.232, -218.655], [-35.333, -207.36], [-53.56, -189.355], [-68.168, -195.694], [-76.397, -218.353], [-42.416, -251.922], [-8.435, -218.353], [-8.444, -112.981]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.12], "y": [0.826]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [25]}, {"t": 30, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.12], "y": [0.908]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [100]}, {"t": 30, "s": [0]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-821-airconditioner').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10.5, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-lineal-821-airconditioner').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 31, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "outline 23", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [65.627, 198.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[66.297, 93.205], [66.239, 27.106]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-821-airconditioner').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10.5, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-lineal-821-airconditioner').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 33, "ix": 1}}, {"n": "g", "nm": "gap", "v": {"a": 0, "k": 154, "ix": 2}}, {"n": "o", "nm": "offset", "v": {"a": 1, "k": [{"i": {"x": [0.505], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [-20]}, {"t": 120, "s": [728]}], "ix": 7}}], "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "outline 24", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [147.127, 198.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[66.354, 167.705], [66.239, 27.106]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-821-airconditioner').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10.5, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-lineal-821-airconditioner').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 77, "ix": 1}}, {"n": "g", "nm": "gap", "v": {"a": 0, "k": 136, "ix": 2}}, {"n": "o", "nm": "offset", "v": {"a": 1, "k": [{"i": {"x": [0.505], "y": [1]}, "o": {"x": [0.2], "y": [0]}, "t": 0, "s": [-32.1]}, {"t": 120, "s": [820]}], "ix": 7}}], "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "outline 17", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [147.127, 198.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[106.854, 167.811], [106.739, 27.213]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-821-airconditioner').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10.5, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-lineal-821-airconditioner').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "d": [{"n": "d", "nm": "dash", "v": {"a": 0, "k": 54, "ix": 1}}, {"n": "g", "nm": "gap", "v": {"a": 0, "k": 38, "ix": 2}}, {"n": "o", "nm": "offset", "v": {"a": 1, "k": [{"i": {"x": [0.505], "y": [1]}, "o": {"x": [0.2], "y": [0]}, "t": 0, "s": [26]}, {"t": 120, "s": [854]}], "ix": 7}}], "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "outline 10", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"t": 120, "s": [180]}], "ix": 10}, "p": {"a": 0, "k": [314.73, 165.188, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [350.73, 216.688, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-19.761, 19.761], [19.761, -19.761]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-821-airconditioner').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10.5, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-lineal-821-airconditioner').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [350.73, 216.688], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-19.761, -19.761], [19.761, 19.761]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-821-airconditioner').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10.5, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-lineal-821-airconditioner').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [350.73, 216.688], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-27.947, 0], [27.947, 0]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-821-airconditioner').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10.5, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-lineal-821-airconditioner').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [350.73, 216.688], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0, 27.947], [0, -27.947]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-821-airconditioner').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10.5, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-lineal-821-airconditioner').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [350.73, 216.688], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "outline 8", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [168.105, 167.076, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-54.895, 0], [54.895, 0]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-821-airconditioner').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10.5, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-lineal-821-airconditioner').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "outline 15", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [314.73, 165.188, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -33.339], [-33.339, 0], [0, 33.339], [33.339, 0]], "o": [[0, 33.339], [33.339, 0], [0, -33.339], [-33.339, 0]], "v": [[-60.366, 0], [0, 60.366], [60.366, 0], [0, -60.366]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-821-airconditioner').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10.5, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-lineal-821-airconditioner').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "outline 13", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [187.18, 137.371, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[73.97, 0], [-73.97, 0]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-821-airconditioner').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10.5, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-lineal-821-airconditioner').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "outline 12", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [214, 131.351, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 32.21], [0, 0], [0, 0], [0, 0], [0.64, -3.44]], "o": [[0, 0], [-32.2, 0], [0, 0], [0, 0], [0, 0], [0, 3.62], [0, 0]], "v": [[49.81, 66.28], [-101.81, 66.28], [-160.12, 7.97], [-160.12, -66.28], [160.12, -66.28], [160.12, 7.97], [159.15, 18.58]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-821-airconditioner').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10.5, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-lineal-821-airconditioner').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "Vector :<PERSON>", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215.021, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215.021, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [-10.935, -10.935], [-15.465, 0], [0, 0], [10.935, 10.935], [0, 15.465], [0, 0], [-0.563, 0.563], [-0.796, 0]], "o": [[0, 0], [0, 0], [0, 15.465], [10.935, 10.935], [0, 0], [-15.465, 0], [-10.935, -10.935], [0, 0], [0, -0.796], [0.563, -0.563], [0, 0]], "v": [[-18.655, -66.27], [-18.655, 7.96], [-18.655, 7.96], [-1.576, 49.191], [39.655, 66.27], [18.655, 66.27], [-22.576, 49.191], [-39.655, 7.96], [-39.655, -63.27], [-38.776, -65.391], [-36.655, -66.27]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.969, 0.973, 0.976, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-821-airconditioner').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [94.045, 131.04], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 50, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 1, "cix": 2, "bm": 1, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0.005, 0], [-0.005, 0]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.071, 0.075, 0.192, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-821-airconditioner').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 18, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-lineal-821-airconditioner').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [113.725, 94.53], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 1, "k": [{"t": 0, "s": [100], "h": 1}, {"t": 19.092, "s": [0], "h": 1}, {"t": 30, "s": [100], "h": 1}, {"t": 49.092, "s": [0], "h": 1}, {"t": 60, "s": [100], "h": 1}, {"t": 79.092, "s": [0], "h": 1}, {"t": 90, "s": [100], "h": 1}, {"t": 109.092, "s": [0], "h": 1}, {"t": 120, "s": [100], "h": 1}], "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 2", "np": 1, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-33.341, 0], [-2.457, 0.304], [0, 30.802], [-29.802, 3.693], [2.54, 0], [0, -33.341]], "o": [[2.54, 0], [-29.802, -3.693], [0, -30.802], [-2.457, -0.304], [-33.341, 0], [0, 33.341]], "v": [[26.435, 60.37], [33.935, 59.909], [-18.935, 0], [33.935, -59.909], [26.435, -60.37], [-33.935, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188, 0.137, 0.682, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-821-airconditioner').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Subtract", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [288.805, 164.9], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 50, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Subtract", "np": 1, "cix": 2, "bm": 1, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-33.341, 0], [0, 33.341], [33.341, 0], [0, -33.341]], "o": [[33.341, 0], [0, -33.341], [-33.341, 0], [0, 33.341]], "v": [[0, 60.37], [60.37, 0], [0, -60.37], [-60.37, 0]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188, 0.137, 0.682, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-821-airconditioner').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [315.24, 164.9], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 7", "np": 1, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.05, -0.46], [0.05, -0.46], [0.08, -0.58], [0.1, -0.57], [0, -0.18], [10.473, -8.714], [13.624, 0], [0, 0], [10.472, 8.713], [2.481, 13.395], [0, 0.19], [0.07, 0.44], [0.06, 0.45], [0.06, 0.59], [0, 0.59], [0, 0.34], [0, 1], [0, 0], [-0.563, 0.563], [-0.796, 0], [0, 0], [-0.563, -0.563], [0, -0.796], [0, 0], [0.05, -0.95], [0, -0.34]], "o": [[-0.05, 0.46], [-0.05, 0.44], [-0.08, 0.58], [-0.03, 0.18], [-2.477, 13.397], [-10.473, 8.714], [0, 0], [-13.623, -0.003], [-10.472, -8.713], [-0.08, -0.18], [-0.1, -0.57], [-0.07, -0.44], [-0.06, -0.45], [-0.06, -0.59], [-0.07, -0.32], [-0.08, -0.95], [0, 0], [0, -0.796], [0.563, -0.563], [0, 0], [0.796, 0], [0.563, 0.563], [0, 0], [0, 0.96], [-0.01, 0.32], [-0.04, 0.59]], "v": [[159.845, 13.655], [159.685, 15.025], [159.505, 16.295], [159.225, 18.015], [159.135, 18.555], [139.095, 52.771], [101.805, 66.255], [-101.815, 66.255], [-139.101, 52.767], [-159.145, 18.555], [-159.225, 18.005], [-159.505, 16.295], [-159.675, 14.965], [-159.835, 13.605], [-159.965, 11.835], [-160.035, 10.835], [-160.115, 7.975], [-160.115, -63.255], [-159.236, -65.376], [-157.115, -66.255], [157.115, -66.255], [159.236, -65.376], [160.115, -63.255], [160.115, 8.025], [160.045, 10.885], [159.975, 11.885]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.969, 0.973, 0.976, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-821-airconditioner').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [214.505, 131.025], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 116", "np": 1, "cix": 2, "bm": 0, "ix": 5, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "ct": 1, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "control", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "stroke", "np": 3, "mn": "Pseudo/@@rkS8ml/UT7+Q2RFzug004w", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "<PERSON><PERSON>", "mn": "Pseudo/@@rkS8ml/UT7+Q2RFzug004w-0001", "ix": 1, "v": {"a": 0, "k": 3, "ix": 1}}]}, {"ty": 5, "nm": "primary", "np": 3, "mn": "ADBE Color Control", "ix": 2, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.071, 0.075, 0.192], "ix": 1}}]}, {"ty": 5, "nm": "secondary", "np": 3, "mn": "ADBE Color Control", "ix": 3, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.969, 0.973, 0.976], "ix": 1}}]}, {"ty": 5, "nm": "tertiary", "np": 3, "mn": "ADBE Color Control", "ix": 4, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.188, 0.137, 0.682], "ix": 1}}]}], "ip": 0, "op": 191, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "hover-pinch", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 0, "op": 130, "st": 0, "bm": 0}], "markers": [{"tm": 0, "cm": "default:hover-pinch", "dr": 120}], "props": {}}