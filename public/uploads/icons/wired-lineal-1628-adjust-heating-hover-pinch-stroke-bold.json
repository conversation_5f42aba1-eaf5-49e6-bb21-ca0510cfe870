{"v": "5.12.1", "fr": 60, "ip": 0, "op": 120, "w": 430, "h": 430, "nm": "wired-lineal-1628-adjust-heating", "ddd": 0, "assets": [{"id": "comp_1", "nm": "hover-pinch", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "gear", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.089], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.4], "y": [1]}, "o": {"x": [0.6], "y": [0]}, "t": 60, "s": [-120]}, {"t": 120, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [315, 327.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[4.122, 2.755], [4.958, 0], [4.701, -4.701], [0.003, -6.648], [-2.755, -4.122], [-4.581, -1.897], [-4.863, 0.967], [-3.506, 3.506], [-0.967, 4.863], [1.897, 4.581]], "o": [[-4.122, -2.755], [-6.648, 0.003], [-4.701, 4.701], [0, 4.958], [2.755, 4.122], [4.581, 1.897], [4.863, -0.967], [3.506, -3.506], [0.967, -4.863], [-1.897, -4.581]], "v": [[13.944, -20.821], [0.017, -25.046], [-17.706, -17.7], [-25.052, 0.023], [-20.827, 13.95], [-9.577, 23.183], [4.907, 24.609], [17.743, 17.748], [24.603, 4.913], [23.177, -9.571]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0.693, -4.833], [0, 0], [-0.626, -2.289], [1.161, -2.07], [0, 0], [2.299, -0.618], [2.069, 1.178], [0, 0], [4.526, -1.832], [0, 0], [1.695, -1.695], [2.397, 0], [0, 0], [1.695, 1.695], [0, 2.397], [0, 0], [3.847, 3.007], [0, 0], [2.302, 0.618], [1.2, 2.06], [0, 0], [-0.616, 2.299], [-2.055, 1.202], [0, 0], [-0.678, 4.834], [0, 0], [0.616, 2.299], [-1.179, 2.067], [0, 0], [-2.302, 0.616], [-2.068, -1.183], [0, 0], [-4.527, 1.83], [0, 0], [-1.693, 1.683], [-2.387, 0], [0, 0], [-1.693, -1.683], [-0.015, -2.387], [0, 0], [-3.826, -3.026], [0, 0], [-2.31, -0.616], [-1.202, -2.067], [0, 0], [0.618, -2.299], [2.056, -1.2], [0, 0]], "o": [[0, 0], [2.052, 1.192], [0.626, 2.289], [0, 0], [-1.2, 2.056], [-2.299, 0.618], [0, 0], [-3.845, 3.01], [0, 0], [0, 2.397], [-1.695, 1.695], [0, 0], [-2.397, 0], [-1.695, -1.695], [0, 0], [-4.525, -1.834], [0, 0], [-2.07, 1.182], [-2.302, -0.618], [0, 0], [-1.179, -2.067], [0.616, -2.299], [0, 0], [-0.678, -4.834], [0, 0], [-2.055, -1.202], [-0.616, -2.299], [0, 0], [1.202, -2.058], [2.302, -0.616], [0, 0], [3.844, -3.011], [0, 0], [0.015, -2.387], [1.693, -1.683], [0, 0], [2.387, 0], [1.693, 1.683], [0, 0], [4.515, 1.845], [0, 0], [2.071, -1.194], [2.31, 0.616], [0, 0], [1.178, 2.069], [-0.618, 2.299], [0, 0], [0.693, 4.833]], "v": [[51.983, 7.309], [60.501, 12.235], [64.68, 17.667], [63.845, 24.469], [53.158, 42.963], [47.696, 47.137], [40.878, 46.262], [32.349, 41.336], [19.73, 48.634], [19.73, 58.462], [17.083, 64.853], [10.692, 67.5], [-10.67, 67.5], [-17.061, 64.853], [-19.708, 58.462], [-19.708, 48.634], [-32.327, 41.336], [-40.845, 46.262], [-47.67, 47.142], [-53.136, 42.963], [-63.812, 24.469], [-64.692, 17.654], [-60.524, 12.19], [-52.006, 7.264], [-52.006, -7.309], [-60.524, -12.235], [-64.692, -17.699], [-63.812, -24.515], [-53.136, -43.019], [-47.667, -47.192], [-40.845, -46.307], [-32.327, -41.381], [-19.708, -48.679], [-19.708, -58.519], [-17.041, -64.873], [-10.67, -67.5], [10.647, -67.5], [17.018, -64.873], [19.685, -58.519], [19.685, -48.679], [32.258, -41.336], [40.788, -46.261], [47.629, -47.163], [53.113, -42.974], [63.8, -24.469], [64.675, -17.651], [60.501, -12.19], [51.983, -7.264]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.2, 0.2, 0.2, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1628-adjust-heating').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10.5, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-lineal-1628-adjust-heating').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "gear", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "mask", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.089], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.4], "y": [1]}, "o": {"x": [0.6], "y": [0]}, "t": 60, "s": [-120]}, {"t": 120, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [315, 327.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[4.122, 2.755], [4.958, 0], [4.701, -4.701], [0.003, -6.648], [-2.755, -4.122], [-4.581, -1.897], [-4.863, 0.967], [-3.506, 3.506], [-0.967, 4.863], [1.897, 4.581]], "o": [[-4.122, -2.755], [-6.648, 0.003], [-4.701, 4.701], [0, 4.958], [2.755, 4.122], [4.581, 1.897], [4.863, -0.967], [3.506, -3.506], [0.967, -4.863], [-1.897, -4.581]], "v": [[13.944, -20.821], [0.017, -25.046], [-17.706, -17.7], [-25.052, 0.023], [-20.827, 13.95], [-9.577, 23.183], [4.907, 24.609], [17.743, 17.748], [24.603, 4.913], [23.177, -9.571]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0.693, -4.833], [0, 0], [-0.626, -2.289], [1.161, -2.07], [0, 0], [2.299, -0.618], [2.069, 1.178], [0, 0], [4.526, -1.832], [0, 0], [1.695, -1.695], [2.397, 0], [0, 0], [1.695, 1.695], [0, 2.397], [0, 0], [3.847, 3.007], [0, 0], [2.302, 0.618], [1.2, 2.06], [0, 0], [-0.616, 2.299], [-2.055, 1.202], [0, 0], [-0.678, 4.834], [0, 0], [0.616, 2.299], [-1.179, 2.067], [0, 0], [-2.302, 0.616], [-2.068, -1.183], [0, 0], [-4.527, 1.83], [0, 0], [-1.693, 1.683], [-2.387, 0], [0, 0], [-1.693, -1.683], [-0.015, -2.387], [0, 0], [-3.826, -3.026], [0, 0], [-2.31, -0.616], [-1.202, -2.067], [0, 0], [0.618, -2.299], [2.056, -1.2], [0, 0]], "o": [[0, 0], [2.052, 1.192], [0.626, 2.289], [0, 0], [-1.2, 2.056], [-2.299, 0.618], [0, 0], [-3.845, 3.01], [0, 0], [0, 2.397], [-1.695, 1.695], [0, 0], [-2.397, 0], [-1.695, -1.695], [0, 0], [-4.525, -1.834], [0, 0], [-2.07, 1.182], [-2.302, -0.618], [0, 0], [-1.179, -2.067], [0.616, -2.299], [0, 0], [-0.678, -4.834], [0, 0], [-2.055, -1.202], [-0.616, -2.299], [0, 0], [1.202, -2.058], [2.302, -0.616], [0, 0], [3.844, -3.011], [0, 0], [0.015, -2.387], [1.693, -1.683], [0, 0], [2.387, 0], [1.693, 1.683], [0, 0], [4.515, 1.845], [0, 0], [2.071, -1.194], [2.31, 0.616], [0, 0], [1.178, 2.069], [-0.618, 2.299], [0, 0], [0.693, 4.833]], "v": [[51.983, 7.309], [60.501, 12.235], [64.68, 17.667], [63.845, 24.469], [53.158, 42.963], [47.696, 47.137], [40.878, 46.262], [32.349, 41.336], [19.73, 48.634], [19.73, 58.462], [17.083, 64.853], [10.692, 67.5], [-10.67, 67.5], [-17.061, 64.853], [-19.708, 58.462], [-19.708, 48.634], [-32.327, 41.336], [-40.845, 46.262], [-47.67, 47.142], [-53.136, 42.963], [-63.812, 24.469], [-64.692, 17.654], [-60.524, 12.19], [-52.006, 7.264], [-52.006, -7.309], [-60.524, -12.235], [-64.692, -17.699], [-63.812, -24.515], [-53.136, -43.019], [-47.667, -47.192], [-40.845, -46.307], [-32.327, -41.381], [-19.708, -48.679], [-19.708, -58.519], [-17.041, -64.873], [-10.67, -67.5], [10.647, -67.5], [17.018, -64.873], [19.685, -58.519], [19.685, -48.679], [32.258, -41.336], [40.788, -46.261], [47.629, -47.163], [53.113, -42.974], [63.8, -24.469], [64.675, -17.651], [60.501, -12.19], [51.983, -7.264]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "gear", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0, 0, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}], "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Shape Layer 2", "tt": 1, "tp": 2, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [150.125, 273, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[53.125, -76.5], [53.125, 95.5], [-52.875, 95.5], [-52.875, -76.5]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188, 0.137, 0.682, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1628-adjust-heating').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [112, 46.5], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 50, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Rectangle 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 1}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "gear 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.089], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.4], "y": [1]}, "o": {"x": [0.6], "y": [0]}, "t": 60, "s": [-120]}, {"t": 120, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [315, 327.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[4.122, 2.755], [4.958, 0], [4.701, -4.701], [0.003, -6.648], [-2.755, -4.122], [-4.581, -1.897], [-4.863, 0.967], [-3.506, 3.506], [-0.967, 4.863], [1.897, 4.581]], "o": [[-4.122, -2.755], [-6.648, 0.003], [-4.701, 4.701], [0, 4.958], [2.755, 4.122], [4.581, 1.897], [4.863, -0.967], [3.506, -3.506], [0.967, -4.863], [-1.897, -4.581]], "v": [[13.944, -20.821], [0.017, -25.046], [-17.706, -17.7], [-25.052, 0.023], [-20.827, 13.95], [-9.577, 23.183], [4.907, 24.609], [17.743, 17.748], [24.603, 4.913], [23.177, -9.571]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0.693, -4.833], [0, 0], [-0.626, -2.289], [1.161, -2.07], [0, 0], [2.299, -0.618], [2.069, 1.178], [0, 0], [4.526, -1.832], [0, 0], [1.695, -1.695], [2.397, 0], [0, 0], [1.695, 1.695], [0, 2.397], [0, 0], [3.847, 3.007], [0, 0], [2.302, 0.618], [1.2, 2.06], [0, 0], [-0.616, 2.299], [-2.055, 1.202], [0, 0], [-0.678, 4.834], [0, 0], [0.616, 2.299], [-1.179, 2.067], [0, 0], [-2.302, 0.616], [-2.068, -1.183], [0, 0], [-4.527, 1.83], [0, 0], [-1.693, 1.683], [-2.387, 0], [0, 0], [-1.693, -1.683], [-0.015, -2.387], [0, 0], [-3.826, -3.026], [0, 0], [-2.31, -0.616], [-1.202, -2.067], [0, 0], [0.618, -2.299], [2.056, -1.2], [0, 0]], "o": [[0, 0], [2.052, 1.192], [0.626, 2.289], [0, 0], [-1.2, 2.056], [-2.299, 0.618], [0, 0], [-3.845, 3.01], [0, 0], [0, 2.397], [-1.695, 1.695], [0, 0], [-2.397, 0], [-1.695, -1.695], [0, 0], [-4.525, -1.834], [0, 0], [-2.07, 1.182], [-2.302, -0.618], [0, 0], [-1.179, -2.067], [0.616, -2.299], [0, 0], [-0.678, -4.834], [0, 0], [-2.055, -1.202], [-0.616, -2.299], [0, 0], [1.202, -2.058], [2.302, -0.616], [0, 0], [3.844, -3.011], [0, 0], [0.015, -2.387], [1.693, -1.683], [0, 0], [2.387, 0], [1.693, 1.683], [0, 0], [4.515, 1.845], [0, 0], [2.071, -1.194], [2.31, 0.616], [0, 0], [1.178, 2.069], [-0.618, 2.299], [0, 0], [0.693, 4.833]], "v": [[51.983, 7.309], [60.501, 12.235], [64.68, 17.667], [63.845, 24.469], [53.158, 42.963], [47.696, 47.137], [40.878, 46.262], [32.349, 41.336], [19.73, 48.634], [19.73, 58.462], [17.083, 64.853], [10.692, 67.5], [-10.67, 67.5], [-17.061, 64.853], [-19.708, 58.462], [-19.708, 48.634], [-32.327, 41.336], [-40.845, 46.262], [-47.67, 47.142], [-53.136, 42.963], [-63.812, 24.469], [-64.692, 17.654], [-60.524, 12.19], [-52.006, 7.264], [-52.006, -7.309], [-60.524, -12.235], [-64.692, -17.699], [-63.812, -24.515], [-53.136, -43.019], [-47.667, -47.192], [-40.845, -46.307], [-32.327, -41.381], [-19.708, -48.679], [-19.708, -58.519], [-17.041, -64.873], [-10.67, -67.5], [10.647, -67.5], [17.018, -64.873], [19.685, -58.519], [19.685, -48.679], [32.258, -41.336], [40.788, -46.261], [47.629, -47.163], [53.113, -42.974], [63.8, -24.469], [64.675, -17.651], [60.501, -12.19], [51.983, -7.264]], "c": true}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "gear", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188, 0.137, 0.682, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1628-adjust-heating').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}], "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Vector 9", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [205.5, 260.15, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 20.3], [0, 20.2], [0, 20.2]], "o": [[0, -20.3], [0, -20.3], [0, -20.3], [0, 0]], "v": [[40.175, 60.75], [56.175, 20.25], [40.175, -20.25], [56.175, -60.75]], "c": false}, "ix": 2}, "nm": "Path 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.089], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [33]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.6], "y": [0]}, "t": 60, "s": [33]}, {"t": 120, "s": [33]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.089], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [100]}, {"i": {"x": [0.4], "y": [1]}, "o": {"x": [0.6], "y": [0]}, "t": 60, "s": [49]}, {"t": 120, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.2, 0.2, 0.2, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1628-adjust-heating').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10.5, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-lineal-1628-adjust-heating').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector 2", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 20.3], [0, 20.2], [0, 20.2]], "o": [[0, -20.3], [0, -20.3], [0, -20.3], [0, 0]], "v": [[-8, 60.75], [8, 20.25], [-8, -20.25], [8, -60.75]], "c": false}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 20.3], [0, 20.2], [0, 20.2]], "o": [[0, -20.3], [0, -20.3], [0, -20.3], [0, 0]], "v": [[-56.2, 60.75], [-40.2, 20.25], [-56.2, -20.25], [-40.2, -60.75]], "c": false}, "ix": 2}, "nm": "Path 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.089], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.4], "y": [1]}, "o": {"x": [0.6], "y": [0]}, "t": 60, "s": [0]}, {"t": 120, "s": [0]}], "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.089], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [100]}, {"i": {"x": [0.4], "y": [1]}, "o": {"x": [0.6], "y": [0]}, "t": 60, "s": [49]}, {"t": 120, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 4, "nm": "Trim Paths 1", "mn": "ADBE Vector Filter - Trim", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.2, 0.2, 0.2, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1628-adjust-heating').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10.5, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-lineal-1628-adjust-heating').layer('control').effect('stroke')('Menu'));"}, "lc": 2, "lj": 2, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Vector", "np": 5, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "roof", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [205.625, 209.514, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [250, 244.514, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.227, 1.114], [0, 0], [1.144, -1.038], [0, 0], [1.113, 1.227], [0, 0], [-1.227, 1.113], [0, 0], [-4.765, -4.325], [0, 0], [1.114, -1.227], [0, 0]], "o": [[0, 0], [-1.144, -1.038], [0, 0], [-1.227, 1.113], [0, 0], [-1.113, -1.227], [0, 0], [4.767, -4.325], [0, 0], [1.227, 1.114], [0, 0], [-1.114, 1.227]], "v": [[150.054, 82.396], [2.037, -51.965], [-1.995, -51.966], [-150.057, 82.368], [-154.295, 82.162], [-167.062, 68.09], [-166.856, 63.852], [-8.376, -79.932], [8.424, -79.93], [166.856, 63.886], [167.061, 68.123], [154.291, 82.191]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.2, 0.2, 0.2, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1628-adjust-heating').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10.5, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-lineal-1628-adjust-heating').layer('control').effect('stroke')('Menu'));"}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "fl", "c": {"a": 0, "k": [0.753, 0.141, 0.714, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1628-adjust-heating').layer('control').effect('tertiary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".tertiary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "tertiary"}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [250, 161.923], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 1", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "mask", "td": 1, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.089], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.4], "y": [1]}, "o": {"x": [0.6], "y": [0]}, "t": 60, "s": [-120]}, {"t": 120, "s": [0]}], "ix": 10}, "p": {"a": 0, "k": [315, 327.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[5.138, 3.433], [6.179, -0.003], [5.858, -5.858], [0, -8.285], [-3.433, -5.138], [-5.709, -2.365], [-6.06, 1.205], [-4.369, 4.369], [-1.205, 6.06], [2.365, 5.709]], "o": [[-5.138, -3.433], [-8.285, 0.004], [-5.858, 5.858], [0, 6.179], [3.433, 5.138], [5.709, 2.365], [6.06, -1.205], [4.369, -4.369], [1.205, -6.06], [-2.365, -5.709]], "v": [[17.314, -26.014], [-0.043, -31.279], [-22.13, -22.124], [-31.285, -0.037], [-26.02, 17.32], [-11.999, 28.826], [6.052, 30.604], [22.048, 22.054], [30.598, 6.058], [28.82, -11.993]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "Merge Paths 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "gear", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.227450981736, 0.20000000298, 0.278431385756, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "primary"}], "ip": 0, "op": 1800, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "fill 2", "tt": 2, "tp": 7, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [205.625, 209.514, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [250, 244.514, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -1.657], [0, 0], [0, 0], [0, 0], [-1.657, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, -1.657], [0, 0], [1.657, 0]], "v": [[23.405, -37.88], [23.405, 40.88], [-23.405, -3.16], [-23.405, -37.88], [-20.405, -40.88], [20.405, -40.88]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.2, 0.2, 0.2, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1628-adjust-heating').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10.5, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-lineal-1628-adjust-heating').layer('control').effect('stroke')('Menu'));"}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [329.987, 145.993], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [1.657, 0], [0, 0], [0, 1.657], [0, 0], [0, 0], [0, 0], [0, 0], [-1.657, 0], [0, 0], [0, -1.657], [0, 0]], "o": [[0, 0], [0, 1.657], [0, 0], [-1.657, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -1.657], [0, 0], [1.657, 0], [0, 0], [0, 0]], "v": [[132.685, -35.5], [132.685, 157.28], [129.685, 160.28], [-129.685, 160.28], [-132.685, 157.28], [-132.685, -35.44], [0.055, -160.28], [56.565, -107.12], [56.565, -141.84], [59.565, -144.84], [100.375, -144.84], [103.375, -141.84], [103.375, -63.08]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.2, 0.2, 0.2, 1], "ix": 3, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1628-adjust-heating').layer('control').effect('primary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 10.5, "ix": 5, "x": "var $bm_rt;\n$bm_rt = $bm_mul($bm_div(value, 3), comp('wired-lineal-1628-adjust-heating').layer('control').effect('stroke')('Menu'));"}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": ".primary", "mn": "ADBE Vector Graphic - Stroke", "hd": false, "cl": "primary"}, {"ty": "tr", "p": {"a": 0, "k": [250, 250], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 3, "cix": 2, "bm": 0, "ix": 2, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "fill", "tt": 2, "tp": 7, "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [205.625, 209.514, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [250, 244.514, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, -0.854], [0, 0], [0, 0], [0, 1.657], [0, 0], [0, 0]], "o": [[0, 0], [-0.636, 0.569], [0, 0], [0, 0], [-1.657, 0], [0, 0], [0, 0], [0, 0]], "v": [[81.931, -130.872], [-47.953, -14.684], [-48.953, -12.448], [-48.953, 160.28], [-78.931, 160.28], [-81.931, 157.28], [-81.931, -35.44], [50.809, -160.28]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.969, 0.973, 0.976, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1628-adjust-heating').layer('control').effect('quaternary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".quaternary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "quaternary"}, {"ty": "tr", "p": {"a": 0, "k": [199.246, 250], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 50, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 2", "np": 2, "cix": 2, "bm": 1, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [-1.657, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, -1.657], [0, 0]], "v": [[5.275, -23.825], [5.275, 23.825], [-5.275, 13.895], [-5.275, -20.825], [-2.275, -23.825]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188, 0.137, 0.682, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1628-adjust-heating').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [311.857, 128.935], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 50, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 3", "np": 2, "cix": 2, "bm": 1, "ix": 2, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, -1.657], [0, 0], [0, 0], [0, 0], [-1.657, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, -1.657], [0, 0], [1.657, 0]], "v": [[23.405, -37.88], [23.405, 40.88], [-23.405, -3.16], [-23.405, -37.88], [-20.405, -40.88], [20.405, -40.88]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.188, 0.137, 0.682, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1628-adjust-heating').layer('control').effect('secondary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".secondary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "secondary"}, {"ty": "tr", "p": {"a": 0, "k": [329.987, 145.993], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 4", "np": 2, "cix": 2, "bm": 0, "ix": 3, "mn": "ADBE Vector Group", "hd": false}, {"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [1.657, 0], [0, 0], [0, 1.657], [0, 0], [0, 0], [0, 0], [0, 0], [-1.657, 0], [0, 0], [0, -1.657], [0, 0]], "o": [[0, 0], [0, 1.657], [0, 0], [-1.657, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, -1.657], [0, 0], [1.657, 0], [0, 0], [0, 0]], "v": [[132.685, -35.5], [132.685, 157.28], [129.685, 160.28], [-129.685, 160.28], [-132.685, 157.28], [-132.685, -35.44], [0.055, -160.28], [56.565, -107.12], [56.565, -141.84], [59.565, -144.84], [100.375, -144.84], [103.375, -141.84], [103.375, -63.08]], "c": true}, "ix": 2}, "nm": "Path 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.969, 0.973, 0.976, 1], "ix": 4, "x": "var $bm_rt;\n$bm_rt = comp('wired-lineal-1628-adjust-heating').layer('control').effect('quaternary')('Color');"}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": ".quaternary", "mn": "ADBE Vector Graphic - Fill", "hd": false, "cl": "quaternary"}, {"ty": "tr", "p": {"a": 0, "k": [250, 250], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Group 5", "np": 2, "cix": 2, "bm": 0, "ix": 4, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 844, "st": 0, "ct": 1, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 3, "nm": "control", "sr": 1, "ks": {"o": {"a": 0, "k": 0, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [0, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ef": [{"ty": 5, "nm": "stroke", "np": 3, "mn": "Pseudo/@@ZuSZlR15TM2u7Fql+4R+tw", "ix": 1, "en": 1, "ef": [{"ty": 7, "nm": "<PERSON><PERSON>", "mn": "Pseudo/@@ZuSZlR15TM2u7Fql+4R+tw-0001", "ix": 1, "v": {"a": 0, "k": 3, "ix": 1}}]}, {"ty": 5, "nm": "primary", "np": 3, "mn": "ADBE Color Control", "ix": 2, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.2, 0.2, 0.2], "ix": 1}}]}, {"ty": 5, "nm": "secondary", "np": 3, "mn": "ADBE Color Control", "ix": 3, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.188, 0.137, 0.682], "ix": 1}}]}, {"ty": 5, "nm": "tertiary", "np": 3, "mn": "ADBE Color Control", "ix": 4, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.753, 0.141, 0.714], "ix": 1}}]}, {"ty": 5, "nm": "quaternary", "np": 3, "mn": "ADBE Color Control", "ix": 5, "en": 1, "ef": [{"ty": 2, "nm": "Color", "mn": "ADBE Color Control-0001", "ix": 1, "v": {"a": 0, "k": [0.969, 0.973, 0.976], "ix": 1}}]}], "ip": 0, "op": 261, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 0, "nm": "hover-pinch", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [215, 215, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [215, 215, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 430, "h": 430, "ip": 0, "op": 130, "st": 0, "bm": 0}], "markers": [{"tm": 0, "cm": "default:hover-pinch", "dr": 120}], "props": {}}