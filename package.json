{"name": "allocvc", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@floating-ui/react": "^0.27.5", "@hookform/resolvers": "^4.1.3", "@lordicon/react": "^1.10.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-toast": "^1.2.4", "@stripe/react-stripe-js": "^3.1.1", "@stripe/stripe-js": "^5.6.0", "@types/express": "^5.0.0", "class-variance-authority": "^0.7.0", "cmdk": "^1.0.4", "cookies-next": "^5.1.0", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "embla-carousel": "^8.5.2", "embla-carousel-autoplay": "^8.5.2", "embla-carousel-react": "^8.5.2", "firebase": "^11.5.0", "firebase-admin": "^13.2.0", "firebase-functions": "^6.3.2", "firebase-tools": "^14.0.1", "form-data": "^4.0.2", "googleapis": "^148.0.0", "js-cookie": "^3.0.5", "lottie-web": "^5.12.2", "lucide-react": "^0.453.0", "luxon": "^3.7.1", "mailgun.js": "^12.0.1", "next": "^15.2.4", "ngrok": "^5.0.0-beta.2", "node-fetch": "^3.3.2", "prettier": "^3.5.3", "punycode": "^2.3.1", "radix-ui": "^1.1.3", "react": "^19.1.0", "react-cookie-consent": "^9.0.0", "react-day-picker": "^9.6.4", "react-dom": "^19.1.0", "react-hook-form": "^7.55.0", "react-icons": "^5.5.0", "sonner": "^2.0.2", "stripe": "^17.7.0", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.2", "@types/js-cookie": "^3.0.6", "@types/luxon": "^3.6.2", "@types/mailgun-js": "^0.22.18", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/eslint-plugin": "^8.28.0", "eslint": "^9.23.0", "eslint-config-next": "15.2.4", "eslint-config-prettier": "^10.1.1", "postcss": "^8.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.0.17", "typescript": "^5"}}