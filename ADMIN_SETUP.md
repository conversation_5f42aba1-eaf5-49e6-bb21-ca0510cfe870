# Admin Panel Setup Guide

## Overview
The admin panel has been set up at `/admin` route with authentication protection. Only users with `userType: "admin"` in Firestore can access the admin panel.

## Creating Your First Admin User

Since you need an admin user to access the admin panel, you'll need to manually promote a user to admin status in Firestore.

### Method 1: Using Firebase Console (Recommended)

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your `allocvc` project
3. Navigate to Firestore Database
4. Find the `users` collection
5. Locate the user document you want to promote to admin
6. Edit the document and change the `userType` field from `"client"` to `"admin"`
7. Save the changes

### Method 2: Using Browser Console (For Development)

1. First, create a regular user account on your site
2. Open browser developer tools (F12)
3. Go to Console tab
4. Run this code (replace `USER_ID` with the actual user ID):

```javascript
// Import Firebase functions
import { doc, updateDoc } from 'firebase/firestore';
import { db } from './src/lib/firebaseConfig';

// Promote user to admin
const userId = 'USER_ID_HERE'; // Replace with actual user ID
const userRef = doc(db, 'users', userId);
await updateDoc(userRef, {
  userType: 'admin'
});
console.log('User promoted to admin');
```

### Method 3: Using the Admin Utils (After you have one admin)

Once you have at least one admin user, you can use the utility functions:

```typescript
import { promoteUserToAdmin } from '@/lib/adminUtils';

const result = await promoteUserToAdmin('USER_ID_HERE');
console.log(result.message);
```

## Accessing the Admin Panel

1. Make sure you're signed in as an admin user
2. Navigate to `http://localhost:3000/admin` (or your domain + `/admin`)
3. You should see the admin dashboard

## Admin Panel Features

Currently implemented:
- ✅ Admin authentication and route protection
- ✅ Basic admin dashboard with placeholder stats
- ✅ Admin navigation header
- ✅ Sign out functionality

Planned features:
- 📋 User management
- 📋 Intervention management
- 📋 Analytics and reporting
- 📋 Settings management

## File Structure

```
src/app/admin/
├── layout.tsx          # Admin layout with navigation
├── page.tsx           # Admin dashboard
└── [future pages]     # Users, interventions, etc.

src/lib/
├── adminUtils.ts      # Admin utility functions

src/components/
├── AdminProtection.tsx # Admin route protection component
```

## Security Notes

- Admin routes are protected by authentication and role checking
- Users must be signed in AND have `userType: "admin"` to access admin pages
- Non-admin users are automatically redirected to the main site
- Admin status is checked on every page load for security

## Next Steps

1. Create your first admin user using Method 1 above
2. Test the admin panel access
3. Start building additional admin features as needed

## Troubleshooting

**Can't access admin panel:**
- Verify you're signed in
- Check that your user document in Firestore has `userType: "admin"`
- Check browser console for any errors

**Getting redirected to main site:**
- This means either you're not signed in or don't have admin privileges
- Follow the steps above to promote your user to admin
