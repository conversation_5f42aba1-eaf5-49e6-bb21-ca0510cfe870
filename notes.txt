### AlloCVC TO-DO LIST ###

__BRETT__
[ ] Mise à jour des politiques et des légales pour qu'elles s'affichent (popup) au lieu de pages séparées
[ ] Les références du footer de page ne fonctionnent pas sur les pages non principales.
[ ] User profile menu settings fix to be carried out
[ ] Favicon ne s'affiche pas sur quelques browsers
[ ] Jours feriés à ajouter
[ ] Recherche de panne clim prix fixe

[ ] Bouton de retour sur la section horaires (créneau)
[ ] Particulier ou société dans identifier (nom de société)

__SKANDER__
[ ] Détails de clim manquant dans l'email de Racim
[/] Prix avec promo historique (+ afficher all historique + bouton facture)
[ ] Dans Stripe, le même client se répète à chaque fois ?
[ ] Confirmer les changements d'email avec un email de vérification (sendEmailVerification)
[ ] Lorsque l'utilisateur change de mot de passe, déconnexion de l'utilisateur

[ ] Société mentionné dans facture


A DEFINIR
[ ] L'utilisateur ne doit pas procéder au paiement s'il est déconnecté à l'étape Créneau
[ ] Ajouter les options de hauteur de mur lors du choix des types de clim


[x] www.allocvc.fr not linked to website
[x] Bannière pas responsive sur mobile (trop grand)
[x] Reload on creaneau and stages after break the app
[x] Code promo bannière IMPORTANT
[x] Adresse du domicile pure firestore
[x] Stripe live platform set up for master branch
[x] Tableau de factures dans user
[x] Stripe live platform - code promo and tax
[x] Mention légales et politiques pop ups
[x] <EMAIL> pour tous les reservations
[x] Intervention recap email with all details possible - <EMAIL>
[x] Calendar price does not show correctly in recap (weekend price)
[x] Remove retour from timeline
[x] Code promo option. #NOUVEAUCLIENT
[x] Taxe de 20% (TVA)
[x] Stocker facture id dans firestore (user - tableau)
[x] Limiter les réservations à une personne par créneau
[x] Champs telephone soit stripe soit identifier
[x] Activer les cookies (accepter, refuser, gérer les options)
[x] Politique de confidentialité - https://www.allo-cvc.fr/cookies
[x] Condition de checker si la demande était déjà fait pour webhook
[x] Personnaliser les factures